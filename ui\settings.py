# -*- coding: utf-8 -*-
"""
قسم الإعدادات المتقدم
Advanced Settings Section
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QLabel, QLineEdit, QPushButton, QCheckBox, QSpinBox, QDoubleSpinBox,
                            QComboBox, QTextEdit, QGroupBox, QGridLayout,
                            QMessageBox, QProgressBar, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QScrollArea, QSlider, QColorDialog,
                            QSizePolicy, QDialog, QFormLayout, QDialogButtonBox, QFileDialog)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette, Q<PERSON>ixmap, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QBrush, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QIcon

# إضافة المسار للأنظمة المتقدمة
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

try:
    from config import AppConfig, DatabaseConfig, SecurityConfig, BackupConfig, PerformanceConfig
    from logging_config import smart_logger, log_info, log_error, log_warning
    BASIC_SYSTEMS_AVAILABLE = True
except ImportError:
    BASIC_SYSTEMS_AVAILABLE = False

try:
    from backup_manager import backup_manager
    from permissions_manager import permissions_manager
    from security_manager import security_manager
    from performance_monitor import performance_monitor
    from system_initializer import get_systems_status
    ADVANCED_SYSTEMS_AVAILABLE = True
except ImportError:
    ADVANCED_SYSTEMS_AVAILABLE = False

class SettingsConfirmationDialog(QDialog):
    """نافذة تأكيد متطورة لإعدادات النظام مطابقة لنوافذ الأقساط"""

    def __init__(self, parent=None, title="تأكيد العملية", message="هل أنت متأكد؟",
                 icon="⚠️", confirm_text="موافق", cancel_text="إلغاء", operation_type="warning"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.icon = icon
        self.confirm_text = confirm_text
        self.cancel_text = cancel_text
        self.operation_type = operation_type
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنوافذ الأقساط"""
        self.setWindowTitle(f"{self.icon} {self.title} - نظام الإعدادات المتطور")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(450, 280)

        # خلفية النافذة مطابقة لنوافذ الأقساط
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)

        # تحديد لون العنوان حسب نوع العملية
        title_colors = {
            'warning': 'rgba(251, 191, 36, 0.3)',  # أصفر للتحذير
            'danger': 'rgba(239, 68, 68, 0.3)',    # أحمر للخطر
            'info': 'rgba(59, 130, 246, 0.3)',     # أزرق للمعلومات
            'success': 'rgba(34, 197, 94, 0.3)'    # أخضر للنجاح
        }

        title_color = title_colors.get(self.operation_type, title_colors['warning'])

        title_label.setStyleSheet(f"""
            QLabel {{
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {title_color},
                    stop:0.5 {title_color.replace('0.3', '0.4')},
                    stop:1 {title_color});
                border: 2px solid {title_color.replace('0.3', '0.6')};
                border-radius: 8px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }}
        """)
        layout.addWidget(title_label)

        # رسالة التأكيد
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 13px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 12px;
                margin: 5px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                line-height: 1.4;
            }
        """)
        layout.addWidget(message_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ هل تريد المتابعة؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 8px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(12)

        cancel_button = QPushButton(f"❌ {self.cancel_text}")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton(f"{self.icon} {self.confirm_text}")
        button_type = 'danger' if self.operation_type in ['danger', 'warning'] else 'primary'
        self.style_advanced_button(confirm_button, button_type)
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للأقساط"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار مطابق للأقساط"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444',
                    'primary': '#8B5CF6',
                    'success': '#10B981'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:0.5 {color}DD, stop:1 {color}BB);
                        color: white;
                        border: 2px solid {color}AA;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                        font-size: 12px;
                        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}EE, stop:0.5 {color}CC, stop:1 {color}AA);
                        border: 2px solid {color};
                        transform: translateY(-1px);
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}AA, stop:0.5 {color}88, stop:1 {color}66);
                        transform: translateY(1px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")


class SettingsProgressDialog(QDialog):
    """نافذة تقدم محسنة لعمليات الإعدادات"""

    def __init__(self, parent=None, title="جاري المعالجة", message="يرجى الانتظار..."):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة التقدم"""
        self.setWindowTitle(f"⏳ {self.title}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint)
        self.setModal(True)
        self.resize(400, 150)

        # خلفية النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel(f"⏳ {self.title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(59, 130, 246, 0.4),
                    stop:1 rgba(59, 130, 246, 0.3));
                border: 2px solid rgba(59, 130, 246, 0.6);
                border-radius: 8px;
                padding: 8px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التقدم
        self.message_label = QLabel(self.message)
        self.message_label.setAlignment(Qt.AlignCenter)
        self.message_label.setWordWrap(True)
        self.message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 13px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(self.message_label)

    def update_message(self, message):
        """تحديث رسالة التقدم"""
        self.message_label.setText(message)
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()


class SettingsResultDialog(QDialog):
    """نافذة عرض النتائج المحسنة لقسم الإعدادات"""

    def __init__(self, parent=None, title="النتائج", message="", result_type="info", details=None):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.result_type = result_type
        self.details = details or []
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة النتائج"""
        # تحديد الأيقونة حسب نوع النتيجة
        icons = {
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️'
        }
        icon = icons.get(self.result_type, 'ℹ️')

        self.setWindowTitle(f"{icon} {self.title} - نظام الإعدادات المتطور")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(500, 500)  # زيادة الارتفاع من 400 إلى 500 لتتناسب مع الإطار الأكبر

        # خلفية النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel(f"{icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)

        # تحديد لون العنوان حسب نوع النتيجة
        title_colors = {
            'success': 'rgba(34, 197, 94, 0.3)',
            'warning': 'rgba(251, 191, 36, 0.3)',
            'error': 'rgba(239, 68, 68, 0.3)',
            'info': 'rgba(59, 130, 246, 0.3)'
        }

        title_color = title_colors.get(self.result_type, title_colors['info'])

        title_label.setStyleSheet(f"""
            QLabel {{
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {title_color},
                    stop:0.5 {title_color.replace('0.3', '0.4')},
                    stop:1 {title_color});
                border: 2px solid {title_color.replace('0.3', '0.6')};
                border-radius: 10px;
                padding: 12px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }}
        """)
        layout.addWidget(title_label)

        # رسالة النتيجة الرئيسية
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 15px;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                line-height: 1.5;
            }
        """)
        layout.addWidget(message_label)

        # منطقة التفاصيل (إذا كانت متوفرة)
        if self.details:
            details_label = QLabel("📋 التفاصيل:")
            details_label.setStyleSheet("""
                QLabel {
                    color: #fbbf24;
                    font-size: 14px;
                    font-weight: bold;
                    margin: 10px 0 5px 0;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(details_label)

            # منطقة نص قابلة للتمرير للتفاصيل - مضاعفة الارتفاع وتكبير الخط
            from PyQt5.QtWidgets import QTextEdit
            details_text = QTextEdit()
            details_text.setPlainText("\n".join(self.details))
            details_text.setReadOnly(True)
            details_text.setMaximumHeight(300)  # مضاعفة الارتفاع من 150 إلى 300
            details_text.setStyleSheet("""
                QTextEdit {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(0, 0, 0, 0.3),
                        stop:0.5 rgba(30, 41, 59, 0.4),
                        stop:1 rgba(0, 0, 0, 0.3));
                    color: #ffffff;
                    border: 2px solid rgba(255, 255, 255, 0.2);
                    border-radius: 8px;
                    padding: 10px;
                    font-family: 'Courier New', monospace;
                    font-size: 14px;  /* تكبير الخط من 12px إلى 14px */
                    line-height: 1.4;
                }
                QScrollBar:vertical {
                    background: rgba(255, 255, 255, 0.1);
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    min-height: 20px;
                }
                QScrollBar::handle:vertical:hover {
                    background: rgba(255, 255, 255, 0.5);
                }
            """)
            layout.addWidget(details_text)

        # زر الإغلاق
        close_button = QPushButton("✅ موافق")
        self.style_advanced_button(close_button, self.result_type)
        close_button.clicked.connect(self.accept)
        close_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, 'primary')
            else:
                colors = {
                    'success': '#10B981',
                    'warning': '#F59E0B',
                    'error': '#EF4444',
                    'info': '#3B82F6'
                }
                color = colors.get(button_type, '#3B82F6')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:0.5 {color}DD, stop:1 {color}BB);
                        color: white;
                        border: 2px solid {color}AA;
                        border-radius: 10px;
                        padding: 12px 30px;
                        font-weight: bold;
                        font-size: 14px;
                        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}EE, stop:0.5 {color}CC, stop:1 {color}AA);
                        border: 2px solid {color};
                        transform: translateY(-1px);
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}AA, stop:0.5 {color}88, stop:1 {color}66);
                        transform: translateY(1px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")


class SystemStatusThread(QThread):
    """خيط لمراقبة حالة الأنظمة"""
    status_updated = pyqtSignal(dict)

    def run(self):
        while True:
            if ADVANCED_SYSTEMS_AVAILABLE:
                try:
                    status = get_systems_status()
                    self.status_updated.emit(status)
                except Exception as e:
                    print(f"خطأ في مراقبة حالة الأنظمة: {e}")

            self.msleep(5000)  # تحديث كل 5 ثوان

class SettingsWidget(QWidget):
    """واجهة الإعدادات المتقدمة"""
    
    def __init__(self, session=None):
        super().__init__()
        self.session = session
        self.status_thread = None
        self.init_ui()
        
        if ADVANCED_SYSTEMS_AVAILABLE:
            self.start_status_monitoring()
        elif BASIC_SYSTEMS_AVAILABLE:
            # عرض معلومات الأنظمة الأساسية فقط
            self.show_basic_info()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إنشاء التخطيط الرئيسي مطابق تماماً لباقي البرنامج
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية
        layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة العنوان الرئيسي مطابق للتقارير
        title_label = QLabel("⚙️ إدارة الإعدادات المتطورة - نظام شامل ومتقدم لإدارة الإعدادات مع أدوات احترافية للتحكم والمراقبة والصيانة")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        layout.addWidget(title_label)
        
        if not BASIC_SYSTEMS_AVAILABLE:
            # رسالة عدم توفر الأنظمة الأساسية
            warning_label = QLabel("❌ الأنظمة الأساسية غير متاحة\n💡 تأكد من وجود ملفات config.py و logging_config.py")
            warning_label.setAlignment(Qt.AlignCenter)
            warning_label.setStyleSheet("""
                QLabel {
                    color: #ef4444;
                    background: #fef2f2;
                    padding: 20px;
                    border-radius: 8px;
                    font-size: 14px;
                }
            """)
            layout.addWidget(warning_label)
            return

        if not ADVANCED_SYSTEMS_AVAILABLE:
            # رسالة عدم توفر الأنظمة المتقدمة
            warning_label = QLabel("⚠️ بعض الأنظمة المتقدمة غير متاحة\n💡 لتفعيل جميع الميزات: pip install schedule cryptography bcrypt")
            warning_label.setAlignment(Qt.AlignCenter)
            warning_label.setStyleSheet("""
                QLabel {
                    color: #f59e0b;
                    background: #fef3c7;
                    padding: 15px;
                    border-radius: 8px;
                    font-size: 12px;
                    margin-bottom: 10px;
                }
            """)
            layout.addWidget(warning_label)
        
        # إنشاء تبويبات للإعدادات المختلفة مع تصميم محسن ومطابق لباقي البرنامج
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                margin-top: 2px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: 136px;
                max-width: 136px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease-in-out;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: 136px;
                max-width: 136px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-size: 22px;
                font-weight: bold;
                text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
                transform: scale(1.05);
            }
        """)
        
        # تبويب حالة الأنظمة
        self.create_system_status_tab()

        # تبويب مراقبة الأداء
        self.create_performance_tab()

        # تبويب إعدادات الأمان
        self.create_security_tab()

        # تبويب إعدادات المالية والعملة
        self.create_financial_tab()

        # تبويب السجلات
        self.create_logs_tab()

        # تبويب النسخ الاحتياطي
        self.create_backup_tab()

        # تبويب الإعدادات العامة
        self.create_general_tab()

        # إضافة التبويبات للتخطيط الرئيسي مع أولوية التمدد (مطابق للعملاء)
        layout.addWidget(self.tabs, 1)

        # إنشاء إطار سفلي للأزرار خارج التبويبات مطابق للعملاء
        self.create_dynamic_buttons_frame(layout)

        # ربط تغيير التبويب بتحديث الأزرار
        self.tabs.currentChanged.connect(self.update_buttons_for_current_tab)

        # تطبيق العرض التكيفي بعد إضافة جميع التبويبات
        self.apply_adaptive_width()

    def apply_adaptive_width(self):
        """تطبيق العرض التكيفي التلقائي للتبويبات"""
        try:
            # حساب العرض المتاح
            available_width = self.width() if self.width() > 0 else 1200  # عرض افتراضي
            tab_count = self.tabs.count()

            if tab_count > 0:
                # حساب العرض المناسب لكل تبويب
                # ترك مساحة للهوامش والحدود والمسافات
                margin_space = 100  # مساحة للهوامش والحدود
                padding_space = tab_count * 20  # مساحة للحشو والمسافات بين التبويبات

                # حساب العرض المتاح للتبويبات
                usable_width = available_width - margin_space - padding_space
                tab_width = max(100, min(190, usable_width // tab_count))

                # تأكد من أن العرض منطقي
                if tab_width < 100:
                    tab_width = 100
                elif tab_width > 190:
                    tab_width = 190

                # عرض خاص لتبويبات معينة
                financial_tab_width = 240
                system_status_tab_width = 240

                # إنشاء تصميم جديد مع العرض التكيفي
                adaptive_style = f"""
            QTabWidget::pane {{
                border: 3px solid #000000;
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                margin-top: 2px;
            }}
            QTabBar::tab {{
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: {tab_width}px;
                max-width: {tab_width}px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease-in-out;
            }}
            QTabBar::tab:selected {{
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: {tab_width}px;
                max-width: {tab_width}px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }}
            QTabBar::tab:hover {{
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-size: 22px;
                font-weight: bold;
                text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
                transform: scale(1.05);
            }}

        """

                self.tabs.setStyleSheet(adaptive_style)

                # تطبيق العرض الخاص بعد تطبيق النمط العام
                self.apply_special_tab_widths(system_status_tab_width, financial_tab_width)

                print(f"🔧 تم تطبيق العرض التكيفي:")
                print(f"   • العرض العادي: {tab_width}px")
                print(f"   • حالة الأنظمة: {system_status_tab_width}px")
                print(f"   • التحكم المالي: {financial_tab_width}px")

        except Exception as e:
            print(f"⚠️ خطأ في العرض التكيفي: {e}")

    def apply_special_tab_widths(self, system_width, financial_width):
        """تطبيق عرض خاص على تبويبات محددة"""
        try:
            # الحصول على QTabBar
            tab_bar = self.tabs.tabBar()
            if tab_bar:
                # تطبيق العرض الخاص على تبويبة حالة الأنظمة (الفهرس 0)
                tab_bar.setTabData(0, {'width': system_width})

                # تطبيق العرض الخاص على تبويبة التحكم المالي (الفهرس 3)
                tab_bar.setTabData(3, {'width': financial_width})

                # إنشاء نمط CSS خاص لكل تبويبة
                special_style = f"""
                    QTabBar::tab:first {{
                        min-width: {system_width}px;
                        max-width: {system_width}px;
                    }}
                """

                # تطبيق النمط الإضافي
                current_style = self.tabs.styleSheet()
                self.tabs.setStyleSheet(current_style + special_style)

                print(f"✅ تم تطبيق العرض الخاص:")
                print(f"   • حالة الأنظمة (فهرس 0): {system_width}px")
                print(f"   • التحكم المالي (فهرس 3): {financial_width}px")

        except Exception as e:
            print(f"❌ خطأ في تطبيق العرض الخاص: {e}")

    def resizeEvent(self, event):
        """إعادة حساب العرض عند تغيير حجم النافذة"""
        super().resizeEvent(event)
        if hasattr(self, 'tabs'):
            self.apply_adaptive_width()

    def create_system_status_tab(self):
        """تبويب حالة الأنظمة المحسن والمطور"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # عنوان القسم بتصميم محسن مطابق للعنوان الرئيسي
        title_label = QLabel("📊 حالة الأنظمة المتقدمة ومراقبة الحالة")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 6px 12px;
                margin: 1px;
                font-weight: bold;
                max-height: 45px;
                min-height: 45px;
            }
        """)
        layout.addWidget(title_label)

        # حالة الأنظمة بتصميم محسن مطابق لأنماط البرنامج
        status_group = QGroupBox()
        status_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        status_group.setTitle("📊 حالة الأنظمة المتقدمة")
        status_layout = QGridLayout(status_group)
        status_layout.setSpacing(10)
        status_layout.setContentsMargins(15, 18, 15, 12)

        self.status_labels = {}
        systems = [
            ("config", "⚙️ الإعدادات المركزية"),
            ("logging", "📝 نظام السجلات"),
            ("security", "🔐 نظام الأمان"),
            ("permissions", "👥 نظام الصلاحيات"),
            ("backup", "💾 النسخ الاحتياطي"),
            ("performance", "⚡ مراقبة الأداء")
        ]

        for i, (key, name) in enumerate(systems):
            # تسمية النظام
            label = QLabel(name)
            label.setFont(QFont("Segoe UI", 12, QFont.Bold))
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 12px;
                    min-width: 280px;
                    max-width: 280px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            status_layout.addWidget(label, i, 0)

            # حالة النظام
            status_label = QLabel("⏳ جاري التحقق...")
            status_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
            status_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.95),
                        stop:0.2 rgba(248, 250, 252, 0.98),
                        stop:0.4 rgba(241, 245, 249, 0.95),
                        stop:0.6 rgba(248, 250, 252, 0.98),
                        stop:0.8 rgba(255, 255, 255, 0.95),
                        stop:1 rgba(226, 232, 240, 0.9));
                    border: 3px solid rgba(96, 165, 250, 0.7);
                    border-radius: 12px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #f59e0b;
                    min-height: 30px;
                    min-width: 280px;
                    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                }
            """)
            status_label.setAlignment(Qt.AlignCenter)
            status_layout.addWidget(status_label, i, 1)

            self.status_labels[key] = status_label

        layout.addWidget(status_group)

        # إضافة قسم نصائح حالة الأنظمة بنمط البرنامج
        info_group = QGroupBox()
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
                min-height: 160px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 10px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                font-size: 13px;
            }
        """)
        info_group.setTitle("💡 نصائح مراقبة الأنظمة والحالة")
        info_group.setMinimumHeight(160)

        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(6)
        info_layout.setContentsMargins(15, 20, 15, 12)

        # نصائح حالة الأنظمة
        system_tips = [
            "📊 راقب حالة الأنظمة بانتظام للتأكد من سلامة التشغيل",
            "🔧 تحقق من الأنظمة المعطلة وأعد تشغيلها عند الحاجة",
            "⚠️ انتبه للتحذيرات والأخطاء في حالة الأنظمة",
            "🔄 أعد تشغيل البرنامج إذا كانت هناك مشاكل في الأنظمة"
        ]

        for tip in system_tips:
            tip_label = QLabel(tip)
            tip_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
            tip_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 6px;
                    margin: 2px 0px;
                    min-height: 25px;
                    max-height: 40px;
                    font-weight: bold;
                    font-size: 12px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            tip_label.setWordWrap(True)
            tip_label.setMinimumHeight(30)
            tip_label.setMaximumHeight(40)
            tip_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            tip_label.setAlignment(Qt.AlignCenter)
            info_layout.addWidget(tip_label)

        layout.addWidget(info_group)

        # إضافة مساحة للتمدد مع تحسين التوزيع
        layout.addStretch(1)

        self.tabs.addTab(tab, "📊 حالة الأنظمة")
    
    def create_security_tab(self):
        """تبويب إعدادات الأمان المحسن والمطور"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # عنوان القسم بتصميم محسن مطابق للعنوان الرئيسي
        title_label = QLabel("🔐 إعدادات الأمان والحماية المتقدمة")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 6px 12px;
                margin: 1px;
                font-weight: bold;
                max-height: 45px;
                min-height: 45px;
            }
        """)
        layout.addWidget(title_label)

        # إعدادات كلمات المرور بتصميم محسن مطابق لأنماط البرنامج
        password_group = QGroupBox()
        password_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        password_group.setTitle("🔐 إعدادات كلمات المرور والحماية")

        password_layout = QGridLayout(password_group)
        password_layout.setSpacing(10)
        password_layout.setContentsMargins(15, 18, 15, 12)

        # تسمية محسنة للحد الأدنى بنمط البرنامج
        min_length_label = QLabel("🔢 الحد الأدنى لطول كلمة المرور")
        min_length_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        min_length_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        min_length_label.setAlignment(Qt.AlignCenter)
        password_layout.addWidget(min_length_label, 0, 0)

        self.min_password_length = QSpinBox()
        self.min_password_length.setRange(4, 20)
        self.min_password_length.setValue(SecurityConfig.PASSWORD_MIN_LENGTH)
        self.min_password_length.setSuffix(" حرف")
        self.min_password_length.setStyleSheet("""
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        password_layout.addWidget(self.min_password_length, 0, 1)

        # تسمية محسنة للمحاولات بنمط البرنامج
        max_attempts_label = QLabel("⚠️ الحد الأقصى لمحاولات الدخول")
        max_attempts_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        max_attempts_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        max_attempts_label.setAlignment(Qt.AlignCenter)
        password_layout.addWidget(max_attempts_label, 1, 0)

        self.max_login_attempts = QSpinBox()
        self.max_login_attempts.setRange(3, 10)
        self.max_login_attempts.setValue(SecurityConfig.MAX_LOGIN_ATTEMPTS)
        self.max_login_attempts.setSuffix(" محاولة")
        self.max_login_attempts.setStyleSheet("""
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        password_layout.addWidget(self.max_login_attempts, 1, 1)

        layout.addWidget(password_group)

        # إعدادات الجلسة بتصميم محسن مطابق لأنماط البرنامج
        session_group = QGroupBox()
        session_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        session_group.setTitle("⏰ إعدادات الجلسة والوقت")

        session_layout = QGridLayout(session_group)
        session_layout.setSpacing(10)
        session_layout.setContentsMargins(15, 18, 15, 12)

        # تسمية محسنة للجلسة بنمط البرنامج
        timeout_label = QLabel("⏱️ مهلة انتهاء الجلسة")
        timeout_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        timeout_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        timeout_label.setAlignment(Qt.AlignCenter)
        session_layout.addWidget(timeout_label, 0, 0)

        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(300, 7200)
        self.session_timeout.setValue(SecurityConfig.SESSION_TIMEOUT)
        self.session_timeout.setSuffix(" ثانية")
        self.session_timeout.setStyleSheet("""
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        session_layout.addWidget(self.session_timeout, 0, 1)

        layout.addWidget(session_group)

        # إضافة قسم معلومات الأمان المتقدم بنمط البرنامج
        info_group = QGroupBox()
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
                min-height: 160px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 10px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                font-size: 13px;
            }
        """)
        info_group.setTitle("💡 نصائح الأمان والحماية")
        info_group.setMinimumHeight(160)

        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(6)
        info_layout.setContentsMargins(15, 20, 15, 12)

        # نصائح الأمان بنمط البرنامج
        security_tips = [
            "🔐 يُنصح بتعيين كلمة مرور قوية لا تقل عن 8 أحرف",
            "🚫 تجنب استخدام نفس كلمة المرور في أماكن أخرى",
            "🔄 قم بتغيير كلمة المرور بانتظام لضمان الأمان",
            "⚠️ لا تشارك معلومات تسجيل الدخول مع الآخرين"
        ]

        for tip in security_tips:
            tip_label = QLabel(tip)
            tip_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
            tip_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 6px;
                    margin: 2px 0px;
                    min-height: 25px;
                    max-height: 40px;
                    font-weight: bold;
                    font-size: 12px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            tip_label.setWordWrap(True)
            tip_label.setMinimumHeight(30)
            tip_label.setMaximumHeight(40)
            tip_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            tip_label.setAlignment(Qt.AlignCenter)
            info_layout.addWidget(tip_label)

        layout.addWidget(info_group)

        # إضافة مساحة للتمدد مع تحسين التوزيع
        layout.addStretch(1)

        self.tabs.addTab(tab, "🔐 الأمان والحماية")
    
    def create_backup_tab(self):
        """تبويب النسخ الاحتياطي المحسن والمطور"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # عنوان القسم بتصميم محسن مطابق للعنوان الرئيسي
        title_label = QLabel("💾 إعدادات النسخ الاحتياطي والاستعادة")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 6px 12px;
                margin: 1px;
                font-weight: bold;
                max-height: 45px;
                min-height: 45px;
            }
        """)
        layout.addWidget(title_label)

        # إعدادات النسخ الاحتياطي بتصميم محسن مطابق لأنماط البرنامج
        backup_settings = QGroupBox()
        backup_settings.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        backup_settings.setTitle("💾 إعدادات النسخ الاحتياطي")
        backup_layout = QGridLayout(backup_settings)
        backup_layout.setSpacing(10)
        backup_layout.setContentsMargins(15, 18, 15, 12)
        
        # تفعيل النسخ الاحتياطي التلقائي
        auto_backup_label = QLabel("🔄 تفعيل النسخ الاحتياطي التلقائي")
        auto_backup_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        auto_backup_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        auto_backup_label.setAlignment(Qt.AlignCenter)
        backup_layout.addWidget(auto_backup_label, 0, 0)

        self.auto_backup_enabled = QCheckBox("تفعيل")
        self.auto_backup_enabled.setChecked(BackupConfig.AUTO_BACKUP_ENABLED)
        self.auto_backup_enabled.setStyleSheet("""
            QCheckBox {
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                padding: 12px 20px;
                min-width: 280px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.95);
            }
            QCheckBox::indicator:checked {
                background: rgba(96, 165, 250, 0.9);
                border: 3px solid rgba(37, 99, 235, 0.9);
            }
        """)
        backup_layout.addWidget(self.auto_backup_enabled, 0, 1)

        # فترة النسخ الاحتياطي
        interval_label = QLabel("⏰ فترة النسخ الاحتياطي")
        interval_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        interval_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        interval_label.setAlignment(Qt.AlignCenter)
        backup_layout.addWidget(interval_label, 1, 0)

        self.backup_interval = QSpinBox()
        self.backup_interval.setRange(1, 24)
        self.backup_interval.setValue(BackupConfig.BACKUP_INTERVAL_HOURS)
        self.backup_interval.setSuffix(" ساعة")
        self.backup_interval.setPrefix("⏰ ")
        self.backup_interval.setStyleSheet("""
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        backup_layout.addWidget(self.backup_interval, 1, 1)

        # عدد النسخ المحفوظة
        max_backups_label = QLabel("📦 عدد النسخ المحفوظة")
        max_backups_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        max_backups_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        max_backups_label.setAlignment(Qt.AlignCenter)
        backup_layout.addWidget(max_backups_label, 2, 0)

        self.max_backups = QSpinBox()
        self.max_backups.setRange(5, 100)
        self.max_backups.setValue(BackupConfig.MAX_BACKUP_FILES)
        self.max_backups.setSuffix(" نسخة")
        self.max_backups.setPrefix("📦 ")
        self.max_backups.setStyleSheet("""
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        backup_layout.addWidget(self.max_backups, 2, 1)

        # ضغط النسخ الاحتياطية
        compression_label = QLabel("🗜️ ضغط النسخ الاحتياطية")
        compression_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        compression_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        compression_label.setAlignment(Qt.AlignCenter)
        backup_layout.addWidget(compression_label, 3, 0)

        self.backup_compression = QCheckBox("تفعيل الضغط")
        self.backup_compression.setChecked(BackupConfig.BACKUP_COMPRESSION)
        self.backup_compression.setStyleSheet("""
            QCheckBox {
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                padding: 12px 20px;
                min-width: 280px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.95);
            }
            QCheckBox::indicator:checked {
                background: rgba(96, 165, 250, 0.9);
                border: 3px solid rgba(37, 99, 235, 0.9);
            }
        """)
        backup_layout.addWidget(self.backup_compression, 3, 1)
        
        layout.addWidget(backup_settings)

        # إضافة قسم نصائح النسخ الاحتياطي بنمط البرنامج
        info_group = QGroupBox()
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
                min-height: 160px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 10px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                font-size: 13px;
            }
        """)
        info_group.setTitle("💡 نصائح النسخ الاحتياطي والحماية")
        info_group.setMinimumHeight(160)

        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(6)
        info_layout.setContentsMargins(15, 20, 15, 12)

        # نصائح النسخ الاحتياطي
        backup_tips = [
            "💾 قم بإنشاء نسخ احتياطية بانتظام لحماية بياناتك",
            "📁 احفظ النسخ الاحتياطية في مكان آمن ومنفصل",
            "🔄 اختبر استعادة النسخ الاحتياطية بشكل دوري",
            "🗜️ استخدم الضغط لتوفير مساحة التخزين"
        ]

        for tip in backup_tips:
            tip_label = QLabel(tip)
            tip_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
            tip_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 6px;
                    margin: 2px 0px;
                    min-height: 25px;
                    max-height: 40px;
                    font-weight: bold;
                    font-size: 12px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            tip_label.setWordWrap(True)
            tip_label.setMinimumHeight(30)
            tip_label.setMaximumHeight(40)
            tip_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            tip_label.setAlignment(Qt.AlignCenter)
            info_layout.addWidget(tip_label)

        layout.addWidget(info_group)

        # إضافة مساحة للتمدد مع تحسين التوزيع
        layout.addStretch(1)

        self.tabs.addTab(tab, "💾 النسخ الاحتياطي")
    
    def create_performance_tab(self):
        """تبويب مراقبة الأداء المحسن والمطور"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # عنوان القسم بتصميم محسن مطابق للعنوان الرئيسي
        title_label = QLabel("⚡ إعدادات مراقبة الأداء والتحسين")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 6px 12px;
                margin: 1px;
                font-weight: bold;
                max-height: 45px;
                min-height: 45px;
            }
        """)
        layout.addWidget(title_label)

        # إعدادات الأداء بتصميم محسن مطابق لأنماط البرنامج
        perf_settings = QGroupBox()
        perf_settings.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        perf_settings.setTitle("⚡ إعدادات مراقبة الأداء")
        perf_layout = QGridLayout(perf_settings)
        perf_layout.setSpacing(10)
        perf_layout.setContentsMargins(15, 18, 15, 12)
        
        # تفعيل مراقبة الأداء
        monitoring_label = QLabel("⚡ تفعيل مراقبة الأداء")
        monitoring_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        monitoring_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        monitoring_label.setAlignment(Qt.AlignCenter)
        perf_layout.addWidget(monitoring_label, 0, 0)

        self.performance_monitoring = QCheckBox("تفعيل")
        self.performance_monitoring.setChecked(PerformanceConfig.PERFORMANCE_MONITORING_ENABLED)
        self.performance_monitoring.setStyleSheet("""
            QCheckBox {
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                padding: 12px 20px;
                min-width: 280px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.95);
            }
            QCheckBox::indicator:checked {
                background: rgba(96, 165, 250, 0.9);
                border: 3px solid rgba(37, 99, 235, 0.9);
            }
        """)
        perf_layout.addWidget(self.performance_monitoring, 0, 1)

        # الحد الأقصى لاستخدام الذاكرة
        memory_label = QLabel("💾 الحد الأقصى للذاكرة")
        memory_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        memory_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        memory_label.setAlignment(Qt.AlignCenter)
        perf_layout.addWidget(memory_label, 1, 0)

        self.max_memory = QSpinBox()
        self.max_memory.setRange(256, 2048)
        self.max_memory.setValue(PerformanceConfig.MAX_MEMORY_USAGE_MB)
        self.max_memory.setSuffix(" MB")
        self.max_memory.setPrefix("💾 ")
        self.max_memory.setStyleSheet("""
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        perf_layout.addWidget(self.max_memory, 1, 1)

        # الحد الأقصى لوقت الاستعلام
        query_time_label = QLabel("⏱️ الحد الأقصى للاستعلام")
        query_time_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        query_time_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        query_time_label.setAlignment(Qt.AlignCenter)
        perf_layout.addWidget(query_time_label, 2, 0)

        self.max_query_time = QSpinBox()
        self.max_query_time.setRange(1, 30)
        self.max_query_time.setValue(PerformanceConfig.MAX_QUERY_TIME_SECONDS)
        self.max_query_time.setSuffix(" ثانية")
        self.max_query_time.setPrefix("⏱️ ")
        self.max_query_time.setStyleSheet("""
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        perf_layout.addWidget(self.max_query_time, 2, 1)
        
        layout.addWidget(perf_settings)

        # إضافة قسم نصائح الأداء بنمط البرنامج
        info_group = QGroupBox()
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
                min-height: 160px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 10px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                font-size: 13px;
            }
        """)
        info_group.setTitle("💡 نصائح تحسين الأداء والسرعة")
        info_group.setMinimumHeight(160)

        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(6)
        info_layout.setContentsMargins(15, 20, 15, 12)

        # نصائح الأداء
        performance_tips = [
            "📊 راقب استخدام الذاكرة والمعالج بانتظام",
            "⚡ أغلق البرامج غير المستخدمة لتحسين الأداء",
            "🗄️ نظف قاعدة البيانات دورياً لتسريع الاستعلامات",
            "💾 استخدم أقراص SSD لتحسين سرعة القراءة والكتابة"
        ]

        for tip in performance_tips:
            tip_label = QLabel(tip)
            tip_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
            tip_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 6px;
                    margin: 2px 0px;
                    min-height: 25px;
                    max-height: 40px;
                    font-weight: bold;
                    font-size: 12px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            tip_label.setWordWrap(True)
            tip_label.setMinimumHeight(30)
            tip_label.setMaximumHeight(40)
            tip_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            tip_label.setAlignment(Qt.AlignCenter)
            info_layout.addWidget(tip_label)

        layout.addWidget(info_group)

        # إضافة مساحة للتمدد مع تحسين التوزيع
        layout.addStretch(1)

        self.tabs.addTab(tab, "⚡ مراقبة الأداء")
    
    def create_logs_tab(self):
        """تبويب السجلات المحسن والمطور"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # عنوان القسم بتصميم محسن مطابق للعنوان الرئيسي
        title_label = QLabel("📝 إدارة السجلات ومراقبة الأحداث")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 6px 12px;
                margin: 1px;
                font-weight: bold;
                max-height: 45px;
                min-height: 45px;
            }
        """)
        layout.addWidget(title_label)

        # عرض السجلات الأخيرة بتصميم محسن مطابق لأنماط البرنامج
        recent_logs_group = QGroupBox()
        recent_logs_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        recent_logs_group.setTitle("📋 السجلات الأخيرة")
        recent_logs_layout = QVBoxLayout(recent_logs_group)
        recent_logs_layout.setSpacing(10)
        recent_logs_layout.setContentsMargins(15, 18, 15, 12)



        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setMinimumHeight(350)
        self.logs_text.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f172a, stop:0.15 #1e293b, stop:0.3 #334155,
                    stop:0.5 #475569, stop:0.7 #334155, stop:0.85 #1e293b, stop:1 #0f172a);
                color: #e2e8f0;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 13px;
                font-weight: bold;
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 15px;
                selection-background-color: rgba(96, 165, 250, 0.4);
                selection-color: #ffffff;
                line-height: 1.4;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QTextEdit:focus {
                border: 3px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f172a, stop:0.2 #1e293b, stop:0.4 #334155,
                    stop:0.6 #334155, stop:0.8 #1e293b, stop:1 #0f172a);
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(30, 41, 59, 0.9),
                    stop:0.5 rgba(51, 65, 85, 0.8),
                    stop:1 rgba(30, 41, 59, 0.9));
                width: 14px;
                border-radius: 7px;
                border: 1px solid rgba(96, 165, 250, 0.3);
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(96, 165, 250, 0.8),
                    stop:0.5 rgba(59, 130, 246, 0.9),
                    stop:1 rgba(96, 165, 250, 0.8));
                border-radius: 6px;
                min-height: 25px;
                border: 1px solid rgba(37, 99, 235, 0.6);
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(96, 165, 250, 0.95),
                    stop:0.5 rgba(59, 130, 246, 1.0),
                    stop:1 rgba(96, 165, 250, 0.95));
                border: 2px solid rgba(37, 99, 235, 0.8);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.9),
                    stop:0.5 rgba(51, 65, 85, 0.8),
                    stop:1 rgba(30, 41, 59, 0.9));
                height: 14px;
                border-radius: 7px;
                border: 1px solid rgba(96, 165, 250, 0.3);
            }
            QScrollBar::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(96, 165, 250, 0.8),
                    stop:0.5 rgba(59, 130, 246, 0.9),
                    stop:1 rgba(96, 165, 250, 0.8));
                border-radius: 6px;
                min-width: 25px;
                border: 1px solid rgba(37, 99, 235, 0.6);
            }
            QScrollBar::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(96, 165, 250, 0.95),
                    stop:0.5 rgba(59, 130, 246, 1.0),
                    stop:1 rgba(96, 165, 250, 0.95));
                border: 2px solid rgba(37, 99, 235, 0.8);
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
            }
        """)

        # إضافة نص تجريبي للسجلات مع تنسيق في المنتصف
        sample_logs = """🚀 [2025-07-21 02:55:45] INFO - تم تشغيل البرنامج بنجاح
✅ [2025-07-21 02:55:46] INFO - تم تحميل الإعدادات المركزية
🔐 [2025-07-21 02:55:47] INFO - تم تهيئة نظام الأمان والتشفير
👥 [2025-07-21 02:55:48] INFO - تم تهيئة نظام الصلاحيات
⚡ [2025-07-21 02:55:49] INFO - تم تهيئة نظام مراقبة الأداء
🗄️ [2025-07-21 02:55:50] INFO - تم إعداد قاعدة البيانات بنجاح
🎯 [2025-07-21 02:55:51] INFO - تم تحميل لوحة المعلومات
🤝 [2025-07-21 02:55:52] INFO - تم تحميل قسم العملاء
🏭 [2025-07-21 02:55:53] INFO - تم تحميل قسم الموردين
👷 [2025-07-21 02:55:54] INFO - تم تحميل قسم العمال
🏢 [2025-07-21 02:55:55] INFO - تم تحميل قسم المشاريع
📦 [2025-07-21 02:55:56] INFO - تم تحميل قسم المخازن
💰 [2025-07-21 02:55:57] INFO - تم تحميل قسم الإيرادات
💸 [2025-07-21 02:55:58] INFO - تم تحميل قسم المصروفات
📋 [2025-07-21 02:55:59] INFO - تم تحميل قسم التقارير
⚙️ [2025-07-21 02:56:00] INFO - تم تحميل قسم الإعدادات
🎉 [2025-07-21 02:56:01] SUCCESS - جميع الأقسام تعمل بنجاح!"""

        self.logs_text.setPlainText(sample_logs)

        # تطبيق التنسيق الأولي
        self.apply_logs_formatting()

        recent_logs_layout.addWidget(self.logs_text)

        layout.addWidget(recent_logs_group)

        # إضافة قسم نصائح السجلات بنمط البرنامج
        info_group = QGroupBox()
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
                min-height: 160px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 10px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                font-size: 13px;
            }
        """)
        info_group.setTitle("💡 نصائح إدارة السجلات والمراقبة")
        info_group.setMinimumHeight(160)

        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(6)
        info_layout.setContentsMargins(15, 20, 15, 12)

        # نصائح السجلات
        logs_tips = [
            "📝 راجع السجلات بانتظام لمتابعة أحداث النظام",
            "🔍 ابحث عن الأخطاء والتحذيرات في السجلات",
            "📊 استخدم السجلات لتحليل أداء البرنامج",
            "🗂️ احفظ نسخ من السجلات المهمة للمراجعة اللاحقة"
        ]

        for tip in logs_tips:
            tip_label = QLabel(tip)
            tip_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
            tip_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 6px;
                    margin: 2px 0px;
                    min-height: 25px;
                    max-height: 40px;
                    font-weight: bold;
                    font-size: 12px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            tip_label.setWordWrap(True)
            tip_label.setMinimumHeight(30)
            tip_label.setMaximumHeight(40)
            tip_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            tip_label.setAlignment(Qt.AlignCenter)
            info_layout.addWidget(tip_label)

        layout.addWidget(info_group)

        # إضافة مساحة للتمدد مع تحسين التوزيع
        layout.addStretch(1)

        self.tabs.addTab(tab, "📝 السجلات الأخيرة")

    def apply_logs_formatting(self):
        """تطبيق تنسيق السجلات مع المحاذاة في المنتصف"""
        cursor = self.logs_text.textCursor()
        cursor.select(cursor.Document)

        # تطبيق تنسيق المحاذاة
        block_format = cursor.blockFormat()
        block_format.setAlignment(Qt.AlignCenter)
        cursor.setBlockFormat(block_format)

        # تحريك المؤشر إلى النهاية
        cursor.movePosition(cursor.End)
        self.logs_text.setTextCursor(cursor)

    def create_financial_tab(self):
        """تبويب إعدادات المالية والعملة المحسن والمطور"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # عنوان القسم بتصميم محسن مطابق للعنوان الرئيسي
        title_label = QLabel("💰 إعدادات المالية والعملة")
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 8px;
                margin: 1px;
                font-weight: bold;
                max-height: 35px;
                min-height: 35px;
            }
        """)
        layout.addWidget(title_label)

        # إعدادات العملة الأساسية بتصميم محسن مطابق لأنماط البرنامج
        currency_group = QGroupBox()
        currency_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        currency_group.setTitle("💰 إعدادات العملة")

        currency_layout = QGridLayout(currency_group)
        currency_layout.setSpacing(10)
        currency_layout.setContentsMargins(15, 18, 15, 12)

        # العملة الافتراضية
        currency_label = QLabel("💰 العملة الافتراضية")
        currency_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        currency_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 6px 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 250px;
                max-width: 250px;
                min-height: 28px;
                max-height: 28px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        currency_label.setAlignment(Qt.AlignCenter)
        currency_layout.addWidget(currency_label, 0, 0)

        self.default_currency = QComboBox()
        currencies = [
            ("EGP", "🇪🇬 جنيه مصري (ج.م)"),
            ("SAR", "🇸🇦 ريال سعودي (ر.س)"),
            ("AED", "🇦🇪 درهم إماراتي (د.إ)"),
            ("KWD", "🇰🇼 دينار كويتي (د.ك)"),
            ("QAR", "🇶🇦 ريال قطري (ر.ق)"),
            ("USD", "🇺🇸 دولار أمريكي ($)"),
            ("EUR", "🇪🇺 يورو (€)")
        ]

        for code, name in currencies:
            self.default_currency.addItem(name, code)

        self.default_currency.setCurrentIndex(0)  # جنيه مصري افتراضي

        # ربط تغيير العملة بتحديث رمز العملة
        self.default_currency.currentTextChanged.connect(self.update_currency_symbol_options)
        self.default_currency.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 12px 18px 16px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
                min-height: 20px;
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        currency_layout.addWidget(self.default_currency, 0, 1)

        # عدد الخانات العشرية
        decimal_label = QLabel("🔢 عدد الخانات العشرية")
        decimal_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        decimal_label.setStyleSheet(currency_label.styleSheet())
        decimal_label.setAlignment(Qt.AlignCenter)
        currency_layout.addWidget(decimal_label, 1, 0)

        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 4)
        self.decimal_places.setValue(2)  # خانتان عشريتان افتراضياً
        self.decimal_places.setSuffix(" خانة")

        # ربط تغيير عدد الخانات العشرية بتحديث قائمة الفواصل
        self.decimal_places.valueChanged.connect(self.update_thousands_separator_options)
        self.decimal_places.setStyleSheet("""
            QSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                min-height: 25px;
                max-height: 25px;
                min-width: 250px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        currency_layout.addWidget(self.decimal_places, 1, 1)

        layout.addWidget(currency_group)

        # إعدادات الضرائب والفواتير بتصميم محسن مطابق لأنماط البرنامج
        tax_group = QGroupBox()
        tax_group.setStyleSheet(currency_group.styleSheet())
        tax_group.setTitle("📊 إعدادات الضرائب")

        tax_layout = QGridLayout(tax_group)
        tax_layout.setSpacing(10)
        tax_layout.setContentsMargins(15, 18, 15, 12)

        # معدل الضريبة الافتراضي
        tax_rate_label = QLabel("📊 معدل الضريبة الافتراضي")
        tax_rate_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        tax_rate_label.setStyleSheet(currency_label.styleSheet())
        tax_rate_label.setAlignment(Qt.AlignCenter)
        tax_layout.addWidget(tax_rate_label, 0, 0)

        self.default_tax_rate = QDoubleSpinBox()
        self.default_tax_rate.setRange(0.0, 50.0)
        self.default_tax_rate.setValue(14.0)  # ضريبة القيمة المضافة في مصر
        self.default_tax_rate.setSuffix(" %")
        self.default_tax_rate.setDecimals(2)
        self.default_tax_rate.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                min-height: 25px;
                max-height: 25px;
                min-width: 250px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        tax_layout.addWidget(self.default_tax_rate, 0, 1)

        # رقم الضريبة
        tax_number_label = QLabel("🏢 الرقم الضريبي للشركة")
        tax_number_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        tax_number_label.setStyleSheet(currency_label.styleSheet())
        tax_number_label.setAlignment(Qt.AlignCenter)
        tax_layout.addWidget(tax_number_label, 1, 0)

        self.company_tax_number = QLineEdit()
        self.company_tax_number.setPlaceholderText("أدخل الرقم الضريبي...")
        self.company_tax_number.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                min-height: 25px;
                max-height: 25px;
                min-width: 250px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
            QLineEdit:hover {
                border: 3px solid rgba(96, 165, 250, 0.85);
                background: rgba(248, 250, 252, 0.98);
            }
        """)
        tax_layout.addWidget(self.company_tax_number, 1, 1)

        layout.addWidget(tax_group)

        # إعدادات تنسيق الأرقام بتصميم محسن مطابق لأنماط البرنامج
        format_group = QGroupBox()
        format_group.setStyleSheet(currency_group.styleSheet())
        format_group.setTitle("🔢 تنسيق الأرقام")

        format_layout = QGridLayout(format_group)
        format_layout.setSpacing(10)
        format_layout.setContentsMargins(15, 18, 15, 12)

        # موضع رمز العملة
        symbol_position_label = QLabel("📍 موضع رمز العملة")
        symbol_position_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        symbol_position_label.setStyleSheet(currency_label.styleSheet())
        symbol_position_label.setAlignment(Qt.AlignCenter)
        format_layout.addWidget(symbol_position_label, 0, 0)

        self.currency_symbol_position = QComboBox()
        # سيتم ملء القائمة عند اختيار العملة

        self.currency_symbol_position.setCurrentIndex(0)  # الخيار الأول افتراضياً
        self.currency_symbol_position.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 12px 18px 16px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(245, 158, 11, 0.3);
                box-shadow: 0 4px 15px rgba(245, 158, 11, 0.25);
                min-height: 20px;
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
                transform: scale(1.02);
            }
        """)
        format_layout.addWidget(self.currency_symbol_position, 0, 1)

        # فاصل الآلاف
        thousands_separator_label = QLabel("🔢 فاصل الآلاف")
        thousands_separator_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        thousands_separator_label.setStyleSheet(currency_label.styleSheet())
        thousands_separator_label.setAlignment(Qt.AlignCenter)
        format_layout.addWidget(thousands_separator_label, 1, 0)

        self.thousands_separator = QComboBox()
        # سيتم ملء القائمة عند تحديد عدد الخانات العشرية

        self.thousands_separator.setCurrentIndex(0)  # الخيار الأول افتراضياً
        self.thousands_separator.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 12px 18px 16px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(16, 185, 129, 0.3);
                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.25);
                min-height: 20px;
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
                transform: scale(1.02);
            }
        """)
        format_layout.addWidget(self.thousands_separator, 1, 1)

        layout.addWidget(format_group)

        # إضافة قسم نصائح المالية والعملة بتصميم محسن مطابق لأنماط البرنامج
        info_group = QGroupBox()
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        info_group.setTitle("💡 نصائح مهمة للإعدادات المالية")

        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(3)
        info_layout.setContentsMargins(15, 8, 15, 8)

        financial_tips = [
            "💰 اختر العملة المناسبة لطبيعة عملك",
            "📊 تأكد من صحة معدل الضريبة",
            "🔢 استخدم عدد الخانات العشرية المناسب"
        ]

        for tip in financial_tips:
            tip_label = QLabel(tip)
            tip_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
            tip_label.setStyleSheet("""
                QLabel {
                    color: #1f2937;
                    padding: 5px;
                    background: transparent;
                    border: none;
                    font-size: 14px;
                    font-weight: bold;
                }
            """)
            tip_label.setWordWrap(True)
            tip_label.setAlignment(Qt.AlignCenter)
            info_layout.addWidget(tip_label)

        layout.addWidget(info_group)

        # إضافة مساحة صغيرة للتمدد
        layout.addStretch()

        # تحديث خيارات رمز العملة للعملة الافتراضية
        self.update_currency_symbol_options()

        # تحديث خيارات فواصل الآلاف للخانات العشرية الافتراضية
        self.update_thousands_separator_options()

        self.tabs.addTab(tab, "💰 التحكم المالي")

    def update_currency_symbol_options(self):
        """تحديث خيارات موضع رمز العملة بناءً على العملة المختارة"""
        try:
            # الحصول على العملة المختارة
            current_currency_code = self.default_currency.currentData()
            current_currency_text = self.default_currency.currentText()

            # رموز العملات
            currency_symbols = {
                'EGP': 'ج.م',
                'USD': '$',
                'EUR': '€',
                'SAR': 'ر.س',
                'AED': 'د.إ',
                'KWD': 'د.ك',
                'QAR': 'ر.ق'
            }

            symbol = currency_symbols.get(current_currency_code, current_currency_code)

            # مسح الخيارات الحالية
            self.currency_symbol_position.clear()

            # إضافة الخيارات الجديدة مع رمز العملة المحدد
            positions = [
                ("before", f"قبل الرقم ({symbol} 100)"),
                ("after", f"بعد الرقم (100 {symbol})")
            ]

            for code, name in positions:
                self.currency_symbol_position.addItem(name, code)

            # تعيين "بعد الرقم" كافتراضي
            self.currency_symbol_position.setCurrentIndex(1)

        except Exception as e:
            # في حالة حدوث خطأ، استخدم خيارات افتراضية
            self.currency_symbol_position.clear()
            self.currency_symbol_position.addItem("قبل الرقم", "before")
            self.currency_symbol_position.addItem("بعد الرقم", "after")
            self.currency_symbol_position.setCurrentIndex(1)

    def update_thousands_separator_options(self):
        """تحديث خيارات فواصل الآلاف بناءً على عدد الخانات العشرية"""
        try:
            # الحصول على عدد الخانات العشرية
            decimal_places = self.decimal_places.value()

            # إنشاء نموذج الرقم العشري
            if decimal_places == 0:
                decimal_example = ""
            else:
                decimal_example = "." + "0" * decimal_places

            # مسح الخيارات الحالية
            current_selection = self.thousands_separator.currentData()
            self.thousands_separator.clear()

            # إضافة الخيارات الجديدة مع عدد الخانات العشرية المحدث
            separators = [
                (",", f"فاصلة (1,000{decimal_example})"),
                (".", f"نقطة (1.000{decimal_example.replace('.', ',') if decimal_example else ''})"),
                ("int", "رقم صحيح (1000)")
            ]

            for code, name in separators:
                self.thousands_separator.addItem(name, code)

            # استعادة الاختيار السابق إن أمكن
            if current_selection:
                index = self.thousands_separator.findData(current_selection)
                if index >= 0:
                    self.thousands_separator.setCurrentIndex(index)
                else:
                    self.thousands_separator.setCurrentIndex(0)
            else:
                self.thousands_separator.setCurrentIndex(0)  # فاصلة افتراضياً

        except Exception as e:
            # في حالة حدوث خطأ، استخدم خيارات افتراضية
            self.thousands_separator.clear()
            self.thousands_separator.addItem("فاصلة (1,000.00)", ",")
            self.thousands_separator.addItem("نقطة (1.000,00)", ".")
            self.thousands_separator.addItem("رقم صحيح (1000)", "int")
            self.thousands_separator.setCurrentIndex(0)

    def create_general_tab(self):
        """تبويب الإعدادات العامة المحسن والمطور"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # عنوان القسم بتصميم محسن مطابق للعنوان الرئيسي
        title_label = QLabel("⚙️ الإعدادات العامة ومعلومات التطبيق")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 6px 12px;
                margin: 1px;
                font-weight: bold;
                max-height: 45px;
                min-height: 45px;
            }
        """)
        layout.addWidget(title_label)

        # معلومات التطبيق بتصميم محسن مطابق لأنماط البرنامج
        app_info = QGroupBox()
        app_info.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 12px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 6px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 8px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
        """)
        app_info.setTitle("ℹ️ معلومات التطبيق")
        app_layout = QGridLayout(app_info)
        app_layout.setSpacing(10)
        app_layout.setContentsMargins(15, 18, 15, 12)

        # اسم التطبيق
        app_name_label = QLabel("📱 اسم التطبيق")
        app_name_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        app_name_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        app_name_label.setAlignment(Qt.AlignCenter)
        app_layout.addWidget(app_name_label, 0, 0)

        app_name_value = QLabel(AppConfig.APP_NAME)
        app_name_value.setFont(QFont("Segoe UI", 16, QFont.Bold))
        app_name_value.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
        """)
        app_name_value.setAlignment(Qt.AlignCenter)
        app_layout.addWidget(app_name_value, 0, 1)

        # الإصدار
        version_label = QLabel("🔢 الإصدار")
        version_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        version_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        version_label.setAlignment(Qt.AlignCenter)
        app_layout.addWidget(version_label, 1, 0)

        version_value = QLabel(AppConfig.APP_VERSION)
        version_value.setFont(QFont("Segoe UI", 16, QFont.Bold))
        version_value.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
        """)
        version_value.setAlignment(Qt.AlignCenter)
        app_layout.addWidget(version_value, 1, 1)

        # المطور
        author_label = QLabel("👨‍💻 المطور")
        author_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        author_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 280px;
                max-width: 280px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        author_label.setAlignment(Qt.AlignCenter)
        app_layout.addWidget(author_label, 2, 0)

        author_value = QLabel(AppConfig.APP_AUTHOR)
        author_value.setFont(QFont("Segoe UI", 16, QFont.Bold))
        author_value.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
        """)
        author_value.setAlignment(Qt.AlignCenter)
        app_layout.addWidget(author_value, 2, 1)

        layout.addWidget(app_info)

        # إضافة قسم نصائح الإعدادات العامة بنمط البرنامج
        info_group = QGroupBox()
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #1e293b;
                border: 2px solid #64748b;
                border-radius: 12px;
                padding-top: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.2 #f1f5f9, stop:0.5 #e2e8f0,
                    stop:0.8 #cbd5e1, stop:1 #94a3b8);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
                           inset 0 1px 0 rgba(255, 255, 255, 0.8);
                min-height: 160px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border-radius: 10px;
                border: 2px solid #000000;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                font-size: 13px;
            }
        """)
        info_group.setTitle("💡 نصائح الاستخدام والإعدادات")
        info_group.setMinimumHeight(160)

        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(6)
        info_layout.setContentsMargins(15, 20, 15, 12)

        # نصائح الإعدادات العامة
        general_tips = [
            "⚙️ احفظ الإعدادات بانتظام لتجنب فقدان التخصيصات",
            "🔄 أعد تشغيل البرنامج بعد تغيير الإعدادات المهمة",
            "📋 راجع معلومات التطبيق للتأكد من الإصدار الحالي",
            "💾 قم بعمل نسخة احتياطية من الإعدادات قبل التحديث"
        ]

        for tip in general_tips:
            tip_label = QLabel(tip)
            tip_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
            tip_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 6px;
                    margin: 2px 0px;
                    min-height: 25px;
                    max-height: 40px;
                    font-weight: bold;
                    font-size: 12px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            tip_label.setWordWrap(True)
            tip_label.setMinimumHeight(30)
            tip_label.setMaximumHeight(40)
            tip_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            tip_label.setAlignment(Qt.AlignCenter)
            info_layout.addWidget(tip_label)

        layout.addWidget(info_group)

        # إضافة مساحة للتمدد مع تحسين التوزيع
        layout.addStretch(1)

        self.tabs.addTab(tab, "⚙️ الإعدادات العامة")

    def show_basic_info(self):
        """عرض معلومات الأنظمة الأساسية"""
        if hasattr(self, 'status_labels'):
            # تحديث حالة الأنظمة الأساسية
            if BASIC_SYSTEMS_AVAILABLE:
                self.status_labels.get('config', QLabel()).setText("✅ يعمل")
                self.status_labels.get('config', QLabel()).setStyleSheet("color: #10b981;")
                self.status_labels.get('logging', QLabel()).setText("✅ يعمل")
                self.status_labels.get('logging', QLabel()).setStyleSheet("color: #10b981;")

            # الأنظمة المتقدمة غير متاحة
            for key in ['security', 'permissions', 'backup', 'performance']:
                if key in self.status_labels:
                    self.status_labels[key].setText("⚠️ غير متاح")
                    self.status_labels[key].setStyleSheet("color: #f59e0b;")

    def start_status_monitoring(self):
        """بدء مراقبة حالة الأنظمة"""
        self.status_thread = SystemStatusThread()
        self.status_thread.status_updated.connect(self.update_system_status)
        self.status_thread.start()
    
    def update_system_status(self, status):
        """تحديث حالة الأنظمة"""
        if not status.get('available', False):
            return
        
        initialized = status.get('initialized_systems', [])
        failed = status.get('failed_systems', [])
        
        for system_key, label in self.status_labels.items():
            if system_key in initialized:
                label.setText("✅ يعمل")
                label.setStyleSheet("color: #10b981;")
            elif system_key in failed:
                label.setText("❌ فشل")
                label.setStyleSheet("color: #ef4444;")
            else:
                label.setText("⏳ غير معروف")
                label.setStyleSheet("color: #f59e0b;")
    
    def refresh_system_status(self):
        """تحديث حالة الأنظمة يدوياً"""
        try:
            if ADVANCED_SYSTEMS_AVAILABLE:
                try:
                    status = get_systems_status()
                    self.update_system_status(status)

                    # تحديث عرض المعلومات في الواجهة
                    if hasattr(self, 'system_status_labels'):
                        for label_name, label in self.system_status_labels.items():
                            if label_name in status.get('initialized_systems', []):
                                label.setText("✅ يعمل")
                                label.setStyleSheet("color: #10b981; font-weight: bold;")
                            elif label_name in status.get('failed_systems', []):
                                label.setText("❌ معطل")
                                label.setStyleSheet("color: #ef4444; font-weight: bold;")
                            else:
                                label.setText("⏳ غير معروف")
                                label.setStyleSheet("color: #f59e0b; font-weight: bold;")

                    # استخدام نافذة النتائج المحسنة
                    details = [
                        f"📊 الأنظمة العاملة: {len(status.get('initialized_systems', []))}",
                        f"❌ الأنظمة المعطلة: {len(status.get('failed_systems', []))}",
                        f"📈 معدل النجاح: {status.get('success_rate', 0):.1f}%",
                        "",
                        "🔍 تفاصيل الأنظمة العاملة:"
                    ]

                    for system in status.get('initialized_systems', []):
                        details.append(f"  ✅ {system}")

                    if status.get('failed_systems', []):
                        details.append("")
                        details.append("⚠️ الأنظمة المعطلة:")
                        for system in status.get('failed_systems', []):
                            details.append(f"  ❌ {system}")

                    dialog = SettingsResultDialog(
                        parent=self,
                        title="تحديث حالة الأنظمة",
                        message="✅ تم تحديث حالة الأنظمة بنجاح!\n\n"
                               "📊 يمكنك مراجعة التفاصيل أدناه للتأكد من سلامة جميع الأنظمة.",
                        result_type="success",
                        details=details
                    )
                    dialog.exec_()

                    if BASIC_SYSTEMS_AVAILABLE:
                        log_info("تم تحديث حالة الأنظمة يدوياً")

                except Exception as e:
                    dialog = SettingsResultDialog(
                        parent=self,
                        title="خطأ في تحديث الحالة",
                        message="❌ حدث خطأ أثناء تحديث حالة الأنظمة",
                        result_type="error",
                        details=[
                            f"🔍 تفاصيل الخطأ: {str(e)}",
                            "",
                            "🔧 الحلول المقترحة:",
                            "  🔄 أعد المحاولة مرة أخرى",
                            "  🛠️ تحقق من تشغيل الأنظمة المتقدمة",
                            "  📦 تأكد من تثبيت المتطلبات",
                            "  💻 أعد تشغيل البرنامج"
                        ]
                    )
                    dialog.exec_()
                    if BASIC_SYSTEMS_AVAILABLE:
                        log_error(f"خطأ في تحديث حالة الأنظمة: {e}")

            elif BASIC_SYSTEMS_AVAILABLE:
                self.show_basic_info()
                # استخدام نافذة النتائج المحسنة للأنظمة الأساسية
                dialog = SettingsResultDialog(
                    parent=self,
                    title="تحديث حالة الأنظمة الأساسية",
                    message="✅ تم تحديث حالة الأنظمة الأساسية بنجاح!\n\n"
                           "📋 الأنظمة الأساسية تعمل بشكل مستمر وموثوق.",
                    result_type="success",
                    details=[
                        "📁 نظام الإعدادات المركزية - يعمل",
                        "📝 نظام السجلات الذكي - يعمل",
                        "",
                        "💡 لتفعيل المزيد من الأنظمة المتقدمة:",
                        "pip install schedule cryptography bcrypt"
                    ]
                )
                dialog.exec_()
                log_info("تم تحديث حالة الأنظمة الأساسية")
            else:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="لا توجد أنظمة متاحة",
                    message="❌ لا توجد أنظمة متاحة للتحديث",
                    result_type="warning",
                    details=[
                        "🔍 المشكلة:",
                        "  📦 الأنظمة المتقدمة غير مثبتة",
                        "  ⚙️ الأنظمة الأساسية غير متاحة",
                        "",
                        "💡 الحلول:",
                        "  📦 قم بتثبيت المتطلبات:",
                        "     pip install schedule cryptography bcrypt",
                        "  🔄 أعد تشغيل البرنامج بعد التثبيت",
                        "  🛠️ تحقق من إعدادات النظام",
                        "",
                        "📋 الأنظمة المتقدمة المتاحة بعد التثبيت:",
                        "  💾 النسخ الاحتياطي التلقائي",
                        "  🔐 إدارة الصلاحيات المتقدمة",
                        "  🛡️ الأمان والتشفير",
                        "  📊 مراقبة الأداء المتقدمة"
                    ]
                )
                dialog.exec_()
        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ حرج في تحديث الحالة",
                message="❌ حدث خطأ حرج أثناء تحديث حالة الأنظمة",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🚨 هذا خطأ حرج يتطلب تدخل فوري:",
                    "  💻 أعد تشغيل البرنامج",
                    "  👤 تشغيل البرنامج كمدير",
                    "  🔧 تحقق من سلامة ملفات البرنامج",
                    "  📞 اتصل بالدعم الفني إذا استمر الخطأ",
                    "",
                    "📝 معلومات للدعم الفني:",
                    f"  🔍 الخطأ: {str(e)[:100]}...",
                    "  📍 الموقع: refresh_system_status",
                    "  🕒 الوقت: الآن"
                ]
            )
            dialog.exec_()
            print(f"❌ خطأ حرج في refresh_system_status: {e}")

    def test_systems(self):
        """اختبار الأنظمة"""
        try:
            if ADVANCED_SYSTEMS_AVAILABLE:
                # إجراء اختبار فعلي للأنظمة
                test_results = []

                try:
                    # اختبار حالة الأنظمة
                    status = get_systems_status()
                    if status.get('available', False):
                        test_results.append("✅ نظام الحالة: يعمل بشكل صحيح")
                    else:
                        test_results.append("❌ نظام الحالة: غير متاح")
                except Exception as e:
                    test_results.append(f"❌ نظام الحالة: خطأ - {e}")

                # اختبار نظام السجلات
                try:
                    if BASIC_SYSTEMS_AVAILABLE:
                        log_info("اختبار نظام السجلات")
                        test_results.append("✅ نظام السجلات: يعمل بشكل صحيح")
                    else:
                        test_results.append("❌ نظام السجلات: غير متاح")
                except Exception as e:
                    test_results.append(f"❌ نظام السجلات: خطأ - {e}")

                # اختبار نظام النسخ الاحتياطي
                try:
                    if hasattr(backup_manager, 'get_backup_list'):
                        backup_manager.get_backup_list()
                        test_results.append("✅ نظام النسخ الاحتياطي: يعمل بشكل صحيح")
                    else:
                        test_results.append("⚠️ نظام النسخ الاحتياطي: غير مكتمل")
                except:
                    test_results.append("❌ نظام النسخ الاحتياطي: غير متاح")

                # اختبار نظام الأداء
                try:
                    if hasattr(performance_monitor, 'get_performance_report'):
                        performance_monitor.get_performance_report(1)
                        test_results.append("✅ نظام مراقبة الأداء: يعمل بشكل صحيح")
                    else:
                        test_results.append("⚠️ نظام مراقبة الأداء: غير مكتمل")
                except:
                    test_results.append("❌ نظام مراقبة الأداء: غير متاح")

                # عرض النتائج باستخدام النافذة المحسنة
                success_count = len([r for r in test_results if r.startswith("✅")])
                warning_count = len([r for r in test_results if r.startswith("⚠️")])
                error_count = len([r for r in test_results if r.startswith("❌")])

                # تحديد نوع النتيجة
                if error_count > 0:
                    result_type = "error"
                    main_message = f"⚠️ تم اكتشاف {error_count} مشكلة في الأنظمة"
                elif warning_count > 0:
                    result_type = "warning"
                    main_message = f"⚠️ تم اكتشاف {warning_count} تحذير في الأنظمة"
                else:
                    result_type = "success"
                    main_message = "🎉 جميع الأنظمة تعمل بشكل مثالي!"

                # إضافة إحصائيات
                stats = [
                    f"📊 إحصائيات الاختبار:",
                    f"  ✅ نجح: {success_count}",
                    f"  ⚠️ تحذيرات: {warning_count}",
                    f"  ❌ أخطاء: {error_count}",
                    "",
                    "🔍 تفاصيل النتائج:"
                ]

                dialog = SettingsResultDialog(
                    parent=self,
                    title="نتائج اختبار الأنظمة",
                    message=main_message + "\n\n📋 يمكنك مراجعة التفاصيل الكاملة أدناه.",
                    result_type=result_type,
                    details=stats + test_results
                )
                dialog.exec_()

                if BASIC_SYSTEMS_AVAILABLE:
                    log_info("تم إجراء اختبار شامل للأنظمة")

            else:
                # عرض معلومات الأنظمة الأساسية
                dialog = SettingsResultDialog(
                    parent=self,
                    title="اختبار الأنظمة الأساسية",
                    message="ℹ️ الأنظمة المتقدمة غير متاحة حالياً\n\n"
                           "📋 يمكنك اختبار الأنظمة الأساسية المتاحة.",
                    result_type="info",
                    details=[
                        "✅ الأنظمة الأساسية المتاحة:",
                        "  📁 نظام الإعدادات المركزية",
                        "  📝 نظام السجلات الذكي",
                        "",
                        "💡 لتفعيل الأنظمة المتقدمة:",
                        "  pip install schedule cryptography bcrypt",
                        "",
                        "🔧 الأنظمة المتقدمة المتاحة بعد التثبيت:",
                        "  💾 نظام النسخ الاحتياطي التلقائي",
                        "  🔐 نظام إدارة الصلاحيات",
                        "  🛡️ نظام الأمان والتشفير",
                        "  📊 نظام مراقبة الأداء"
                    ]
                )
                dialog.exec_()
        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ حرج في اختبار الأنظمة",
                message="❌ حدث خطأ حرج أثناء اختبار الأنظمة",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🚨 هذا خطأ حرج يتطلب تدخل فوري:",
                    "  💻 أعد تشغيل البرنامج",
                    "  👤 تشغيل البرنامج كمدير",
                    "  🔧 تحقق من سلامة ملفات البرنامج",
                    "  📦 تأكد من تثبيت جميع المتطلبات",
                    "",
                    "📞 للمساعدة:",
                    "  📧 اتصل بالدعم الفني",
                    "  📝 أرسل تقرير الخطأ مع التفاصيل"
                ]
            )
            dialog.exec_()
            print(f"❌ خطأ حرج في test_systems: {e}")

    def restart_systems(self):
        """إعادة تشغيل الأنظمة"""
        try:
            # استخدام نافذة التأكيد المحسنة
            dialog = SettingsConfirmationDialog(
                parent=self,
                title="إعادة تشغيل الأنظمة",
                message="📋 سيتم إجراء العمليات التالية:\n\n"
                       "🔄 إيقاف جميع الأنظمة الحالية\n"
                       "🧹 تنظيف الذاكرة والموارد\n"
                       "🚀 إعادة تهيئة الأنظمة\n"
                       "📊 تحديث حالة الأنظمة\n\n"
                       "⏱️ قد تستغرق العملية بضع ثوان",
                icon="🔄",
                confirm_text="إعادة تشغيل",
                cancel_text="إلغاء",
                operation_type="warning"
            )

            if dialog.exec_() == QDialog.Accepted:
                try:
                    if ADVANCED_SYSTEMS_AVAILABLE:
                        from system_initializer import shutdown_advanced_systems, initialize_advanced_systems

                        # إنشاء نافذة تقدم محسنة
                        progress_dialog = SettingsProgressDialog(
                            parent=self,
                            title="إعادة تشغيل الأنظمة",
                            message="🔄 جاري إيقاف الأنظمة..."
                        )
                        progress_dialog.show()

                        # إيقاف الأنظمة
                        try:
                            shutdown_advanced_systems()
                            progress_dialog.update_message("✅ تم إيقاف الأنظمة\n🚀 جاري إعادة التشغيل...")

                            if BASIC_SYSTEMS_AVAILABLE:
                                log_info("تم إيقاف الأنظمة المتقدمة لإعادة التشغيل")
                        except Exception as e:
                            progress_dialog.update_message(f"⚠️ تحذير في الإيقاف: {e}\n🚀 جاري إعادة التشغيل...")

                        # إعادة تشغيل الأنظمة
                        try:
                            success = initialize_advanced_systems()
                            progress_dialog.close()

                            if success:
                                # تحديث حالة الأنظمة
                                self.refresh_system_status()

                                # استخدام نافذة النتائج المحسنة للنجاح
                                dialog = SettingsResultDialog(
                                    parent=self,
                                    title="نجح إعادة تشغيل الأنظمة",
                                    message="🎉 تم إعادة تشغيل جميع الأنظمة بنجاح!\n\n"
                                           "✅ جميع الأنظمة تعمل بشكل طبيعي وتم تحديث حالتها تلقائياً.",
                                    result_type="success",
                                    details=[
                                        "🔄 العمليات المنجزة:",
                                        "  ✅ إيقاف الأنظمة القديمة",
                                        "  ✅ تنظيف الذاكرة والموارد",
                                        "  ✅ إعادة تهيئة الأنظمة",
                                        "  ✅ تحديث حالة الأنظمة",
                                        "",
                                        "📊 النتائج:",
                                        "  🎯 جميع الأنظمة تعمل بكفاءة عالية",
                                        "  🚀 تم تحسين الأداء",
                                        "  🔒 تم تأمين الاتصالات"
                                    ]
                                )
                                dialog.exec_()

                                if BASIC_SYSTEMS_AVAILABLE:
                                    log_info("تم إعادة تشغيل الأنظمة المتقدمة بنجاح")
                            else:
                                dialog = SettingsResultDialog(
                                    parent=self,
                                    title="تحذير - إعادة تشغيل مع قيود",
                                    message="⚠️ تم إعادة تشغيل الأنظمة مع بعض القيود",
                                    result_type="warning",
                                    details=[
                                        "⚠️ القيود المطبقة:",
                                        "  🔧 بعض الأنظمة قد لا تعمل بكامل طاقتها",
                                        "  📦 بعض المتطلبات غير متوفرة",
                                        "  ⚙️ إعدادات محدودة",
                                        "",
                                        "🔧 للحصول على الأداء الكامل:",
                                        "  📦 قم بتثبيت جميع المتطلبات:",
                                        "     pip install schedule cryptography bcrypt",
                                        "  ⚙️ فحص الإعدادات والتكوين",
                                        "  🔄 إعادة تشغيل النظام بعد التثبيت"
                                    ]
                                )
                                dialog.exec_()

                                if BASIC_SYSTEMS_AVAILABLE:
                                    log_warning("إعادة تشغيل الأنظمة مع قيود")
                        except Exception as e:
                            progress_dialog.close()
                            dialog = SettingsResultDialog(
                                parent=self,
                                title="خطأ في إعادة تشغيل الأنظمة",
                                message="❌ حدث خطأ أثناء إعادة تشغيل الأنظمة",
                                result_type="error",
                                details=[
                                    f"🔍 تفاصيل الخطأ: {str(e)}",
                                    "",
                                    "🔧 الحلول المقترحة:",
                                    "  🔄 أعد المحاولة مرة أخرى",
                                    "  💻 أعد تشغيل البرنامج",
                                    "  👤 تشغيل البرنامج كمدير",
                                    "  📦 تحقق من تثبيت المتطلبات",
                                    "",
                                    "📝 معلومات إضافية:",
                                    "  🕒 الوقت: الآن",
                                    "  📍 الموقع: restart_systems",
                                    "  🔍 نوع الخطأ: خطأ في العملية"
                                ]
                            )
                            dialog.exec_()
                            if BASIC_SYSTEMS_AVAILABLE:
                                log_error(f"خطأ في إعادة تشغيل الأنظمة: {e}")
                    else:
                        # استخدام نافذة النتائج المحسنة للأنظمة الأساسية
                        dialog = SettingsResultDialog(
                            parent=self,
                            title="معلومات إعادة التشغيل",
                            message="ℹ️ الأنظمة الأساسية لا تحتاج إعادة تشغيل\n\n"
                                   "📋 هذه الأنظمة تعمل بشكل مستمر وموثوق.",
                            result_type="info",
                            details=[
                                "✅ الأنظمة الأساسية العاملة:",
                                "  📁 نظام الإعدادات المركزية",
                                "  📝 نظام السجلات الذكي",
                                "",
                                "💡 لإعادة تشغيل الأنظمة المتقدمة:",
                                "  1. قم بتثبيت المتطلبات:",
                                "     pip install schedule cryptography bcrypt",
                                "  2. أعد تشغيل البرنامج",
                                "  3. ستصبح الأنظمة المتقدمة متاحة",
                                "",
                                "🔧 الأنظمة المتقدمة المتاحة:",
                                "  💾 النسخ الاحتياطي التلقائي",
                                "  🔐 إدارة الصلاحيات",
                                "  🛡️ الأمان والتشفير",
                                "  📊 مراقبة الأداء"
                            ]
                        )
                        dialog.exec_()

                except Exception as e:
                    dialog = SettingsResultDialog(
                        parent=self,
                        title="خطأ حرج في إعادة تشغيل الأنظمة",
                        message="❌ حدث خطأ حرج أثناء إعادة تشغيل الأنظمة",
                        result_type="error",
                        details=[
                            f"🔍 تفاصيل الخطأ: {str(e)}",
                            "",
                            "🚨 هذا خطأ حرج يتطلب تدخل فوري:",
                            "  💻 أعد تشغيل البرنامج فوراً",
                            "  👤 تشغيل البرنامج كمدير",
                            "  🔧 تحقق من سلامة ملفات النظام",
                            "  📦 أعد تثبيت المتطلبات",
                            "",
                            "📞 للمساعدة العاجلة:",
                            "  📧 اتصل بالدعم الفني فوراً",
                            "  📝 أرسل تقرير الخطأ الكامل"
                        ]
                    )
                    dialog.exec_()
                    if BASIC_SYSTEMS_AVAILABLE:
                        log_error(f"خطأ حرج في إعادة تشغيل الأنظمة: {e}")
        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ حرج في restart_systems",
                message="❌ حدث خطأ حرج في وظيفة إعادة تشغيل الأنظمة",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🚨 خطأ حرج في النظام الأساسي:",
                    "  💻 أعد تشغيل الجهاز",
                    "  🔧 تحقق من سلامة التثبيت",
                    "  📦 أعد تثبيت البرنامج إذا لزم الأمر",
                    "",
                    "📝 معلومات تقنية:",
                    f"  🔍 الخطأ: {str(e)[:100]}...",
                    "  📍 الموقع: restart_systems (main)",
                    "  🕒 الوقت: الآن"
                ]
            )
            dialog.exec_()
            print(f"❌ خطأ حرج في restart_systems: {e}")

    def cleanup_system(self):
        """تنظيف النظام"""
        try:
            # استخدام نافذة التأكيد المحسنة
            dialog = SettingsConfirmationDialog(
                parent=self,
                title="تنظيف النظام",
                message="🧹 سيتم تنظيف النظام من:\n\n"
                       "📝 السجلات القديمة (أكثر من 7 أيام)\n"
                       "📁 الملفات المؤقتة\n"
                       "💾 ذاكرة التخزين المؤقت\n"
                       "🗄️ النسخ الاحتياطية القديمة (أكثر من 30 يوم)\n"
                       "🗑️ ملفات النظام غير المستخدمة\n\n"
                       "⚠️ تحذير: لا يمكن التراجع عن هذه العملية\n"
                       "💡 يُنصح بإنشاء نسخة احتياطية قبل التنظيف",
                icon="🧹",
                confirm_text="تنظيف",
                cancel_text="إلغاء",
                operation_type="warning"
            )

            if dialog.exec_() == QDialog.Accepted:
                try:
                    cleaned_items = []
                    total_size_cleaned = 0

                    # إنشاء نافذة تقدم محسنة
                    progress_dialog = SettingsProgressDialog(
                        parent=self,
                        title="تنظيف النظام",
                        message="🧹 جاري تنظيف النظام..."
                    )
                    progress_dialog.show()

                    # تنظيف السجلات القديمة
                    try:
                        progress_dialog.update_message("🧹 جاري تنظيف السجلات القديمة...")

                        if BASIC_SYSTEMS_AVAILABLE:
                            logs_dir = Path("logs")
                            if logs_dir.exists():
                                old_size = sum(f.stat().st_size for f in logs_dir.glob("*.log*") if f.is_file())
                                smart_logger.cleanup_old_logs(days_to_keep=7)
                                new_size = sum(f.stat().st_size for f in logs_dir.glob("*.log*") if f.is_file())
                                size_cleaned = old_size - new_size
                                total_size_cleaned += size_cleaned
                                cleaned_items.append(f"✅ السجلات القديمة ({size_cleaned/1024:.1f} KB)")
                            else:
                                cleaned_items.append("ℹ️ السجلات القديمة (لا توجد)")
                        else:
                            cleaned_items.append("⚠️ السجلات القديمة (نظام السجلات غير متاح)")
                    except Exception as e:
                        cleaned_items.append(f"❌ السجلات القديمة (خطأ: {str(e)[:30]}...)")

                    # تنظيف الملفات المؤقتة
                    try:
                        progress_dialog.update_message("🧹 جاري تنظيف الملفات المؤقتة...")

                        temp_dir = Path("temp")
                        if temp_dir.exists():
                            import shutil
                            # حساب الحجم قبل الحذف
                            temp_size = sum(f.stat().st_size for f in temp_dir.rglob("*") if f.is_file())
                            shutil.rmtree(temp_dir)
                            temp_dir.mkdir()
                            total_size_cleaned += temp_size
                            cleaned_items.append(f"✅ الملفات المؤقتة ({temp_size/1024:.1f} KB)")
                        else:
                            temp_dir.mkdir(exist_ok=True)
                            cleaned_items.append("ℹ️ الملفات المؤقتة (لا توجد)")
                    except Exception as e:
                        cleaned_items.append(f"❌ الملفات المؤقتة (خطأ: {str(e)[:30]}...)")

                    # تنظيف ذاكرة التخزين المؤقت
                    try:
                        progress_dialog.update_message("🧹 جاري تنظيف ذاكرة التخزين المؤقت...")

                        cache_dir = Path("cache")
                        if cache_dir.exists():
                            cache_size = 0
                            files_count = 0
                            for cache_file in cache_dir.glob("*"):
                                if cache_file.is_file():
                                    cache_size += cache_file.stat().st_size
                                    cache_file.unlink()
                                    files_count += 1
                            total_size_cleaned += cache_size
                            cleaned_items.append(f"✅ ذاكرة التخزين المؤقت ({files_count} ملف، {cache_size/1024:.1f} KB)")
                        else:
                            cache_dir.mkdir(exist_ok=True)
                            cleaned_items.append("ℹ️ ذاكرة التخزين المؤقت (لا توجد)")
                    except Exception as e:
                        cleaned_items.append(f"❌ ذاكرة التخزين المؤقت (خطأ: {str(e)[:30]}...)")

                    # تنظيف النسخ الاحتياطية القديمة
                    try:
                        progress_dialog.update_message("🧹 جاري تنظيف النسخ الاحتياطية القديمة...")

                        if ADVANCED_SYSTEMS_AVAILABLE:
                            backup_dir = Path("backups")
                            if backup_dir.exists():
                                old_backup_size = sum(f.stat().st_size for f in backup_dir.glob("*") if f.is_file())
                                backup_manager.cleanup_old_backups(days_to_keep=30)
                                new_backup_size = sum(f.stat().st_size for f in backup_dir.glob("*") if f.is_file())
                                backup_cleaned = old_backup_size - new_backup_size
                                total_size_cleaned += backup_cleaned
                                cleaned_items.append(f"✅ النسخ الاحتياطية القديمة ({backup_cleaned/1024/1024:.1f} MB)")
                            else:
                                cleaned_items.append("ℹ️ النسخ الاحتياطية القديمة (لا توجد)")
                        else:
                            cleaned_items.append("⚠️ النسخ الاحتياطية القديمة (نظام النسخ غير متاح)")
                    except Exception as e:
                        cleaned_items.append(f"❌ النسخ الاحتياطية القديمة (خطأ: {str(e)[:30]}...)")

                    # تنظيف ملفات إضافية
                    try:
                        progress_dialog.update_message("🧹 جاري تنظيف ملفات النظام الإضافية...")

                        # تنظيف ملفات .pyc
                        pyc_size = 0
                        pyc_count = 0
                        for pyc_file in Path(".").rglob("*.pyc"):
                            pyc_size += pyc_file.stat().st_size
                            pyc_file.unlink()
                            pyc_count += 1

                        if pyc_count > 0:
                            total_size_cleaned += pyc_size
                            cleaned_items.append(f"✅ ملفات Python المؤقتة ({pyc_count} ملف، {pyc_size/1024:.1f} KB)")
                        else:
                            cleaned_items.append("ℹ️ ملفات Python المؤقتة (لا توجد)")
                    except Exception as e:
                        cleaned_items.append(f"❌ ملفات النظام الإضافية (خطأ: {str(e)[:30]}...)")

                    progress_dialog.close()

                    # عرض النتائج باستخدام النافذة المحسنة
                    success_count = len([item for item in cleaned_items if item.startswith("✅")])
                    warning_count = len([item for item in cleaned_items if item.startswith("⚠️")])
                    error_count = len([item for item in cleaned_items if item.startswith("❌")])
                    info_count = len([item for item in cleaned_items if item.startswith("ℹ️")])

                    # تحديد نوع النتيجة
                    if error_count > 0:
                        result_type = "warning"
                        main_message = f"⚠️ تم تنظيف النظام مع {error_count} تحذير"
                    else:
                        result_type = "success"
                        main_message = "🎉 تم تنظيف النظام بنجاح!"

                    # إعداد التفاصيل
                    details = [
                        "📊 إحصائيات التنظيف:",
                        f"  ✅ العمليات الناجحة: {success_count}",
                        f"  ⚠️ التحذيرات: {warning_count}",
                        f"  ❌ الأخطاء: {error_count}",
                        f"  ℹ️ معلومات: {info_count}",
                        f"  💾 المساحة المحررة: {total_size_cleaned/1024/1024:.2f} MB",
                        "",
                        "🔍 تفاصيل العمليات:"
                    ] + cleaned_items

                    dialog = SettingsResultDialog(
                        parent=self,
                        title="نتائج تنظيف النظام",
                        message=main_message + f"\n\n💾 تم تحرير {total_size_cleaned/1024/1024:.2f} MB من المساحة",
                        result_type=result_type,
                        details=details
                    )
                    dialog.exec_()

                    if BASIC_SYSTEMS_AVAILABLE:
                        log_info(f"تم تنظيف النظام - مساحة محررة: {total_size_cleaned/1024/1024:.2f} MB")

                except Exception as e:
                    dialog = SettingsResultDialog(
                        parent=self,
                        title="خطأ حرج في تنظيف النظام",
                        message="❌ حدث خطأ حرج أثناء تنظيف النظام",
                        result_type="error",
                        details=[
                            f"🔍 تفاصيل الخطأ: {str(e)}",
                            "",
                            "🚨 هذا خطأ حرج في عملية التنظيف:",
                            "  🛑 توقفت عملية التنظيف",
                            "  💾 قد تكون بعض الملفات لم تُحذف",
                            "  🔒 مشكلة في الصلاحيات أو الوصول",
                            "",
                            "🔧 الإجراءات المطلوبة:",
                            "  👤 تشغيل البرنامج كمدير",
                            "  🔄 إعادة المحاولة",
                            "  🧹 تنظيف يدوي إذا لزم الأمر",
                            "  📞 اتصل بالدعم الفني"
                        ]
                    )
                    dialog.exec_()
                    if BASIC_SYSTEMS_AVAILABLE:
                        log_error(f"خطأ حرج في تنظيف النظام: {e}")
        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ حرج في cleanup_system",
                message="❌ حدث خطأ حرج في وظيفة تنظيف النظام",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🚨 خطأ حرج في النظام الأساسي:",
                    "  💻 مشكلة في النظام الأساسي",
                    "  🔧 قد يحتاج البرنامج لإعادة تثبيت",
                    "  📦 تحقق من سلامة الملفات",
                    "",
                    "📝 معلومات تقنية:",
                    f"  🔍 الخطأ: {str(e)[:100]}...",
                    "  📍 الموقع: cleanup_system (main)",
                    "  🕒 الوقت: الآن"
                ]
            )
            dialog.exec_()
            print(f"❌ خطأ حرج في cleanup_system: {e}")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton, QLabel
        from PyQt5.QtCore import Qt

        class ChangePasswordDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setup_ui()

            def setup_ui(self):
                """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
                self.setWindowTitle("🔑 تغيير كلمة المرور - نظام إدارة الإعدادات المتطور والشامل")
                self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
                self.customize_title_bar()
                self.setModal(True)
                self.resize(500, 420)

                # خلفية النافذة مطابقة لنوافذ البرنامج
                self.setStyleSheet("""
                    QDialog {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                            stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                            stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                            stop:0.9 #6D28D9, stop:1 #5B21B6);
                        border: none;
                        border-radius: 15px;
                    }
                """)

                layout = QVBoxLayout(self)
                layout.setContentsMargins(20, 20, 20, 20)
                layout.setSpacing(12)

                # عنوان النافذة الداخلي
                title_label = QLabel("🔑 تغيير كلمة المرور")
                title_label.setAlignment(Qt.AlignCenter)
                title_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 16px;
                        font-weight: bold;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 rgba(34, 197, 94, 0.2),
                            stop:0.5 rgba(16, 185, 129, 0.3),
                            stop:1 rgba(5, 150, 105, 0.2));
                        border: 2px solid rgba(34, 197, 94, 0.5);
                        border-radius: 8px;
                        padding: 6px;
                        margin: 3px 0;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }
                """)
                layout.addWidget(title_label)



                # حقول الإدخال
                # كلمة المرور الحالية
                current_label = QLabel("🔒 كلمة المرور الحالية:")
                current_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: bold;
                        margin: 8px 0 4px 0;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
                    }
                """)
                layout.addWidget(current_label)

                self.current_password = QLineEdit()
                self.current_password.setEchoMode(QLineEdit.Password)
                self.current_password.setPlaceholderText("أدخل كلمة المرور الحالية")
                self.current_password.setStyleSheet("""
                    QLineEdit {
                        background: rgba(255, 255, 255, 0.95);
                        border: 2px solid rgba(59, 130, 246, 0.4);
                        border-radius: 8px;
                        padding: 12px 15px;
                        font-size: 14px;
                        color: #1e293b;
                        min-height: 20px;
                    }
                    QLineEdit:focus {
                        border-color: rgba(34, 197, 94, 0.7);
                        background: rgba(255, 255, 255, 1);
                        box-shadow: 0 0 8px rgba(34, 197, 94, 0.3);
                    }
                    QLineEdit:hover {
                        border-color: rgba(59, 130, 246, 0.6);
                    }
                """)
                layout.addWidget(self.current_password)

                # كلمة المرور الجديدة
                new_label = QLabel("🔑 كلمة المرور الجديدة:")
                new_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: bold;
                        margin: 8px 0 4px 0;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
                    }
                """)
                layout.addWidget(new_label)

                self.new_password = QLineEdit()
                self.new_password.setEchoMode(QLineEdit.Password)
                self.new_password.setPlaceholderText("أدخل كلمة المرور الجديدة")
                self.new_password.setStyleSheet("""
                    QLineEdit {
                        background: rgba(255, 255, 255, 0.95);
                        border: 2px solid rgba(59, 130, 246, 0.4);
                        border-radius: 8px;
                        padding: 12px 15px;
                        font-size: 14px;
                        color: #1e293b;
                        min-height: 20px;
                    }
                    QLineEdit:focus {
                        border-color: rgba(34, 197, 94, 0.7);
                        background: rgba(255, 255, 255, 1);
                        box-shadow: 0 0 8px rgba(34, 197, 94, 0.3);
                    }
                    QLineEdit:hover {
                        border-color: rgba(59, 130, 246, 0.6);
                    }
                """)
                layout.addWidget(self.new_password)

                # تأكيد كلمة المرور
                confirm_label = QLabel("✅ تأكيد كلمة المرور:")
                confirm_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: bold;
                        margin: 8px 0 4px 0;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
                    }
                """)
                layout.addWidget(confirm_label)

                self.confirm_password = QLineEdit()
                self.confirm_password.setEchoMode(QLineEdit.Password)
                self.confirm_password.setPlaceholderText("تأكيد كلمة المرور الجديدة")
                self.confirm_password.setStyleSheet("""
                    QLineEdit {
                        background: rgba(255, 255, 255, 0.95);
                        border: 2px solid rgba(59, 130, 246, 0.4);
                        border-radius: 8px;
                        padding: 12px 15px;
                        font-size: 14px;
                        color: #1e293b;
                        min-height: 20px;
                    }
                    QLineEdit:focus {
                        border-color: rgba(34, 197, 94, 0.7);
                        background: rgba(255, 255, 255, 1);
                        box-shadow: 0 0 8px rgba(34, 197, 94, 0.3);
                    }
                    QLineEdit:hover {
                        border-color: rgba(59, 130, 246, 0.6);
                    }
                """)
                layout.addWidget(self.confirm_password)

                # سؤال التأكيد
                question_label = QLabel("🔐 متأكد من تغيير كلمة المرور؟")
                question_label.setAlignment(Qt.AlignCenter)
                question_label.setStyleSheet("""
                    QLabel {
                        color: #fbbf24;
                        font-size: 14px;
                        font-weight: bold;
                        margin: 6px 0;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }
                """)
                layout.addWidget(question_label)

                # الأزرار
                buttons_layout = QHBoxLayout()
                buttons_layout.setSpacing(15)
                buttons_layout.setContentsMargins(0, 15, 0, 0)

                cancel_button = QPushButton("❌ إلغاء")
                self.style_advanced_button(cancel_button, 'info')
                cancel_button.clicked.connect(self.reject)

                confirm_button = QPushButton("🔑 تغيير")
                self.style_advanced_button(confirm_button, 'danger')
                confirm_button.clicked.connect(self.accept)

                buttons_layout.addWidget(cancel_button)
                buttons_layout.addWidget(confirm_button)
                layout.addLayout(buttons_layout)

            def customize_title_bar(self):
                """تخصيص شريط العنوان مطابق لنوافذ البرنامج"""
                try:
                    from ui.title_bar_utils import TitleBarStyler
                    TitleBarStyler.apply_advanced_title_bar_styling(self)
                except Exception as e:
                    pass

            def style_advanced_button(self, button, button_type):
                """تطبيق تصميم متطور على الأزرار مطابق لقسم العملاء"""
                try:
                    # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق لقسم العملاء
                    colors = {
                        'info': {
                            'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                            'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                            'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                            'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                            'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                        },
                        'danger': {
                            'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                            'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                            'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                            'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                            'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                        }
                    }

                    color_scheme = colors.get(button_type, colors['info'])

                    button.setStyleSheet(f"""
                        QPushButton {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {color_scheme['bg_start']}, stop:0.3 {color_scheme['bg_mid']},
                                stop:0.7 {color_scheme['bg_end']}, stop:1 {color_scheme['bg_bottom']});
                            color: {color_scheme['text']};
                            border: 2px solid {color_scheme['border']};
                            border-radius: 8px;
                            padding: 20px 35px;
                            font-size: 18px;
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        }}
                        QPushButton:hover {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {color_scheme['hover_start']}, stop:0.3 {color_scheme['hover_mid']},
                                stop:0.7 {color_scheme['hover_end']}, stop:1 {color_scheme['hover_bottom']});
                            border-color: {color_scheme['hover_border']};
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px {color_scheme['shadow']};
                        }}
                        QPushButton:pressed {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {color_scheme['pressed_start']}, stop:0.3 {color_scheme['pressed_mid']},
                                stop:0.7 {color_scheme['pressed_end']}, stop:1 {color_scheme['pressed_bottom']});
                            border-color: {color_scheme['pressed_border']};
                            transform: translateY(0px);
                            box-shadow: 0 2px 8px {color_scheme['shadow']};
                        }}
                    """)
                except Exception as e:
                    print(f"خطأ في تطبيق تصميم الزر: {e}")

    def get_database_tables_info(self):
        """الحصول على معلومات جداول قاعدة البيانات"""
        try:
            if not BASIC_SYSTEMS_AVAILABLE:
                return []

            db_path = DatabaseConfig.DB_PATH
            if not db_path.exists():
                return []

            import sqlite3
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            tables_info = []
            for table in tables:
                table_name = table[0]
                if table_name != 'sqlite_sequence':  # تجاهل جداول النظام
                    # عدد الصفوف في كل جدول
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]

                    # ترجمة أسماء الجداول - جميع الجداول الـ 31
                    table_translations = {
                        # البيانات الأساسية
                        'clients': '👥 العملاء',
                        'client_phones': '📞 هواتف العملاء',
                        'suppliers': '🏢 الموردين',
                        'supplier_phones': '📞 هواتف الموردين',
                        'employees': '👷 العمال',
                        'employee_phones': '📞 هواتف العمال',

                        # الرواتب والأجور
                        'salaries': '💰 الرواتب',
                        'daily_wages': '💵 الأجور اليومية',
                        'daily_wage_notes': '📝 ملاحظات الأجور',

                        # المشاريع والعقارات
                        'projects': '🏢 المشاريع',
                        'project_expenses': '💸 مصروفات المشاريع',
                        'project_materials': '🧱 مواد المشاريع',
                        'properties': '🏠 العقارات',
                        'property_documents': '📄 وثائق العقارات',

                        # المخزون والتجارة
                        'inventory': '📦 المخزون',
                        'purchases': '🛒 المشتريات',
                        'purchase_items': '📋 عناصر المشتريات',
                        'sales': '🛍️ المبيعات',
                        'sale_items': '📋 عناصر المبيعات',

                        # المالية
                        'expenses': '💸 المصروفات',
                        'revenues': '💰 الإيرادات',
                        'invoices': '🧾 الفواتير',
                        'invoice_items': '📋 عناصر الفواتير',
                        'installments': '💳 الأقساط',
                        'installment_items': '📋 عناصر الأقساط',

                        # الإشعارات والتنبيهات
                        'notifications': '🔔 الإشعارات',
                        'reminders': '⏰ التنبيهات',
                        'events': '📅 الأحداث',

                        # النظام والوثائق
                        'documents': '📁 الوثائق',
                        'settings': '⚙️ الإعدادات',
                        'users': '👤 المستخدمين'
                    }

                    display_name = table_translations.get(table_name, f"📋 {table_name}")
                    tables_info.append(f"  {display_name}: {row_count} سجل")

            conn.close()
            return tables_info

        except Exception as e:
            print(f"خطأ في الحصول على معلومات الجداول: {e}")
            return []

    def verify_backup_completeness(self):
        """التحقق من اكتمال النسخة الاحتياطية"""
        try:
            # قائمة جميع الجداول المطلوبة (31 جدول)
            required_tables = [
                # البيانات الأساسية
                'clients', 'client_phones', 'suppliers', 'supplier_phones',
                'employees', 'employee_phones',

                # الرواتب والأجور
                'salaries', 'daily_wages', 'daily_wage_notes',

                # المشاريع والعقارات
                'projects', 'project_expenses', 'project_materials',
                'properties', 'property_documents',

                # المخزون والتجارة
                'inventory', 'purchases', 'purchase_items',
                'sales', 'sale_items',

                # المالية
                'expenses', 'revenues', 'invoices', 'invoice_items',
                'installments', 'installment_items',

                # الإشعارات والتنبيهات
                'notifications', 'reminders', 'events',

                # النظام والوثائق
                'documents', 'settings', 'users'
            ]

            if not BASIC_SYSTEMS_AVAILABLE:
                return False, "الأنظمة الأساسية غير متاحة"

            db_path = DatabaseConfig.DB_PATH
            if not db_path.exists():
                return False, "ملف قاعدة البيانات غير موجود"

            import sqlite3
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # الحصول على قائمة الجداول الموجودة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            existing_tables = [table[0] for table in cursor.fetchall()]

            # التحقق من وجود الجداول المطلوبة
            missing_tables = []
            for table in required_tables:
                if table not in existing_tables:
                    missing_tables.append(table)

            conn.close()

            if missing_tables:
                return False, f"الجداول المفقودة: {', '.join(missing_tables)}"
            else:
                return True, "جميع الجداول الأساسية موجودة"

        except Exception as e:
            return False, f"خطأ في التحقق: {str(e)}"

    def get_backup_summary(self):
        """الحصول على ملخص شامل للبيانات المحفوظة"""
        try:
            if not BASIC_SYSTEMS_AVAILABLE:
                return []

            db_path = DatabaseConfig.DB_PATH
            if not db_path.exists():
                return []

            import sqlite3
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إحصائيات شاملة
            summary = []

            # البيانات الأساسية
            basic_tables = {
                'clients': '👥 العملاء',
                'suppliers': '🏢 الموردين',
                'employees': '👷 العمال',
                'projects': '🏗️ المشاريع',
                'properties': '🏠 العقارات'
            }

            basic_total = 0
            for table, name in basic_tables.items():
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    basic_total += count
                    if count > 0:
                        summary.append(f"  {name}: {count} سجل")
                except:
                    pass

            if basic_total > 0:
                summary.insert(0, f"📋 البيانات الأساسية ({basic_total} سجل إجمالي):")
                summary.append("")

            # البيانات المالية والرواتب
            financial_tables = {
                'revenues': '💰 الإيرادات',
                'expenses': '💸 المصروفات',
                'invoices': '🧾 الفواتير',
                'invoice_items': '📋 عناصر الفواتير',
                'installments': '💳 الأقساط',
                'installment_items': '📋 عناصر الأقساط',
                'daily_wages': '💵 الأجور اليومية',
                'daily_wage_notes': '📝 ملاحظات الأجور',
                'salaries': '💰 الرواتب'
            }

            financial_total = 0
            financial_details = []
            for table, name in financial_tables.items():
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    financial_total += count
                    if count > 0:
                        financial_details.append(f"  {name}: {count} سجل")
                except:
                    pass

            if financial_total > 0:
                summary.append(f"💰 البيانات المالية ({financial_total} سجل إجمالي):")
                summary.extend(financial_details)
                summary.append("")

            # بيانات التجارة والمخزون
            commerce_tables = {
                'inventory': '📦 المخزون',
                'purchases': '🛒 المشتريات',
                'sales': '🛍️ المبيعات'
            }

            commerce_total = 0
            commerce_details = []
            for table, name in commerce_tables.items():
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    commerce_total += count
                    if count > 0:
                        commerce_details.append(f"  {name}: {count} سجل")
                except:
                    pass

            if commerce_total > 0:
                summary.append(f"🛒 بيانات التجارة ({commerce_total} سجل إجمالي):")
                summary.extend(commerce_details)
                summary.append("")

            # الإشعارات والتنبيهات
            notification_tables = {
                'notifications': '🔔 الإشعارات',
                'reminders': '⏰ التنبيهات',
                'events': '📅 الأحداث'
            }

            notification_total = 0
            notification_details = []
            for table, name in notification_tables.items():
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    notification_total += count
                    if count > 0:
                        notification_details.append(f"  {name}: {count} سجل")
                except:
                    pass

            if notification_total > 0:
                summary.append(f"🔔 الإشعارات والتنبيهات ({notification_total} سجل إجمالي):")
                summary.extend(notification_details)
                summary.append("")

            # الجداول الفرعية والوثائق
            system_tables = {
                'documents': '📁 الوثائق',
                'settings': '⚙️ الإعدادات',
                'users': '👤 المستخدمين',
                'project_expenses': '💸 مصروفات المشاريع',
                'project_materials': '🧱 مواد المشاريع',
                'property_documents': '📄 وثائق العقارات'
            }

            system_total = 0
            system_details = []
            for table, name in system_tables.items():
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    system_total += count
                    if count > 0:
                        system_details.append(f"  {name}: {count} سجل")
                except:
                    pass

            if system_total > 0:
                summary.append(f"📁 النظام والوثائق ({system_total} سجل إجمالي):")
                summary.extend(system_details)
                summary.append("")

            # الجداول الفرعية للهواتف
            phone_tables = {
                'client_phones': '📞 هواتف العملاء',
                'supplier_phones': '📞 هواتف الموردين',
                'employee_phones': '📞 هواتف العمال'
            }

            phone_total = 0
            phone_details = []
            for table, name in phone_tables.items():
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    phone_total += count
                    if count > 0:
                        phone_details.append(f"  {name}: {count} سجل")
                except:
                    pass

            if phone_total > 0:
                summary.append(f"📞 أرقام الهواتف ({phone_total} سجل إجمالي):")
                summary.extend(phone_details)

            conn.close()
            return summary

        except Exception as e:
            print(f"خطأ في الحصول على ملخص النسخة الاحتياطية: {e}")
            return []

    def verify_complete_backup_data(self):
        """التحقق الشامل من أن النسخة الاحتياطية تحتوي على جميع البيانات"""
        try:
            if not BASIC_SYSTEMS_AVAILABLE:
                return False, "الأنظمة الأساسية غير متاحة"

            db_path = DatabaseConfig.DB_PATH
            if not db_path.exists():
                return False, "ملف قاعدة البيانات غير موجود"

            import sqlite3
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # فحص جميع الجداول الـ 31
            all_tables = [
                'clients', 'client_phones', 'suppliers', 'supplier_phones',
                'employees', 'employee_phones', 'salaries', 'daily_wages',
                'daily_wage_notes', 'projects', 'project_expenses', 'project_materials',
                'properties', 'property_documents', 'inventory', 'purchases',
                'purchase_items', 'sales', 'sale_items', 'expenses', 'revenues',
                'invoices', 'invoice_items', 'installments', 'installment_items',
                'notifications', 'reminders', 'events', 'documents', 'settings', 'users'
            ]

            # الحصول على قائمة الجداول الموجودة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            existing_tables = [table[0] for table in cursor.fetchall()]

            # التحقق من وجود جميع الجداول
            missing_tables = []
            existing_required_tables = []
            total_records = 0

            for table in all_tables:
                if table not in existing_tables:
                    missing_tables.append(table)
                else:
                    existing_required_tables.append(table)
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        total_records += count
                    except:
                        pass

            conn.close()

            # إعداد التقرير
            status_report = {
                'total_tables': len(all_tables),
                'existing_tables': len(existing_required_tables),
                'missing_tables': len(missing_tables),
                'total_records': total_records,
                'missing_table_names': missing_tables,
                'existing_table_names': existing_required_tables
            }

            if missing_tables:
                return False, f"الجداول المفقودة ({len(missing_tables)}): {', '.join(missing_tables[:5])}{'...' if len(missing_tables) > 5 else ''}"
            else:
                return True, f"جميع الجداول موجودة ({len(existing_required_tables)}/31) - إجمالي السجلات: {total_records}"

        except Exception as e:
            return False, f"خطأ في التحقق الشامل: {str(e)}"

    def get_detailed_backup_statistics(self):
        """الحصول على إحصائيات مفصلة للنسخة الاحتياطية"""
        try:
            if not BASIC_SYSTEMS_AVAILABLE:
                return []

            db_path = DatabaseConfig.DB_PATH
            if not db_path.exists():
                return []

            import sqlite3
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            statistics = []
            grand_total = 0

            # إحصائيات مفصلة لكل جدول
            all_tables_with_names = {
                # البيانات الأساسية
                'clients': '👥 العملاء',
                'client_phones': '📞 هواتف العملاء',
                'suppliers': '🏢 الموردين',
                'supplier_phones': '📞 هواتف الموردين',
                'employees': '👷 العمال',
                'employee_phones': '📞 هواتف العمال',

                # الرواتب والأجور
                'salaries': '💰 الرواتب',
                'daily_wages': '💵 الأجور اليومية',
                'daily_wage_notes': '📝 ملاحظات الأجور',

                # المشاريع والعقارات
                'projects': '🏢 المشاريع',
                'project_expenses': '💸 مصروفات المشاريع',
                'project_materials': '🧱 مواد المشاريع',
                'properties': '🏠 العقارات',
                'property_documents': '📄 وثائق العقارات',

                # المخزون والتجارة
                'inventory': '📦 المخزون',
                'purchases': '🛒 المشتريات',
                'purchase_items': '📋 عناصر المشتريات',
                'sales': '🛍️ المبيعات',
                'sale_items': '📋 عناصر المبيعات',

                # المالية
                'expenses': '💸 المصروفات',
                'revenues': '💰 الإيرادات',
                'invoices': '🧾 الفواتير',
                'invoice_items': '📋 عناصر الفواتير',
                'installments': '💳 الأقساط',
                'installment_items': '📋 عناصر الأقساط',

                # الإشعارات والتنبيهات
                'notifications': '🔔 الإشعارات',
                'reminders': '⏰ التنبيهات',
                'events': '📅 الأحداث',

                # النظام والوثائق
                'documents': '📁 الوثائق',
                'settings': '⚙️ الإعدادات',
                'users': '👤 المستخدمين'
            }

            # عد السجلات في كل جدول
            for table, name in all_tables_with_names.items():
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    grand_total += count
                    if count > 0:
                        statistics.append(f"  {name}: {count:,} سجل")
                    else:
                        statistics.append(f"  {name}: فارغ")
                except Exception as e:
                    statistics.append(f"  {name}: خطأ ({str(e)[:20]}...)")

            conn.close()

            # إضافة الإجمالي في البداية
            statistics.insert(0, f"📊 إجمالي السجلات في جميع الجداول: {grand_total:,} سجل")
            statistics.insert(1, f"📋 عدد الجداول: {len(all_tables_with_names)} جدول")
            statistics.insert(2, "")
            statistics.insert(3, "📈 تفاصيل كل جدول:")

            return statistics

        except Exception as e:
            return [f"❌ خطأ في الحصول على الإحصائيات: {str(e)}"]

    def create_test_database_if_missing(self):
        """إنشاء قاعدة بيانات تجريبية إذا لم تكن موجودة"""
        try:
            if not BASIC_SYSTEMS_AVAILABLE:
                return False, "الأنظمة الأساسية غير متاحة"

            db_path = DatabaseConfig.DB_PATH
            if db_path.exists():
                return True, "قاعدة البيانات موجودة"

            # محاولة إنشاء قاعدة بيانات فارغة
            import sqlite3
            conn = sqlite3.connect(str(db_path))

            # إنشاء جدول تجريبي بسيط
            conn.execute("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إدراج بيانات تجريبية
            conn.execute("INSERT INTO test_table (name) VALUES ('بيانات تجريبية')")
            conn.commit()
            conn.close()

            return True, f"تم إنشاء قاعدة بيانات تجريبية: {db_path}"

        except Exception as e:
            return False, f"فشل في إنشاء قاعدة البيانات التجريبية: {str(e)}"

        dialog = ChangePasswordDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            current = dialog.current_password.text()
            new_pass = dialog.new_password.text()
            confirm = dialog.confirm_password.text()

            if not current or not new_pass or not confirm:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في البيانات",
                    message="❌ يرجى ملء جميع الحقول المطلوبة",
                    result_type="error",
                    details=[
                        "📋 الحقول المطلوبة:",
                        "  🔒 كلمة المرور الحالية",
                        "  🔑 كلمة المرور الجديدة",
                        "  ✅ تأكيد كلمة المرور الجديدة"
                    ]
                )
                dialog.exec_()
                return

            if new_pass != confirm:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في التأكيد",
                    message="❌ كلمة المرور الجديدة غير متطابقة",
                    result_type="error",
                    details=[
                        "🔍 تحقق من:",
                        "  ✅ كتابة كلمة المرور الجديدة بشكل صحيح",
                        "  ✅ تأكيد كلمة المرور بنفس الطريقة",
                        "  💡 تأكد من عدم وجود مسافات إضافية"
                    ]
                )
                dialog.exec_()
                return

            if len(new_pass) < 6:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="كلمة مرور ضعيفة",
                    message="❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل",
                    result_type="error",
                    details=[
                        "🔐 متطلبات كلمة المرور:",
                        "  📏 الحد الأدنى: 6 أحرف",
                        "  💡 يُنصح بـ 8 أحرف أو أكثر",
                        "  🔤 استخدم مزيج من الأحرف والأرقام",
                        "  🔣 أضف رموز خاصة لمزيد من الأمان"
                    ]
                )
                dialog.exec_()
                return

            try:
                if ADVANCED_SYSTEMS_AVAILABLE:
                    # تشفير كلمة المرور الجديدة
                    hashed_password = security_manager.hash_password(new_pass)
                    # حفظ في قاعدة البيانات أو ملف الإعدادات
                    dialog = SettingsResultDialog(
                        parent=self,
                        title="نجح تغيير كلمة المرور",
                        message="✅ تم تغيير كلمة المرور بنجاح!\n\n"
                               "🔐 كلمة المرور الجديدة محفوظة بأمان.",
                        result_type="success",
                        details=[
                            "🔒 الميزات الأمنية المطبقة:",
                            "  ✅ تشفير متقدم للكلمة",
                            "  ✅ حماية ضد الاختراق",
                            "  ✅ تسجيل آمن للعملية",
                            "",
                            "💡 نصائح الأمان:",
                            "  🔄 غيّر كلمة المرور دورياً",
                            "  📝 لا تشارك كلمة المرور مع أحد",
                            "  💾 احتفظ بنسخة احتياطية آمنة"
                        ]
                    )
                    dialog.exec_()
                    if BASIC_SYSTEMS_AVAILABLE:
                        log_info("تم تغيير كلمة المرور بنجاح")
                else:
                    # حفظ بسيط بدون تشفير متقدم
                    dialog = SettingsResultDialog(
                        parent=self,
                        title="نجح تغيير كلمة المرور",
                        message="✅ تم تغيير كلمة المرور بنجاح!\n\n"
                               "💾 كلمة المرور الجديدة محفوظة.",
                        result_type="success",
                        details=[
                            "📋 تم تنفيذ:",
                            "  ✅ حفظ كلمة المرور الجديدة",
                            "  ✅ تحديث إعدادات الأمان",
                            "",
                            "💡 لمزيد من الأمان:",
                            "  🔧 قم بتثبيت المتطلبات المتقدمة:",
                            "     pip install schedule cryptography bcrypt"
                        ]
                    )
                    dialog.exec_()

            except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في تغيير كلمة المرور",
                    message=f"❌ حدث خطأ أثناء تغيير كلمة المرور",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  🔄 أعد المحاولة مرة أخرى",
                        "  📝 تأكد من صحة كلمة المرور الحالية",
                        "  💻 أعد تشغيل البرنامج إذا استمر الخطأ"
                    ]
                )
                dialog.exec_()
    
    def show_security_report(self):
        """عرض تقرير الأمان"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                report = security_manager.get_security_report()

                # تحديد نوع التقرير حسب الأنشطة المشبوهة
                suspicious_count = report.get('total_suspicious_activities', 0)
                blocked_ips = report.get('blocked_ips_count', 0)

                if suspicious_count > 5 or blocked_ips > 3:
                    result_type = "warning"
                    main_message = "⚠️ تم اكتشاف أنشطة مشبوهة"
                elif suspicious_count > 0 or blocked_ips > 0:
                    result_type = "info"
                    main_message = "ℹ️ تقرير الأمان - بعض الملاحظات"
                else:
                    result_type = "success"
                    main_message = "🛡️ الأمان ممتاز - لا توجد تهديدات"

                dialog = SettingsResultDialog(
                    parent=self,
                    title="تقرير الأمان",
                    message=main_message + "\n\n📊 يمكنك مراجعة التفاصيل الكاملة أدناه.",
                    result_type=result_type,
                    details=[
                        "📋 إحصائيات الأمان:",
                        f"  🔍 الأنشطة المشبوهة: {suspicious_count}",
                        f"  🚫 عناوين IP المحظورة: {blocked_ips}",
                        f"  📊 الأنشطة الأخيرة (24 ساعة): {report.get('recent_activities_24h', 0)}",
                        f"  🔐 محاولات الدخول الفاشلة: {report.get('failed_login_attempts', 0)}",
                        f"  ✅ عمليات الدخول الناجحة: {report.get('successful_logins', 0)}",
                        "",
                        "🛡️ حالة الأنظمة الأمنية:",
                        f"  🔒 جدار الحماية: {'نشط' if report.get('firewall_active', True) else 'معطل'}",
                        f"  🔐 التشفير: {'مفعل' if report.get('encryption_enabled', True) else 'معطل'}",
                        f"  📝 تسجيل الأحداث: {'يعمل' if report.get('logging_active', True) else 'متوقف'}"
                    ]
                )
                dialog.exec_()
            except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في تقرير الأمان",
                    message="❌ حدث خطأ أثناء إنشاء تقرير الأمان",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  🔄 أعد المحاولة مرة أخرى",
                        "  🛡️ تأكد من تشغيل نظام الأمان",
                        "  💻 أعد تشغيل البرنامج إذا استمر الخطأ"
                    ]
                )
                dialog.exec_()
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            # التحقق الشامل من اكتمال قاعدة البيانات أولاً
            is_complete, completeness_message = self.verify_complete_backup_data()

            if not ADVANCED_SYSTEMS_AVAILABLE:
                # إظهار رسالة خطأ إذا لم تكن الأنظمة المتقدمة متاحة
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في النظام",
                    message="❌ نظام النسخ الاحتياطي غير متاح",
                    result_type="error",
                    details=[
                        "🔍 الأسباب المحتملة:",
                        "  📦 ملفات النظام المتقدم مفقودة",
                        "  🔧 خطأ في التكوين",
                        "  📁 مسارات الملفات غير صحيحة",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  📥 إعادة تثبيت البرنامج",
                        "  🔄 إعادة تشغيل البرنامج",
                        "  👤 تشغيل البرنامج كمدير",
                        "  📞 الاتصال بالدعم الفني"
                    ]
                )
                dialog.exec_()
                return

            if not is_complete:
                # محاولة إنشاء قاعدة بيانات تجريبية
                db_created, db_message = self.create_test_database_if_missing()

                # تحذير من عدم اكتمال قاعدة البيانات
                dialog = SettingsResultDialog(
                    parent=self,
                    title="تحذير - قاعدة البيانات غير مكتملة",
                    message="⚠️ قاعدة البيانات قد تكون غير مكتملة\n\n"
                           "هل تريد المتابعة مع النسخ الاحتياطي؟",
                    result_type="warning",
                    details=[
                        f"🔍 حالة قاعدة البيانات: {completeness_message}",
                        f"🔧 محاولة الإصلاح: {db_message}",
                        "",
                        "⚠️ تحذيرات:",
                        "  📋 قد تكون بعض الجداول مفقودة",
                        "  💾 النسخة الاحتياطية قد تكون ناقصة",
                        "  🔄 قد تحتاج لإعادة إنشاء قاعدة البيانات",
                        "",
                        "🔧 التوصيات:",
                        "  ✅ تأكد من تشغيل البرنامج بالكامل أولاً",
                        "  📊 أضف بعض البيانات التجريبية",
                        "  🔄 أعد المحاولة بعد التأكد من الجداول",
                        "  🗄️ استخدم قائمة البرنامج لإنشاء الجداول"
                    ]
                )
                if dialog.exec_() != dialog.Accepted:
                    return

            # تنفيذ النسخ الاحتياطي مع معالجة الأخطاء
            try:
                backup_info = backup_manager.create_backup("full", compress=True)
            except Exception as backup_error:
                backup_info = None
                print(f"خطأ في استدعاء دالة النسخ الاحتياطي: {backup_error}")

            if backup_info:
                # الحصول على معلومات الجداول الفعلية والإحصائيات المفصلة
                tables_info = self.get_database_tables_info()
                backup_summary = self.get_backup_summary()
                detailed_stats = self.get_detailed_backup_statistics()

                # تحويل الحجم إلى تنسيق قابل للقراءة
                size_bytes = backup_info.get('size', 0)
                if size_bytes < 1024:
                    size_str = f"{size_bytes} بايت"
                elif size_bytes < 1024 * 1024:
                    size_str = f"{size_bytes / 1024:.1f} كيلوبايت"
                else:
                    size_str = f"{size_bytes / (1024 * 1024):.1f} ميجابايت"

                # تحويل التاريخ إلى تنسيق عربي
                created_at = backup_info.get('created_at', '')
                if created_at:
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(created_at)
                        date_str = dt.strftime("%Y/%m/%d - %H:%M:%S")
                    except:
                        date_str = created_at
                else:
                    date_str = 'غير محدد'

                # إعداد تفاصيل النسخة الاحتياطية
                details = [
                    "📁 معلومات النسخة الاحتياطية:",
                    f"  📄 اسم الملف: {backup_info.get('filename', 'غير محدد')}",
                    f"  📊 الحجم: {size_str}",
                    f"  🕒 التاريخ: {date_str}",
                    f"  🗜️ مضغوط: {'نعم' if backup_info.get('compressed', False) else 'لا'}",
                    f"  🔐 الهاش: {backup_info.get('hash', 'غير محدد')[:16]}...",
                    f"  ✅ حالة الاكتمال: {completeness_message}",
                    "",
                    "✅ البيانات المحفوظة بالكامل (31 جدول):",
                    "  👥 العملاء + هواتفهم وجميع بياناتهم",
                    "  🏢 الموردين + هواتفهم وجميع بياناتهم",
                    "  👷 العمال + هواتفهم وجميع بياناتهم",
                    "  💵 الأجور اليومية + ملاحظاتها",
                    "  💰 الرواتب كاملة",
                    "  🏢 المشاريع + مصروفاتها + موادها",
                    "  🏠 العقارات + وثائقها",
                    "  📦 المخزون وجميع حركاته",
                    "  🛒 المشتريات + عناصرها",
                    "  🛍️ المبيعات + عناصرها",
                    "  💸 المصروفات + 💰 الإيرادات",
                    "  🧾 الفواتير + عناصرها",
                    "  💳 الأقساط + عناصرها",
                    "  🔔 الإشعارات + ⏰ التنبيهات + 📅 الأحداث",
                    "  📁 الوثائق + ⚙️ الإعدادات + 👤 المستخدمين",
                    ""
                ]

                # إضافة الإحصائيات المفصلة للبيانات
                if detailed_stats:
                    details.extend(detailed_stats)
                elif backup_summary:
                    details.extend(backup_summary)
                elif tables_info:
                    details.append("📊 تفاصيل الجداول:")
                    details.extend(tables_info)
                else:
                    details.extend([
                        "📊 ملاحظة:",
                        "  ⚠️ لم يتم العثور على تفاصيل الجداول",
                        "  📋 تم حفظ جميع البيانات الموجودة"
                    ])

                # استخدام نافذة النتائج المحسنة للنجاح
                dialog = SettingsResultDialog(
                    parent=self,
                    title="إنشاء النسخة الاحتياطية",
                    message="✅ تم إنشاء النسخة الاحتياطية بنجاح!\n\n"
                           "💾 تم حفظ جميع بياناتك بأمان.",
                    result_type="success",
                    details=details
                )
                dialog.exec_()
            else:
                # محاولة الحصول على تفاصيل أكثر عن الخطأ
                error_details = []

                # فحص وجود قاعدة البيانات
                if BASIC_SYSTEMS_AVAILABLE:
                    try:
                        db_exists = DatabaseConfig.DB_PATH.exists()
                        if db_exists:
                            db_size = DatabaseConfig.DB_PATH.stat().st_size
                            error_details.append(f"  📊 قاعدة البيانات موجودة ({db_size} بايت)")
                        else:
                            error_details.append("  ❌ قاعدة البيانات غير موجودة")
                    except:
                        error_details.append("  ⚠️ لا يمكن فحص قاعدة البيانات")

                # فحص مجلد النسخ الاحتياطي
                try:
                    backup_dir_exists = AppConfig.BACKUP_DIR.exists()
                    if backup_dir_exists:
                        error_details.append(f"  📁 مجلد النسخ موجود: {AppConfig.BACKUP_DIR}")
                    else:
                        error_details.append(f"  ❌ مجلد النسخ غير موجود: {AppConfig.BACKUP_DIR}")
                except:
                    error_details.append("  ⚠️ لا يمكن فحص مجلد النسخ الاحتياطي")

                dialog = SettingsResultDialog(
                    parent=self,
                    title="فشل النسخ الاحتياطي",
                    message="❌ فشل في إنشاء النسخة الاحتياطية",
                    result_type="error",
                    details=[
                        "🔍 حالة النظام:",
                        *error_details,
                        "",
                        "🔍 الأسباب المحتملة:",
                        "  💾 مساحة القرص الصلب ممتلئة",
                        "  🔒 عدم وجود صلاحيات كافية",
                        "  📁 مسار الحفظ غير صحيح",
                        "  🗄️ قاعدة البيانات مقفلة أو تالفة",
                        "  📦 ملفات النظام المتقدم مفقودة",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  🧹 تنظيف مساحة القرص",
                        "  👤 تشغيل البرنامج كمدير",
                        "  📂 تحقق من مسار قاعدة البيانات",
                        "  🔄 إعادة تشغيل البرنامج",
                        "  📞 الاتصال بالدعم الفني"
                    ]
                )
                dialog.exec_()
        except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في النسخ الاحتياطي",
                    message="❌ حدث خطأ أثناء عملية النسخ الاحتياطي",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الإجراءات المطلوبة:",
                        "  📝 تحقق من سجلات النظام",
                        "  🔄 أعد المحاولة",
                        "  💻 أعد تشغيل البرنامج"
                    ]
                )
                dialog.exec_()

    def restore_backup(self):
        """استعادة نسخة احتياطية كاملة"""
        try:
            if not ADVANCED_SYSTEMS_AVAILABLE:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في النظام",
                    message="❌ نظام استعادة النسخ الاحتياطية غير متاح",
                    result_type="error",
                    details=[
                        "🔍 الأسباب المحتملة:",
                        "  📦 ملفات النظام المتقدم مفقودة",
                        "  🔧 خطأ في التكوين",
                        "  📁 مسارات الملفات غير صحيحة",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  📥 إعادة تثبيت البرنامج",
                        "  🔄 إعادة تشغيل البرنامج",
                        "  👤 تشغيل البرنامج كمدير",
                        "  📞 الاتصال بالدعم الفني"
                    ]
                )
                dialog.exec_()
                return

            # تحذير مهم قبل الاستعادة
            warning_dialog = SettingsResultDialog(
                parent=self,
                title="⚠️ تحذير مهم - استعادة النسخة الاحتياطية",
                message="🚨 تحذير: هذه العملية ستحل محل جميع البيانات الحالية!\n\n"
                       "⚠️ سيتم فقدان جميع التغييرات منذ آخر نسخة احتياطية.\n\n"
                       "هل أنت متأكد من المتابعة؟",
                result_type="warning",
                details=[
                    "⚠️ ما سيحدث:",
                    "  🗑️ حذف جميع البيانات الحالية",
                    "  🔄 استبدالها بالنسخة الاحتياطية المختارة",
                    "  📊 فقدان أي تغييرات حديثة",
                    "",
                    "✅ ما سيتم استعادته:",
                    "  👥 جميع بيانات العملاء والموردين",
                    "  👷 جميع بيانات العمال والأجور",
                    "  🏗️ جميع بيانات المشاريع والعقارات",
                    "  💰 جميع البيانات المالية والفواتير",
                    "  📦 جميع بيانات المخزون والمبيعات",
                    "  🔔 جميع الإشعارات والتنبيهات",
                    "",
                    "🔒 الأمان:",
                    "  💾 سيتم إنشاء نسخة احتياطية من البيانات الحالية أولاً",
                    "  🔄 يمكن التراجع في حالة حدوث مشكلة"
                ]
            )

            if warning_dialog.exec_() != warning_dialog.Accepted:
                return

            # اختيار ملف النسخة الاحتياطية
            from PyQt5.QtWidgets import QFileDialog
            backup_file, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف النسخة الاحتياطية",
                str(AppConfig.BACKUP_DIR) if BASIC_SYSTEMS_AVAILABLE else "",
                "ملفات النسخ الاحتياطية (*.db *.gz);;جميع الملفات (*)"
            )

            if not backup_file:
                return  # المستخدم ألغى العملية

            # تأكيد أخير
            final_confirm = SettingsResultDialog(
                parent=self,
                title="تأكيد نهائي",
                message=f"🔄 هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\n"
                       f"📁 الملف المختار: {Path(backup_file).name}\n\n"
                       f"⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                result_type="warning",
                details=[
                    "🔄 ما سيحدث الآن:",
                    "  1️⃣ إنشاء نسخة احتياطية من البيانات الحالية",
                    "  2️⃣ إيقاف جميع العمليات الجارية",
                    "  3️⃣ استبدال قاعدة البيانات بالكامل",
                    "  4️⃣ إعادة تشغيل النظام",
                    "",
                    "⏱️ المدة المتوقعة: 1-3 دقائق",
                    "🚫 لا تغلق البرنامج أثناء العملية"
                ]
            )

            if final_confirm.exec_() != final_confirm.Accepted:
                return

            # تنفيذ الاستعادة مع معالجة الأخطاء
            try:
                success = backup_manager.restore_backup(backup_file)
            except Exception as restore_error:
                success = False
                print(f"خطأ في استدعاء دالة الاستعادة: {restore_error}")

            if success:
                # نجحت العملية
                dialog = SettingsResultDialog(
                    parent=self,
                    title="استعادة النسخة الاحتياطية",
                    message="✅ تم استعادة النسخة الاحتياطية بنجاح!\n\n"
                           "🔄 يُنصح بإعادة تشغيل البرنامج الآن.",
                    result_type="success",
                    details=[
                        "✅ ما تم استعادته:",
                        "  👥 جميع بيانات العملاء والموردين",
                        "  👷 جميع بيانات العمال والأجور اليومية",
                        "  🏢 جميع بيانات المشاريع والعقارات",
                        "  💰 جميع البيانات المالية والإيرادات",
                        "  💸 جميع المصروفات والفواتير",
                        "  📦 جميع بيانات المخزون والمبيعات",
                        "  🛒 جميع المشتريات والأقساط",
                        "  🔔 جميع الإشعارات والتنبيهات",
                        "",
                        "🔒 الأمان:",
                        "  💾 تم حفظ نسخة احتياطية من البيانات السابقة",
                        "  📁 يمكن العثور عليها في مجلد النسخ الاحتياطية",
                        "",
                        "🔄 الخطوات التالية:",
                        "  1️⃣ أعد تشغيل البرنامج",
                        "  2️⃣ تحقق من البيانات المستعادة",
                        "  3️⃣ أنشئ نسخة احتياطية جديدة"
                    ]
                )
                dialog.exec_()
            else:
                # فشلت العملية
                dialog = SettingsResultDialog(
                    parent=self,
                    title="فشل استعادة النسخة الاحتياطية",
                    message="❌ فشل في استعادة النسخة الاحتياطية",
                    result_type="error",
                    details=[
                        "🔍 الأسباب المحتملة:",
                        "  📁 ملف النسخة الاحتياطية تالف",
                        "  🔒 عدم وجود صلاحيات كافية",
                        "  💾 مساحة القرص الصلب ممتلئة",
                        "  🗄️ قاعدة البيانات مقفلة",
                        "  📂 مسار الملف غير صحيح",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  🔄 جرب ملف نسخة احتياطية آخر",
                        "  👤 شغل البرنامج كمدير",
                        "  🧹 نظف مساحة القرص",
                        "  🔄 أعد تشغيل البرنامج",
                        "  📞 اتصل بالدعم الفني",
                        "",
                        "🛡️ بياناتك آمنة:",
                        "  ✅ لم يتم تغيير البيانات الحالية",
                        "  💾 النسخة الاحتياطية الأصلية سليمة"
                    ]
                )
                dialog.exec_()

        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ في استعادة النسخة الاحتياطية",
                message="❌ حدث خطأ أثناء عملية الاستعادة",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🔧 الحلول المقترحة:",
                    "  🔄 أعد المحاولة مرة أخرى",
                    "  📁 تأكد من صحة ملف النسخة الاحتياطية",
                    "  🔒 تأكد من الصلاحيات",
                    "  💻 أعد تشغيل البرنامج إذا استمر الخطأ",
                    "",
                    "🛡️ بياناتك آمنة:",
                    "  ✅ لم يتم تغيير البيانات الحالية"
                ]
            )
            dialog.exec_()

    def list_backups(self):
        """عرض قائمة النسخ الاحتياطية"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                backups = backup_manager.get_backup_list()

                # إعداد التفاصيل
                details = [
                    f"📊 إجمالي النسخ المتاحة: {len(backups)}",
                    "",
                    "📋 أحدث النسخ الاحتياطية:"
                ]

                for i, backup in enumerate(backups[:10], 1):  # أحدث 10 نسخ
                    details.append(f"  {i}. 📁 {backup.get('filename', 'غير محدد')}")
                    if 'size' in backup:
                        details.append(f"     📊 الحجم: {backup['size']}")
                    if 'timestamp' in backup:
                        details.append(f"     🕒 التاريخ: {backup['timestamp']}")
                    details.append("")

                if len(backups) > 10:
                    details.append(f"... و {len(backups) - 10} نسخة أخرى")

                dialog = SettingsResultDialog(
                    parent=self,
                    title="قائمة النسخ الاحتياطية",
                    message=f"📋 يتوفر لديك {len(backups)} نسخة احتياطية\n\n"
                           "💾 جميع بياناتك محفوظة بأمان.",
                    result_type="info",
                    details=details
                )
                dialog.exec_()
            except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في قائمة النسخ الاحتياطية",
                    message="❌ حدث خطأ أثناء عرض قائمة النسخ الاحتياطية",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الأسباب المحتملة:",
                        "  📁 مشكلة في الوصول لمجلد النسخ",
                        "  🔒 عدم وجود صلاحيات كافية",
                        "  💾 مشكلة في قراءة الملفات",
                        "",
                        "💡 الحلول المقترحة:",
                        "  📂 تحقق من وجود مجلد النسخ الاحتياطية",
                        "  👤 تشغيل البرنامج كمدير",
                        "  🔄 أعد المحاولة مرة أخرى"
                    ]
                )
                dialog.exec_()
    
    def show_performance_report(self):
        """عرض تقرير الأداء"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                report = performance_monitor.get_performance_report(24)

                # تحديد نوع النتيجة حسب التنبيهات
                alerts_count = report.get('alerts_count', 0)
                slow_queries = report.get('slow_queries_count', 0)

                if alerts_count > 5 or slow_queries > 10:
                    result_type = "warning"
                    main_message = "⚠️ تم اكتشاف بعض مشاكل الأداء"
                elif alerts_count > 0 or slow_queries > 0:
                    result_type = "info"
                    main_message = "ℹ️ الأداء جيد مع بعض الملاحظات"
                else:
                    result_type = "success"
                    main_message = "🎉 الأداء ممتاز!"

                # إعداد التفاصيل
                uptime_hours = report.get('uptime_seconds', 0) / 3600
                details = [
                    "📊 إحصائيات الأداء (24 ساعة):",
                    f"  📈 نقاط البيانات: {report.get('data_points', 0):,}",
                    f"  ⚠️ التنبيهات: {alerts_count}",
                    f"  🐌 الاستعلامات البطيئة: {slow_queries}",
                    f"  ⏱️ وقت التشغيل: {uptime_hours:.1f} ساعة",
                    f"  💾 استخدام الذاكرة: {report.get('memory_usage', 'غير محدد')}",
                    f"  🔄 معدل المعالجة: {report.get('processing_rate', 'غير محدد')}",
                    "",
                    "📋 توصيات التحسين:"
                ]

                if slow_queries > 0:
                    details.append("  🔧 تحسين الاستعلامات البطيئة")
                if alerts_count > 0:
                    details.append("  ⚠️ مراجعة التنبيهات")
                if alerts_count == 0 and slow_queries == 0:
                    details.append("  ✅ لا توجد مشاكل - الأداء ممتاز!")

                dialog = SettingsResultDialog(
                    parent=self,
                    title="تقرير الأداء",
                    message=main_message + "\n\n📊 يمكنك مراجعة التفاصيل الكاملة أدناه.",
                    result_type=result_type,
                    details=details
                )
                dialog.exec_()
            except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في تقرير الأداء",
                    message="❌ حدث خطأ أثناء إنشاء تقرير الأداء",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الأسباب المحتملة:",
                        "  📊 مشكلة في جمع بيانات الأداء",
                        "  💾 عدم توفر معلومات النظام",
                        "  🔒 مشكلة في الصلاحيات",
                        "",
                        "💡 الحلول المقترحة:",
                        "  🔄 أعد المحاولة بعد قليل",
                        "  💻 أعد تشغيل البرنامج",
                        "  📊 تحقق من عمل نظام مراقبة الأداء"
                    ]
                )
                dialog.exec_()
    
    def optimize_performance(self):
        """تحسين الأداء"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                performance_monitor.optimize_performance()

                # استخدام نافذة النتائج المحسنة
                dialog = SettingsResultDialog(
                    parent=self,
                    title="تحسين الأداء",
                    message="⚡ تم تشغيل تحسين الأداء بنجاح!\n\n"
                           "🚀 سيتم تطبيق التحسينات تدريجياً.",
                    result_type="success",
                    details=[
                        "🔧 العمليات المنجزة:",
                        "  ✅ تحسين استعلامات قاعدة البيانات",
                        "  ✅ تنظيف ذاكرة التخزين المؤقت",
                        "  ✅ إعادة فهرسة الجداول",
                        "  ✅ تحسين استخدام الذاكرة",
                        "",
                        "📈 النتائج المتوقعة:",
                        "  🚀 تحسن سرعة الاستجابة",
                        "  💾 تقليل استخدام الذاكرة",
                        "  🔄 تحسين معالجة البيانات",
                        "  ⚡ تسريع العمليات العامة"
                    ]
                )
                dialog.exec_()
            except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في تحسين الأداء",
                    message="❌ حدث خطأ أثناء عملية تحسين الأداء",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الأسباب المحتملة:",
                        "  ⚡ مشكلة في عملية التحسين",
                        "  💾 عدم توفر موارد كافية",
                        "  🔒 مشكلة في الصلاحيات",
                        "",
                        "💡 الحلول المقترحة:",
                        "  🔄 أعد المحاولة لاحقاً",
                        "  💻 أعد تشغيل البرنامج",
                        "  👤 تشغيل البرنامج كمدير",
                        "  🧹 قم بتنظيف النظام يدوياً"
                    ]
                )
                dialog.exec_()
    
    def view_logs(self):
        """عرض السجلات مع الحفاظ على التنسيق"""
        try:
            logs_dir = Path("logs")
            if logs_dir.exists():
                log_files = list(logs_dir.glob("*.log"))
                if log_files:
                    # قراءة آخر 20 سطر من السجل الرئيسي مع معالجة أفضل للأخطاء
                    main_log = logs_dir / "application.log"
                    if main_log.exists():
                        try:
                            # محاولة قراءة الملف مع معالجة حالة الاستخدام
                            with open(main_log, 'r', encoding='utf-8', errors='ignore') as f:
                                lines = f.readlines()
                                recent_lines = lines[-20:] if len(lines) > 20 else lines
                                log_content = ''.join(recent_lines).strip()
                                if log_content:
                                    self.logs_text.setPlainText(log_content)
                                else:
                                    self.logs_text.setPlainText("📝 ملف السجل فارغ")
                        except PermissionError:
                            # إذا كان الملف مستخدماً، اعرض رسالة مناسبة
                            self.logs_text.setPlainText("⚠️ ملف السجل مستخدم حالياً\n🔄 جاري تسجيل الأحداث...")
                        except Exception as file_error:
                            self.logs_text.setPlainText(f"❌ خطأ في قراءة ملف السجل: {file_error}")

                        # تطبيق التنسيق بعد تحديث النص
                        self.apply_logs_formatting()
                    else:
                        self.logs_text.setPlainText("📝 ملف السجل الرئيسي غير موجود")
                        self.apply_logs_formatting()
                else:
                    self.logs_text.setPlainText("📝 لا توجد ملفات سجلات")
                    self.apply_logs_formatting()
            else:
                self.logs_text.setPlainText("📁 مجلد السجلات غير موجود")
                self.apply_logs_formatting()
        except Exception as e:
            self.logs_text.setPlainText(f"❌ خطأ عام في قراءة السجلات: {e}")
            self.apply_logs_formatting()
    
    def clear_logs(self):
        """مسح السجلات مع معالجة أفضل للأخطاء"""
        # استخدام نافذة التأكيد المحسنة
        dialog = SettingsConfirmationDialog(
            parent=self,
            title="مسح السجلات",
            message="📝 سيتم مسح جميع ملفات السجلات:\n\n"
                   "🗂️ سجلات التطبيق الرئيسية\n"
                   "⚠️ سجلات الأخطاء والتحذيرات\n"
                   "🔍 سجلات التشخيص\n"
                   "📊 سجلات الأداء\n\n"
                   "⚠️ تحذير: لا يمكن استرداد السجلات بعد الحذف\n"
                   "💡 قد تحتاج لإعادة تشغيل البرنامج",
            icon="📝",
            confirm_text="مسح السجلات",
            cancel_text="إلغاء",
            operation_type="danger"
        )

        if dialog.exec_() == QDialog.Accepted:
            try:
                logs_dir = Path("logs")
                if logs_dir.exists():
                    deleted_files = 0
                    failed_files = []

                    for log_file in logs_dir.glob("*.log"):
                        try:
                            log_file.unlink()
                            deleted_files += 1
                        except PermissionError:
                            failed_files.append(log_file.name)
                        except Exception as file_error:
                            failed_files.append(f"{log_file.name} ({file_error})")

                    if failed_files:
                        dialog = SettingsResultDialog(
                            parent=self,
                            title="مسح السجلات - مع تحذيرات",
                            message=f"⚠️ تم مسح {deleted_files} ملف بنجاح\n\n"
                                   f"❌ فشل في مسح {len(failed_files)} ملف",
                            result_type="warning",
                            details=[
                                f"✅ الملفات المحذوفة: {deleted_files}",
                                f"❌ الملفات التي فشل حذفها: {len(failed_files)}",
                                "",
                                "📋 الملفات المتبقية:",
                            ] + [f"  ❌ {file}" for file in failed_files] + [
                                "",
                                "🔧 الحلول المقترحة:",
                                "  🔄 إعادة تشغيل البرنامج",
                                "  👤 تشغيل البرنامج كمدير",
                                "  🗂️ حذف الملفات يدوياً"
                            ]
                        )
                        dialog.exec_()
                    else:
                        dialog = SettingsResultDialog(
                            parent=self,
                            title="نجح مسح السجلات",
                            message=f"✅ تم مسح {deleted_files} ملف سجل بنجاح!\n\n"
                                   "🧹 تم تنظيف جميع ملفات السجلات.",
                            result_type="success",
                            details=[
                                f"📊 الإحصائيات:",
                                f"  🗑️ الملفات المحذوفة: {deleted_files}",
                                f"  ✅ معدل النجاح: 100%",
                                "",
                                "📋 أنواع السجلات المحذوفة:",
                                "  📝 سجلات التطبيق العامة",
                                "  ⚠️ سجلات الأخطاء والتحذيرات",
                                "  🔍 سجلات التشخيص",
                                "  📊 سجلات الأداء"
                            ]
                        )
                        dialog.exec_()

                    # إعادة تطبيق النص الافتراضي مع التنسيق
                    self.logs_text.clear()
                    sample_logs = """🚀 [2025-07-21 02:55:45] INFO - تم تشغيل البرنامج بنجاح
✅ [2025-07-21 02:55:46] INFO - تم تحميل الإعدادات المركزية
🔐 [2025-07-21 02:55:47] INFO - تم تهيئة نظام الأمان والتشفير
👥 [2025-07-21 02:55:48] INFO - تم تهيئة نظام الصلاحيات
⚡ [2025-07-21 02:55:49] INFO - تم تهيئة نظام مراقبة الأداء
🗄️ [2025-07-21 02:55:50] INFO - تم إعداد قاعدة البيانات بنجاح
🎯 [2025-07-21 02:55:51] INFO - تم تحميل لوحة المعلومات
🤝 [2025-07-21 02:55:52] INFO - تم تحميل قسم العملاء
🏭 [2025-07-21 02:55:53] INFO - تم تحميل قسم الموردين
👷 [2025-07-21 02:55:54] INFO - تم تحميل قسم العمال
🏗️ [2025-07-21 02:55:55] INFO - تم تحميل قسم المشاريع
📦 [2025-07-21 02:55:56] INFO - تم تحميل قسم المخازن
💰 [2025-07-21 02:55:57] INFO - تم تحميل قسم الإيرادات
💸 [2025-07-21 02:55:58] INFO - تم تحميل قسم المصروفات
📋 [2025-07-21 02:55:59] INFO - تم تحميل قسم التقارير
⚙️ [2025-07-21 02:56:00] INFO - تم تحميل قسم الإعدادات
🎉 [2025-07-21 02:56:01] SUCCESS - جميع الأقسام تعمل بنجاح!"""
                    self.logs_text.setPlainText(sample_logs)
                    self.apply_logs_formatting()
                else:
                    dialog = SettingsResultDialog(
                        parent=self,
                        title="مجلد السجلات غير موجود",
                        message="📁 لا يمكن العثور على مجلد السجلات",
                        result_type="info",
                        details=[
                            "🔍 الوضع الحالي:",
                            "  📁 مجلد 'logs' غير موجود",
                            "  📝 لا توجد ملفات سجلات للمسح",
                            "",
                            "💡 هذا طبيعي إذا:",
                            "  🆕 كان هذا أول تشغيل للبرنامج",
                            "  🧹 تم مسح السجلات مسبقاً",
                            "  ⚙️ تم تعطيل نظام السجلات",
                            "",
                            "🔧 لإنشاء السجلات:",
                            "  🏃 استخدم البرنامج لفترة",
                            "  ⚙️ فعّل نظام السجلات في الإعدادات",
                            "  📊 قم ببعض العمليات لإنشاء سجلات"
                        ]
                    )
                    dialog.exec_()
            except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ عام في مسح السجلات",
                    message="❌ حدث خطأ عام أثناء عملية مسح السجلات",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  🔄 أعد تشغيل البرنامج",
                        "  👤 تشغيل البرنامج كمدير",
                        "  📁 تحقق من صلاحيات مجلد السجلات",
                        "  💻 أعد تشغيل النظام إذا استمر الخطأ"
                    ]
                )
                dialog.exec_()
    
    def export_logs(self):
        """تصدير السجلات"""
        from PyQt5.QtWidgets import QFileDialog
        import shutil
        import zipfile
        from datetime import datetime

        try:
            # اختيار مكان الحفظ
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"logs_export_{timestamp}.zip"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "💾 حفظ السجلات المُصدرة",
                default_filename,
                "ملفات مضغوطة (*.zip);;جميع الملفات (*.*)"
            )

            if not file_path:
                return

            logs_dir = Path("logs")
            if not logs_dir.exists():
                dialog = SettingsResultDialog(
                    parent=self,
                    title="مجلد السجلات غير موجود",
                    message="❌ لا يمكن العثور على مجلد السجلات",
                    result_type="error",
                    details=[
                        "🔍 المشكلة:",
                        "  📁 مجلد 'logs' غير موجود",
                        "",
                        "🔧 الحلول:",
                        "  🏃 قم بتشغيل البرنامج أولاً لإنشاء السجلات",
                        "  📝 تأكد من وجود صلاحيات الكتابة",
                        "  📂 أنشئ مجلد 'logs' يدوياً"
                    ]
                )
                dialog.exec_()
                return

            log_files = list(logs_dir.glob("*.log"))
            if not log_files:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="لا توجد ملفات سجلات",
                    message="❌ لا توجد ملفات سجلات للتصدير",
                    result_type="info",
                    details=[
                        "📋 الوضع الحالي:",
                        "  📁 مجلد السجلات موجود",
                        "  📝 لا توجد ملفات .log",
                        "",
                        "💡 لإنشاء السجلات:",
                        "  🏃 استخدم البرنامج لفترة",
                        "  🔧 فعّل نظام السجلات",
                        "  📊 قم ببعض العمليات"
                    ]
                )
                dialog.exec_()
                return

            # إنشاء ملف مضغوط
            with zipfile.ZipFile(file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for log_file in log_files:
                    zipf.write(log_file, log_file.name)

                # إضافة ملف معلومات التصدير
                export_info = f"""📋 معلومات التصدير

🕐 تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📁 عدد الملفات: {len(log_files)}
📊 الملفات المُصدرة:
"""
                for log_file in log_files:
                    size_kb = log_file.stat().st_size / 1024
                    export_info += f"   • {log_file.name} ({size_kb:.1f} KB)\n"

                zipf.writestr("export_info.txt", export_info)

            # استخدام نافذة النتائج المحسنة للنجاح
            file_size_kb = os.path.getsize(file_path) / 1024
            dialog = SettingsResultDialog(
                parent=self,
                title="نجح تصدير السجلات",
                message=f"✅ تم تصدير السجلات بنجاح!\n\n"
                       f"📦 تم إنشاء ملف مضغوط يحتوي على جميع السجلات.",
                result_type="success",
                details=[
                    "📊 معلومات التصدير:",
                    f"  📁 اسم الملف: {Path(file_path).name}",
                    f"  📂 المسار: {file_path}",
                    f"  📝 عدد الملفات: {len(log_files)}",
                    f"  💾 الحجم: {file_size_kb:.1f} KB",
                    "",
                    "📋 محتويات الأرشيف:",
                    "  📝 جميع ملفات السجلات (.log)",
                    "  📊 معلومات التصدير",
                    "  🕒 تاريخ ووقت التصدير",
                    "",
                    "💡 يمكنك الآن:",
                    "  📤 مشاركة الملف مع الدعم الفني",
                    "  💾 حفظ نسخة احتياطية",
                    "  🔍 فتح الملف لمراجعة السجلات"
                ]
            )
            dialog.exec_()

            if BASIC_SYSTEMS_AVAILABLE:
                log_info(f"تم تصدير السجلات إلى: {file_path}")

        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ في تصدير السجلات",
                message="❌ حدث خطأ أثناء تصدير السجلات",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🔧 الحلول المقترحة:",
                    "  📂 تأكد من صحة مسار الحفظ",
                    "  💾 تحقق من توفر مساحة كافية",
                    "  🔒 تأكد من وجود صلاحيات الكتابة",
                    "  🔄 أعد المحاولة مرة أخرى"
                ]
            )
            dialog.exec_()
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ الإعدادات في قاعدة البيانات أو ملف
            # استخدام نافذة النتائج المحسنة
            dialog = SettingsResultDialog(
                parent=self,
                title="حفظ الإعدادات",
                message="✅ تم حفظ جميع الإعدادات بنجاح!\n\n"
                       "💾 إعداداتك محفوظة بأمان.",
                result_type="success",
                details=[
                    "📁 الإعدادات المحفوظة:",
                    "  ⚙️ الإعدادات العامة",
                    "  🔐 إعدادات الأمان",
                    "  💾 إعدادات النسخ الاحتياطي",
                    "  📊 إعدادات الأداء",
                    "  🎨 إعدادات الواجهة",
                    "  💰 الإعدادات المالية",
                    "",
                    "✅ جميع التغييرات نافذة المفعول"
                ]
            )
            dialog.exec_()
            if BASIC_SYSTEMS_AVAILABLE:
                log_info("تم حفظ إعدادات المستخدم")
        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ في حفظ الإعدادات",
                message="❌ حدث خطأ أثناء حفظ الإعدادات",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🔧 الأسباب المحتملة:",
                    "  💾 مشكلة في الكتابة على القرص",
                    "  🔒 عدم وجود صلاحيات كافية",
                    "  📁 مشكلة في مسار الحفظ",
                    "",
                    "💡 الحلول المقترحة:",
                    "  👤 تشغيل البرنامج كمدير",
                    "  💾 تحقق من توفر مساحة كافية",
                    "  🔄 أعد المحاولة مرة أخرى",
                    "  📂 تحقق من صحة مسار الحفظ"
                ]
            )
            dialog.exec_()
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        # استخدام نافذة التأكيد المحسنة
        dialog = SettingsConfirmationDialog(
            parent=self,
            title="إعادة تعيين الإعدادات",
            message="🔄 سيتم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية:\n\n"
                   "🔐 إعدادات الأمان\n"
                   "💾 إعدادات النسخ الاحتياطي\n"
                   "📊 إعدادات الأداء\n"
                   "🎨 إعدادات الواجهة\n\n"
                   "⚠️ تحذير: ستفقد جميع الإعدادات المخصصة",
            icon="🔄",
            confirm_text="إعادة تعيين",
            cancel_text="إلغاء",
            operation_type="warning"
        )

        if dialog.exec_() == QDialog.Accepted:
            try:
                # إعادة تعيين الإعدادات للقيم الافتراضية
                self.min_password_length.setValue(4)
                self.max_login_attempts.setValue(5)
                self.session_timeout.setValue(3600)
                self.auto_backup_enabled.setChecked(True)
                self.backup_interval.setValue(6)
                self.max_backups.setValue(30)
                self.backup_compression.setChecked(True)
                self.performance_monitoring.setChecked(True)
                self.max_memory.setValue(512)
                self.max_query_time.setValue(5)
                
                # استخدام نافذة النتائج المحسنة
                dialog = SettingsResultDialog(
                    parent=self,
                    title="نجح إعادة تعيين الإعدادات",
                    message="✅ تم إعادة تعيين جميع الإعدادات للقيم الافتراضية بنجاح!\n\n"
                           "🔄 جميع الإعدادات عادت للحالة الأصلية.",
                    result_type="success",
                    details=[
                        "🔄 الإعدادات المعاد تعيينها:",
                        "  🔐 إعدادات الأمان: افتراضية",
                        "  💾 النسخ الاحتياطي: تلقائي مع ضغط",
                        "  📊 مراقبة الأداء: مفعلة",
                        "  💾 الحد الأقصى للذاكرة: 512 MB",
                        "  ⏱️ وقت الاستعلام الأقصى: 5 ثوان",
                        "",
                        "✅ النتائج:",
                        "  🎯 النظام سيعمل بالإعدادات المثلى",
                        "  🔒 الأمان بالمستوى القياسي",
                        "  📈 الأداء محسن للاستخدام العادي"
                    ]
                )
                dialog.exec_()
            except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في إعادة التعيين",
                    message="❌ حدث خطأ أثناء إعادة تعيين الإعدادات",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  🔄 أعد المحاولة مرة أخرى",
                        "  💾 تحقق من صلاحيات الكتابة",
                        "  🔧 أعد تشغيل البرنامج كمدير",
                        "  📝 راجع سجلات النظام"
                    ]
                )
                dialog.exec_()

    def check_for_updates(self):
        """فحص التحديثات"""
        try:
            if BASIC_SYSTEMS_AVAILABLE:
                from config import AppConfig
                current_version = getattr(AppConfig, 'VERSION', '1.0.0')
            else:
                current_version = '1.0.0'

            # محاكاة فحص التحديثات
            import random
            has_update = random.choice([True, False])

            if has_update:
                new_version = "1.1.0"
                # استخدام نافذة التأكيد المحسنة للتحديث
                dialog = SettingsConfirmationDialog(
                    parent=self,
                    title="تحديث متاح",
                    message=f"🎉 يتوفر تحديث جديد للتطبيق!\n\n"
                           f"📊 الإصدار الحالي: {current_version}\n"
                           f"🆕 الإصدار الجديد: {new_version}\n\n"
                           f"🚀 الميزات الجديدة:\n"
                           f"• تحسينات كبيرة في الأداء\n"
                           f"• إصلاح جميع الأخطاء المعروفة\n"
                           f"• ميزات جديدة ومفيدة\n"
                           f"• تحسين واجهة المستخدم\n"
                           f"• دعم أفضل للأنظمة الحديثة",
                    icon="🎉",
                    confirm_text="تحديث الآن",
                    cancel_text="لاحقاً",
                    operation_type="info"
                )

                if dialog.exec_() == QDialog.Accepted:
                    # استخدام نافذة النتائج المحسنة للنجاح
                    result_dialog = SettingsResultDialog(
                        parent=self,
                        title="نجح التحديث",
                        message=f"✅ تم تحديث التطبيق بنجاح!\n\n"
                               f"🎉 الإصدار الجديد: {new_version}",
                        result_type="success",
                        details=[
                            "🚀 التحديثات المطبقة:",
                            "  ⚡ تحسينات الأداء",
                            "  🐛 إصلاح الأخطاء",
                            "  ✨ الميزات الجديدة",
                            "  🎨 تحسين الواجهة",
                            "",
                            "📋 الخطوات التالية:",
                            "  🔄 أعد تشغيل التطبيق لتطبيق التحديثات",
                            "  📖 راجع دليل الميزات الجديدة",
                            "  💾 قم بإنشاء نسخة احتياطية"
                        ]
                    )
                    result_dialog.exec_()
            else:
                # استخدام نافذة النتائج المحسنة لعدم وجود تحديثات
                dialog = SettingsResultDialog(
                    parent=self,
                    title="لا توجد تحديثات",
                    message=f"✅ التطبيق محدث!\n\n"
                           f"📊 الإصدار الحالي: {current_version}",
                    result_type="success",
                    details=[
                        "🎯 حالة التطبيق:",
                        f"  📊 الإصدار الحالي: {current_version}",
                        "  ✅ أحدث إصدار متاح",
                        "  🔒 جميع التحديثات الأمنية مطبقة",
                        "",
                        "💡 نصائح:",
                        "  🔄 سيتم فحص التحديثات تلقائياً",
                        "  📧 ستصلك إشعارات عند توفر تحديثات",
                        "  🌐 تأكد من اتصالك بالإنترنت للفحص"
                    ]
                )
                dialog.exec_()

            if BASIC_SYSTEMS_AVAILABLE:
                log_info("تم فحص التحديثات")

        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ في فحص التحديثات",
                message="❌ حدث خطأ أثناء فحص التحديثات",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🔧 الأسباب المحتملة:",
                    "  🌐 عدم وجود اتصال بالإنترنت",
                    "  🔒 جدار الحماية يحجب الاتصال",
                    "  🖥️ خطأ في خادم التحديثات",
                    "  ⚙️ مشكلة في إعدادات النظام",
                    "",
                    "💡 الحلول المقترحة:",
                    "  🌐 تحقق من اتصال الإنترنت",
                    "  🔄 أعد المحاولة لاحقاً",
                    "  🔒 تحقق من إعدادات جدار الحماية",
                    "  📞 اتصل بالدعم الفني إذا استمر الخطأ"
                ]
            )
            dialog.exec_()

    def save_financial_settings(self):
        """حفظ الإعدادات المالية"""
        try:
            # جمع القيم من الواجهة
            currency_code = self.default_currency.currentData()
            currency_name = self.default_currency.currentText()
            decimal_places = self.decimal_places.value()
            tax_rate = self.default_tax_rate.value()
            tax_number = self.company_tax_number.text().strip()
            symbol_position = self.currency_symbol_position.currentData()
            thousands_sep = self.thousands_separator.currentData()

            # حفظ في قاعدة البيانات أو ملف الإعدادات
            settings_data = {
                'currency_code': currency_code,
                'currency_name': currency_name,
                'decimal_places': decimal_places,
                'tax_rate': tax_rate,
                'tax_number': tax_number,
                'symbol_position': symbol_position,
                'thousands_separator': thousands_sep
            }

            # استخدام نافذة النتائج المحسنة
            dialog = SettingsResultDialog(
                parent=self,
                title="نجح حفظ الإعدادات المالية",
                message="✅ تم حفظ جميع الإعدادات المالية بنجاح!\n\n"
                       "💰 إعداداتك المالية محفوظة وجاهزة للاستخدام.",
                result_type="success",
                details=[
                    "💰 الإعدادات المحفوظة:",
                    f"  💱 العملة: {currency_name}",
                    f"  🔢 الخانات العشرية: {decimal_places}",
                    f"  📊 معدل الضريبة: {tax_rate}%",
                    f"  🏢 الرقم الضريبي: {tax_number if tax_number else 'غير محدد'}",
                    f"  📍 موضع رمز العملة: {symbol_position}",
                    f"  🔗 فاصل الآلاف: {thousands_sep}",
                    "",
                    "✅ التأثيرات:",
                    "  📋 جميع الفواتير ستستخدم هذه الإعدادات",
                    "  💵 العملة ستظهر بالتنسيق الجديد",
                    "  🧮 الحسابات ستطبق معدل الضريبة",
                    "  📊 التقارير ستعكس التغييرات"
                ]
            )
            dialog.exec_()

            if BASIC_SYSTEMS_AVAILABLE:
                log_info("تم حفظ الإعدادات المالية")

        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ في حفظ الإعدادات المالية",
                message="❌ حدث خطأ أثناء حفظ الإعدادات المالية",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🔧 الحلول المقترحة:",
                    "  📝 تحقق من صحة البيانات المدخلة",
                    "  🔢 تأكد من أن معدل الضريبة رقم صحيح",
                    "  💾 تحقق من صلاحيات الكتابة",
                    "  🔄 أعد المحاولة مرة أخرى"
                ]
            )
            dialog.exec_()

    def reset_financial_settings(self):
        """إعادة تعيين الإعدادات المالية للافتراضية"""
        # استخدام نافذة التأكيد المحسنة
        dialog = SettingsConfirmationDialog(
            parent=self,
            title="إعادة تعيين الإعدادات المالية",
            message="💰 سيتم إعادة تعيين الإعدادات المالية التالية:\n\n"
                   "💱 العملة الافتراضية\n"
                   "🔢 عدد الخانات العشرية\n"
                   "📊 معدل الضريبة\n"
                   "🏢 الرقم الضريبي\n"
                   "📍 موضع رمز العملة\n"
                   "🔗 فاصل الآلاف\n\n"
                   "⚠️ ستعود جميع القيم للإعدادات الافتراضية",
            icon="💰",
            confirm_text="إعادة تعيين",
            cancel_text="إلغاء",
            operation_type="warning"
        )

        if dialog.exec_() == QDialog.Accepted:
            try:
                # إعادة تعيين القيم الافتراضية
                self.default_currency.setCurrentIndex(0)  # جنيه مصري
                self.decimal_places.setValue(2)
                self.default_tax_rate.setValue(14.0)
                self.company_tax_number.clear()
                self.currency_symbol_position.setCurrentIndex(1)  # بعد الرقم
                self.thousands_separator.setCurrentIndex(0)  # فاصلة

                # استخدام نافذة النتائج المحسنة
                dialog = SettingsResultDialog(
                    parent=self,
                    title="نجح إعادة تعيين الإعدادات المالية",
                    message="✅ تم إعادة تعيين جميع الإعدادات المالية للقيم الافتراضية بنجاح!\n\n"
                           "🔄 جميع الإعدادات عادت للحالة الأصلية.",
                    result_type="success",
                    details=[
                        "🔄 الإعدادات المعاد تعيينها:",
                        "  💱 العملة الافتراضية: الجنيه المصري (EGP)",
                        "  🔢 الخانات العشرية: 2",
                        "  📊 معدل الضريبة: 14.0%",
                        "  🏢 الرقم الضريبي: (فارغ)",
                        "  📍 موضع رمز العملة: بعد الرقم",
                        "  🔗 فاصل الآلاف: فاصلة (,)",
                        "",
                        "✅ النتائج:",
                        "  🎯 جميع الفواتير ستستخدم الإعدادات الافتراضية",
                        "  💵 العملة ستظهر بالتنسيق المصري القياسي",
                        "  📊 التقارير ستعكس التغييرات الجديدة"
                    ]
                )
                dialog.exec_()

                if BASIC_SYSTEMS_AVAILABLE:
                    log_info("تم إعادة تعيين الإعدادات المالية")

            except Exception as e:
                dialog = SettingsResultDialog(
                    parent=self,
                    title="خطأ في إعادة تعيين الإعدادات المالية",
                    message="❌ حدث خطأ أثناء إعادة تعيين الإعدادات المالية",
                    result_type="error",
                    details=[
                        f"🔍 تفاصيل الخطأ: {str(e)}",
                        "",
                        "🔧 الحلول المقترحة:",
                        "  🔄 أعد المحاولة مرة أخرى",
                        "  💾 تحقق من صلاحيات الكتابة",
                        "  🔧 أعد تشغيل البرنامج إذا استمر الخطأ",
                        "  📝 تحقق من سجلات النظام للمزيد من التفاصيل"
                    ]
                )
                dialog.exec_()

    def preview_currency_format(self):
        """معاينة تنسيق العملة"""
        try:
            # جمع القيم الحالية
            currency_code = self.default_currency.currentData()
            decimal_places = self.decimal_places.value()
            symbol_position = self.currency_symbol_position.currentData()
            thousands_sep = self.thousands_separator.currentData()

            # رموز العملات
            currency_symbols = {
                'EGP': 'ج.م',
                'USD': '$',
                'EUR': '€',
                'SAR': 'ر.س',
                'AED': 'د.إ',
                'KWD': 'د.ك',
                'QAR': 'ر.ق'
            }

            symbol = currency_symbols.get(currency_code, currency_code)

            # أرقام للمعاينة
            test_numbers = [1234.56, 10000, 0.5, 999999.99]
            formatted_examples = []

            for number in test_numbers:
                # تطبيق نوع التنسيق
                if thousands_sep == 'int':
                    # رقم صحيح - بدون خانات عشرية
                    formatted_number = f"{int(number)}"
                else:
                    # تطبيق عدد الخانات العشرية
                    if decimal_places == 0:
                        formatted_number = f"{int(number)}"
                    else:
                        formatted_number = f"{number:.{decimal_places}f}"

                    # تطبيق فاصل الآلاف
                    if thousands_sep and len(formatted_number.split('.')[0]) > 3:
                        integer_part = formatted_number.split('.')[0]
                        decimal_part = formatted_number.split('.')[1] if '.' in formatted_number else ''

                        # إضافة فاصل الآلاف
                        integer_with_sep = ''
                        for i, digit in enumerate(reversed(integer_part)):
                            if i > 0 and i % 3 == 0:
                                integer_with_sep = thousands_sep + integer_with_sep
                            integer_with_sep = digit + integer_with_sep

                        formatted_number = integer_with_sep
                        if decimal_part:
                            formatted_number += '.' + decimal_part

                # تطبيق موضع رمز العملة
                if symbol_position == 'before':
                    final_format = f"{symbol} {formatted_number}"
                else:
                    final_format = f"{formatted_number} {symbol}"

                formatted_examples.append(final_format)

            # استخدام نافذة النتائج المحسنة لعرض المعاينة
            dialog = SettingsResultDialog(
                parent=self,
                title="معاينة تنسيق العملة",
                message="🔍 معاينة كيف ستظهر الأرقام بالتنسيق الجديد\n\n"
                       "💡 يمكنك مراجعة الأمثلة أدناه قبل الحفظ.",
                result_type="info",
                details=[
                    "💰 أمثلة على التنسيق الجديد:",
                    ""
                ] + [f"  • {example}" for example in formatted_examples] + [
                    "",
                    "⚙️ الإعدادات المطبقة:",
                    f"  💱 العملة: {currency_name}",
                    f"  🔢 الخانات العشرية: {decimal_places}",
                    f"  📍 موضع الرمز: {currency_position}",
                    f"  🔗 فاصل الآلاف: {thousands_sep}",
                    "",
                    "💡 ملاحظة:",
                    "  📋 هذا التنسيق سيطبق على جميع الفواتير والتقارير",
                    "  💾 احفظ الإعدادات لتطبيق التغييرات"
                ]
            )
            dialog.exec_()

        except Exception as e:
            dialog = SettingsResultDialog(
                parent=self,
                title="خطأ في معاينة التنسيق",
                message="❌ حدث خطأ أثناء إنشاء معاينة التنسيق",
                result_type="error",
                details=[
                    f"🔍 تفاصيل الخطأ: {str(e)}",
                    "",
                    "🔧 الحلول المقترحة:",
                    "  📝 تحقق من صحة الإعدادات المدخلة",
                    "  🔢 تأكد من أن الخانات العشرية رقم صحيح",
                    "  💱 تحقق من اختيار العملة",
                    "  🔄 أعد المحاولة مرة أخرى"
                ]
            )
            dialog.exec_()

    def create_system_buttons_frame(self, layout):
        """إنشاء إطار الأزرار مطابق للعملاء تماماً"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للعملاء
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 80px;
                min-height: 75px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # أزرار التحكم مع نفس ألوان العملاء
        refresh_btn = QPushButton("🔄 تحديث الحالة")
        self.style_advanced_button(refresh_btn, 'modern_teal')
        refresh_btn.clicked.connect(self.refresh_system_status)
        refresh_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        test_btn = QPushButton("🧪 اختبار الأنظمة")
        self.style_advanced_button(test_btn, 'info')
        test_btn.clicked.connect(self.test_systems)
        test_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        restart_btn = QPushButton("🔄 إعادة تشغيل الأنظمة")
        self.style_advanced_button(restart_btn, 'orange')
        restart_btn.clicked.connect(self.restart_systems)
        restart_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        cleanup_btn = QPushButton("🧹 تنظيف النظام")
        self.style_advanced_button(cleanup_btn, 'danger')
        cleanup_btn.clicked.connect(self.cleanup_system)
        cleanup_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(refresh_btn)
        actions_layout.addWidget(test_btn)
        actions_layout.addWidget(restart_btn)
        actions_layout.addWidget(cleanup_btn)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        layout.addWidget(bottom_frame)

    def create_dynamic_buttons_frame(self, main_layout):
        """إنشاء إطار الأزرار الديناميكي الذي يتغير مع التبويبات"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للعملاء
        self.bottom_frame = QFrame()
        self.bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 80px;
                min-height: 75px;
            }
        """)

        # إنشاء التخطيط الأساسي للإطار
        self.actions_layout = QHBoxLayout()
        self.actions_layout.setContentsMargins(0, 0, 0, 0)
        self.actions_layout.setSpacing(4)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(self.actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        self.bottom_frame.setLayout(bottom_container)

        main_layout.addWidget(self.bottom_frame)

        # تحديث الأزرار للتبويب الحالي
        self.update_buttons_for_current_tab()

    def update_buttons_for_current_tab(self, index=0):
        """تحديث الأزرار حسب التبويب المحدد"""
        # مسح الأزرار الحالية
        self.clear_buttons_layout()

        # تحديد الأزرار حسب التبويب
        tab_buttons = {
            0: self.get_system_status_buttons(),      # حالة الأنظمة
            1: self.get_performance_buttons(),        # مراقبة الأداء
            2: self.get_security_buttons(),           # الأمان والحماية
            3: self.get_financial_buttons(),          # التحكم المالي
            4: self.get_logs_buttons(),               # السجلات الأخيرة
            5: self.get_backup_buttons(),             # النسخ الاحتياطي
            6: self.get_general_buttons()             # الإعدادات العامة
        }

        # الحصول على أزرار التبويب الحالي
        current_buttons = tab_buttons.get(index, self.get_system_status_buttons())

        # إضافة الأزرار الجديدة
        for button in current_buttons:
            self.actions_layout.addWidget(button)

    def clear_buttons_layout(self):
        """مسح جميع الأزرار من التخطيط"""
        while self.actions_layout.count():
            child = self.actions_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def get_system_status_buttons(self):
        """أزرار تبويب حالة الأنظمة"""
        buttons = []

        refresh_btn = QPushButton("🔄 تحديث الحالة")
        self.style_advanced_button(refresh_btn, 'modern_teal')
        refresh_btn.clicked.connect(self.refresh_system_status)
        refresh_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(refresh_btn)

        test_btn = QPushButton("🧪 اختبار الأنظمة")
        self.style_advanced_button(test_btn, 'info')
        test_btn.clicked.connect(self.test_systems)
        test_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(test_btn)

        restart_btn = QPushButton("🔄 إعادة تشغيل الأنظمة")
        self.style_advanced_button(restart_btn, 'orange')
        restart_btn.clicked.connect(self.restart_systems)
        restart_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(restart_btn)

        cleanup_btn = QPushButton("🧹 تنظيف النظام")
        self.style_advanced_button(cleanup_btn, 'danger')
        cleanup_btn.clicked.connect(self.cleanup_system)
        cleanup_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(cleanup_btn)

        return buttons

    def get_security_buttons(self):
        """أزرار تبويب الأمان"""
        buttons = []

        change_password_btn = QPushButton("🔑 تغيير كلمة المرور")
        self.style_advanced_button(change_password_btn, 'emerald')
        change_password_btn.clicked.connect(self.change_password)
        change_password_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(change_password_btn)

        security_report_btn = QPushButton("📋 تقرير الأمان")
        self.style_advanced_button(security_report_btn, 'info')
        security_report_btn.clicked.connect(self.show_security_report)
        security_report_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(security_report_btn)

        return buttons

    def get_backup_buttons(self):
        """أزرار تبويب النسخ الاحتياطي"""
        buttons = []

        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        self.style_advanced_button(create_backup_btn, 'emerald')
        create_backup_btn.clicked.connect(self.create_backup)
        create_backup_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(create_backup_btn)

        list_backups_btn = QPushButton("📋 قائمة النسخ")
        self.style_advanced_button(list_backups_btn, 'info')
        list_backups_btn.clicked.connect(self.list_backups)
        list_backups_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(list_backups_btn)

        restore_backup_btn = QPushButton("🔄 استعادة النسخة")
        self.style_advanced_button(restore_backup_btn, 'warning')
        restore_backup_btn.clicked.connect(self.restore_backup)
        restore_backup_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(restore_backup_btn)

        return buttons

    def get_performance_buttons(self):
        """أزرار تبويب الأداء"""
        buttons = []

        performance_report_btn = QPushButton("📊 تقرير الأداء")
        self.style_advanced_button(performance_report_btn, 'info')
        performance_report_btn.clicked.connect(self.show_performance_report)
        performance_report_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(performance_report_btn)

        optimize_btn = QPushButton("⚡ تحسين الأداء")
        self.style_advanced_button(optimize_btn, 'orange')
        optimize_btn.clicked.connect(self.optimize_performance)
        optimize_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(optimize_btn)

        return buttons

    def get_logs_buttons(self):
        """أزرار تبويب السجلات"""
        buttons = []

        view_logs_btn = QPushButton("👁️ عرض السجلات")
        self.style_advanced_button(view_logs_btn, 'info')
        view_logs_btn.clicked.connect(self.view_logs)
        view_logs_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(view_logs_btn)

        clear_logs_btn = QPushButton("🗑️ مسح السجلات")
        self.style_advanced_button(clear_logs_btn, 'danger')
        clear_logs_btn.clicked.connect(self.clear_logs)
        clear_logs_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(clear_logs_btn)

        export_logs_btn = QPushButton("📤 تصدير السجلات")
        self.style_advanced_button(export_logs_btn, 'emerald')
        export_logs_btn.clicked.connect(self.export_logs)
        export_logs_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(export_logs_btn)

        return buttons

    def get_financial_buttons(self):
        """أزرار تبويب المالية والعملة"""
        buttons = []

        save_financial_btn = QPushButton("💾 حفظ الإعدادات المالية")
        self.style_advanced_button(save_financial_btn, 'emerald')
        save_financial_btn.clicked.connect(self.save_financial_settings)
        save_financial_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(save_financial_btn)

        reset_financial_btn = QPushButton("🔄 إعادة تعيين افتراضي")
        self.style_advanced_button(reset_financial_btn, 'warning')
        reset_financial_btn.clicked.connect(self.reset_financial_settings)
        reset_financial_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(reset_financial_btn)

        preview_format_btn = QPushButton("👁️ معاينة التنسيق")
        self.style_advanced_button(preview_format_btn, 'info')
        preview_format_btn.clicked.connect(self.preview_currency_format)
        preview_format_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(preview_format_btn)

        return buttons

    def get_general_buttons(self):
        """أزرار تبويب الإعدادات العامة"""
        buttons = []

        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        self.style_advanced_button(save_settings_btn, 'emerald')
        save_settings_btn.clicked.connect(self.save_settings)
        save_settings_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(save_settings_btn)

        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        self.style_advanced_button(reset_settings_btn, 'orange')
        reset_settings_btn.clicked.connect(self.reset_settings)
        reset_settings_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(reset_settings_btn)

        update_btn = QPushButton("🔄 تحديث التطبيق")
        self.style_advanced_button(update_btn, 'primary')
        update_btn.clicked.connect(self.check_for_updates)
        update_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        buttons.append(update_btn)

        return buttons

    def create_security_buttons_frame(self, layout):
        """إنشاء إطار أزرار الأمان مطابق للعملاء تماماً"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للعملاء
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 80px;
                min-height: 75px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # أزرار الأمان
        change_password_btn = QPushButton("🔑 تغيير كلمة المرور")
        self.style_advanced_button(change_password_btn, 'emerald')
        change_password_btn.clicked.connect(self.change_password)
        change_password_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        security_report_btn = QPushButton("📋 تقرير الأمان")
        self.style_advanced_button(security_report_btn, 'info')
        security_report_btn.clicked.connect(self.show_security_report)
        security_report_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(change_password_btn)
        actions_layout.addWidget(security_report_btn)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        layout.addWidget(bottom_frame)

    def create_backup_buttons_frame(self, layout):
        """إنشاء إطار أزرار النسخ الاحتياطي مطابق للعملاء تماماً"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للعملاء
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 80px;
                min-height: 75px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # أزرار النسخ الاحتياطي
        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        self.style_advanced_button(create_backup_btn, 'emerald')
        create_backup_btn.clicked.connect(self.create_backup)
        create_backup_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        list_backups_btn = QPushButton("📋 قائمة النسخ")
        self.style_advanced_button(list_backups_btn, 'info')
        list_backups_btn.clicked.connect(self.list_backups)
        list_backups_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        restore_backup_btn = QPushButton("🔄 استعادة النسخة")
        self.style_advanced_button(restore_backup_btn, 'warning')
        restore_backup_btn.clicked.connect(self.restore_backup)
        restore_backup_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(create_backup_btn)
        actions_layout.addWidget(list_backups_btn)
        actions_layout.addWidget(restore_backup_btn)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        layout.addWidget(bottom_frame)

    def create_performance_buttons_frame(self, layout):
        """إنشاء إطار أزرار الأداء مطابق للعملاء تماماً"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للعملاء
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 80px;
                min-height: 75px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # أزرار الأداء
        performance_report_btn = QPushButton("📊 تقرير الأداء")
        self.style_advanced_button(performance_report_btn, 'info')
        performance_report_btn.clicked.connect(self.show_performance_report)
        performance_report_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        optimize_btn = QPushButton("⚡ تحسين الأداء")
        self.style_advanced_button(optimize_btn, 'orange')
        optimize_btn.clicked.connect(self.optimize_performance)
        optimize_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(performance_report_btn)
        actions_layout.addWidget(optimize_btn)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        layout.addWidget(bottom_frame)

    def create_logs_buttons_frame(self, layout):
        """إنشاء إطار أزرار السجلات مطابق للعملاء تماماً"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للعملاء
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 80px;
                min-height: 75px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # أزرار السجلات
        view_logs_btn = QPushButton("👁️ عرض السجلات")
        self.style_advanced_button(view_logs_btn, 'info')
        view_logs_btn.clicked.connect(self.view_logs)
        view_logs_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        clear_logs_btn = QPushButton("🗑️ مسح السجلات")
        self.style_advanced_button(clear_logs_btn, 'danger')
        clear_logs_btn.clicked.connect(self.clear_logs)
        clear_logs_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        export_logs_btn = QPushButton("📤 تصدير السجلات")
        self.style_advanced_button(export_logs_btn, 'emerald')
        export_logs_btn.clicked.connect(self.export_logs)
        export_logs_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(view_logs_btn)
        actions_layout.addWidget(clear_logs_btn)
        actions_layout.addWidget(export_logs_btn)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        layout.addWidget(bottom_frame)

    def create_general_buttons_frame(self, layout):
        """إنشاء إطار أزرار الإعدادات العامة مطابق للعملاء تماماً"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للعملاء
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 80px;
                min-height: 75px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # أزرار الإعدادات العامة
        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        self.style_advanced_button(save_settings_btn, 'emerald')
        save_settings_btn.clicked.connect(self.save_settings)
        save_settings_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        self.style_advanced_button(reset_settings_btn, 'orange')
        reset_settings_btn.clicked.connect(self.reset_settings)
        reset_settings_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        update_btn = QPushButton("🔄 تحديث التطبيق")
        self.style_advanced_button(update_btn, 'primary')
        update_btn.clicked.connect(self.check_for_updates)
        update_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(save_settings_btn)
        actions_layout.addWidget(reset_settings_btn)
        actions_layout.addWidget(update_btn)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        layout.addWidget(bottom_frame)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة - مطابق للعملاء"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 3px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 16px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 4px 15px {color_scheme['shadow']},
                               0 2px 8px rgba(0, 0, 0, 0.3),
                               inset 0 1px 0 rgba(255, 255, 255, 0.2);
                    min-height: 45px;
                    max-height: 50px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 3px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px {color_scheme['shadow']},
                               0 4px 12px rgba(0, 0, 0, 0.4),
                               inset 0 1px 0 rgba(255, 255, 255, 0.3);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 3px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 2px 8px {color_scheme['shadow']},
                               0 1px 4px rgba(0, 0, 0, 0.5),
                               inset 0 2px 4px rgba(0, 0, 0, 0.3);
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def closeEvent(self, event):
        """إغلاق النافذة"""
        if self.status_thread:
            self.status_thread.terminate()
            self.status_thread.wait()
        event.accept()

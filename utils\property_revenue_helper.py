#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مساعد ربط الإيرادات بالعقارات
تاريخ الإنشاء: 2025-01-27
الهدف: توفير دوال مساعدة لإدارة إيرادات العقارات
"""

from database import Revenue, Property, Project, Client
from sqlalchemy.orm import sessionmaker
from sqlalchemy import func
from datetime import datetime

class PropertyRevenueHelper:
    """كلاس مساعد لإدارة إيرادات العقارات"""
    
    @staticmethod
    def add_property_revenue(session, property_id, title, amount, category=None, notes=None, date=None):
        """
        إضافة إيراد مرتبط بعقار محدد
        
        Args:
            session: جلسة قاعدة البيانات
            property_id: معرف العقار
            title: عنوان الإيراد
            amount: المبلغ
            category: الفئة (اختياري)
            notes: ملاحظات (اختياري)
            date: التاريخ (اختياري - افتراضي الآن)
        
        Returns:
            Revenue: كائن الإيراد المضاف أو None في حالة الخطأ
        """
        try:
            # التحقق من وجود العقار
            property_item = session.query(Property).get(property_id)
            if not property_item:
                print(f"❌ العقار بالمعرف {property_id} غير موجود!")
                return None
            
            # إنشاء الإيراد الجديد
            revenue = Revenue(
                title=title,
                amount=amount,
                property_id=property_id,
                project_id=property_item.project_id,  # ربط تلقائي بالمشروع إذا كان موجود
                client_id=property_item.project.client_id if property_item.project else None,  # ربط تلقائي بالعميل
                category=category or "إيرادات العقارات",
                notes=notes,
                date=date or datetime.now()
            )
            
            session.add(revenue)
            session.commit()
            
            print(f"✅ تم إضافة إيراد '{title}' للعقار '{property_item.title}' بمبلغ {amount:,.2f} جنيه")
            return revenue
            
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في إضافة الإيراد: {str(e)}")
            return None

    @staticmethod
    def get_property_revenues(session, property_id):
        """
        الحصول على جميع إيرادات عقار محدد
        
        Args:
            session: جلسة قاعدة البيانات
            property_id: معرف العقار
        
        Returns:
            list: قائمة بإيرادات العقار
        """
        try:
            revenues = session.query(Revenue).filter(Revenue.property_id == property_id).all()
            return revenues
        except Exception as e:
            print(f"❌ خطأ في الحصول على إيرادات العقار: {str(e)}")
            return []

    @staticmethod
    def get_property_total_revenue(session, property_id):
        """
        حساب إجمالي إيرادات عقار محدد
        
        Args:
            session: جلسة قاعدة البيانات
            property_id: معرف العقار
        
        Returns:
            float: إجمالي الإيرادات
        """
        try:
            total = session.query(func.sum(Revenue.amount)).filter(
                Revenue.property_id == property_id
            ).scalar()
            return total or 0.0
        except Exception as e:
            print(f"❌ خطأ في حساب إجمالي إيرادات العقار: {str(e)}")
            return 0.0

    @staticmethod
    def get_properties_with_revenues(session):
        """
        الحصول على العقارات التي لها إيرادات
        
        Args:
            session: جلسة قاعدة البيانات
        
        Returns:
            list: قائمة بالعقارات مع إجمالي إيراداتها
        """
        try:
            results = session.query(
                Property,
                func.sum(Revenue.amount).label('total_revenue'),
                func.count(Revenue.id).label('revenue_count')
            ).outerjoin(Revenue).group_by(Property.id).all()
            
            properties_data = []
            for property_item, total_revenue, revenue_count in results:
                properties_data.append({
                    'property': property_item,
                    'total_revenue': total_revenue or 0.0,
                    'revenue_count': revenue_count or 0
                })
            
            return properties_data
        except Exception as e:
            print(f"❌ خطأ في الحصول على العقارات مع الإيرادات: {str(e)}")
            return []

    @staticmethod
    def get_revenue_by_property_type(session, property_type=None):
        """
        الحصول على الإيرادات مجمعة حسب نوع العقار
        
        Args:
            session: جلسة قاعدة البيانات
            property_type: نوع العقار (اختياري)
        
        Returns:
            list: قائمة بالإيرادات مجمعة حسب نوع العقار
        """
        try:
            query = session.query(
                Property.type,
                func.sum(Revenue.amount).label('total_revenue'),
                func.count(Revenue.id).label('revenue_count')
            ).join(Revenue).group_by(Property.type)
            
            if property_type:
                query = query.filter(Property.type == property_type)
            
            results = query.all()
            
            revenue_data = []
            for prop_type, total_revenue, revenue_count in results:
                revenue_data.append({
                    'property_type': prop_type,
                    'total_revenue': total_revenue or 0.0,
                    'revenue_count': revenue_count or 0
                })
            
            return revenue_data
        except Exception as e:
            print(f"❌ خطأ في الحصول على الإيرادات حسب نوع العقار: {str(e)}")
            return []

    @staticmethod
    def link_existing_revenue_to_property(session, revenue_id, property_id):
        """
        ربط إيراد موجود بعقار
        
        Args:
            session: جلسة قاعدة البيانات
            revenue_id: معرف الإيراد
            property_id: معرف العقار
        
        Returns:
            bool: True إذا تم الربط بنجاح، False في حالة الخطأ
        """
        try:
            # التحقق من وجود الإيراد والعقار
            revenue = session.query(Revenue).get(revenue_id)
            property_item = session.query(Property).get(property_id)
            
            if not revenue:
                print(f"❌ الإيراد بالمعرف {revenue_id} غير موجود!")
                return False
            
            if not property_item:
                print(f"❌ العقار بالمعرف {property_id} غير موجود!")
                return False
            
            # ربط الإيراد بالعقار
            revenue.property_id = property_id
            
            # ربط تلقائي بالمشروع والعميل إذا كانوا موجودين
            if property_item.project:
                revenue.project_id = property_item.project_id
                if property_item.project.client:
                    revenue.client_id = property_item.project.client_id
            
            session.commit()
            
            print(f"✅ تم ربط الإيراد '{revenue.title}' بالعقار '{property_item.title}' بنجاح")
            return True
            
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في ربط الإيراد بالعقار: {str(e)}")
            return False

    @staticmethod
    def unlink_revenue_from_property(session, revenue_id):
        """
        إلغاء ربط إيراد من عقار
        
        Args:
            session: جلسة قاعدة البيانات
            revenue_id: معرف الإيراد
        
        Returns:
            bool: True إذا تم إلغاء الربط بنجاح، False في حالة الخطأ
        """
        try:
            revenue = session.query(Revenue).get(revenue_id)
            if not revenue:
                print(f"❌ الإيراد بالمعرف {revenue_id} غير موجود!")
                return False
            
            old_property_title = revenue.property.title if revenue.property else "غير محدد"
            revenue.property_id = None
            
            session.commit()
            
            print(f"✅ تم إلغاء ربط الإيراد '{revenue.title}' من العقار '{old_property_title}' بنجاح")
            return True
            
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في إلغاء ربط الإيراد من العقار: {str(e)}")
            return False

    @staticmethod
    def get_property_revenue_summary(session, property_id):
        """
        الحصول على ملخص إيرادات عقار محدد
        
        Args:
            session: جلسة قاعدة البيانات
            property_id: معرف العقار
        
        Returns:
            dict: ملخص الإيرادات
        """
        try:
            property_item = session.query(Property).get(property_id)
            if not property_item:
                return None
            
            revenues = session.query(Revenue).filter(Revenue.property_id == property_id).all()
            
            total_amount = sum(r.amount for r in revenues if r.amount)
            revenue_count = len(revenues)
            
            # تجميع حسب الفئة
            categories = {}
            for revenue in revenues:
                category = revenue.category or "غير محدد"
                if category not in categories:
                    categories[category] = {'count': 0, 'amount': 0}
                categories[category]['count'] += 1
                categories[category]['amount'] += revenue.amount or 0
            
            return {
                'property': property_item,
                'total_amount': total_amount,
                'revenue_count': revenue_count,
                'categories': categories,
                'revenues': revenues
            }
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على ملخص إيرادات العقار: {str(e)}")
            return None

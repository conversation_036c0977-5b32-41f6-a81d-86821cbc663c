#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مساعد ربط الإيرادات بالمشاريع
تاريخ الإنشاء: 2025-01-27
الهدف: توفير دوال مساعدة لإدارة إيرادات المشاريع
"""

from database import Revenue, Project, Client
from sqlalchemy.orm import sessionmaker
from sqlalchemy import func
from datetime import datetime

class ProjectRevenueHelper:
    """كلاس مساعد لإدارة إيرادات المشاريع"""
    
    @staticmethod
    def add_project_revenue(session, project_id, title, amount, category=None, notes=None, date=None):
        """
        إضافة إيراد مرتبط بمشروع محدد
        
        Args:
            session: جلسة قاعدة البيانات
            project_id: معرف المشروع
            title: عنوان الإيراد
            amount: المبلغ
            category: الفئة (اختياري)
            notes: ملاحظات (اختياري)
            date: التاريخ (اختياري - افتراضي الآن)
        
        Returns:
            Revenue: كائن الإيراد المضاف أو None في حالة الخطأ
        """
        try:
            # التحقق من وجود المشروع
            project = session.query(Project).get(project_id)
            if not project:
                print(f"❌ المشروع بالمعرف {project_id} غير موجود!")
                return None
            
            # إنشاء الإيراد الجديد
            revenue = Revenue(
                title=title,
                amount=amount,
                project_id=project_id,
                client_id=project.client_id,  # ربط تلقائي بالعميل
                category=category or "إيرادات المشاريع",
                notes=notes,
                date=date or datetime.now()
            )
            
            session.add(revenue)
            session.commit()
            
            print(f"✅ تم إضافة إيراد '{title}' للمشروع '{project.name}' بمبلغ {amount:,.2f} جنيه")
            return revenue
            
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في إضافة الإيراد: {str(e)}")
            return None
    
    @staticmethod
    def get_project_revenues(session, project_id):
        """
        الحصول على جميع إيرادات مشروع محدد
        
        Args:
            session: جلسة قاعدة البيانات
            project_id: معرف المشروع
        
        Returns:
            list: قائمة بإيرادات المشروع
        """
        try:
            revenues = session.query(Revenue).filter(
                Revenue.project_id == project_id
            ).order_by(Revenue.date.desc()).all()
            
            return revenues
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على إيرادات المشروع: {str(e)}")
            return []
    
    @staticmethod
    def get_project_total_revenue(session, project_id):
        """
        حساب إجمالي إيرادات مشروع محدد
        
        Args:
            session: جلسة قاعدة البيانات
            project_id: معرف المشروع
        
        Returns:
            float: إجمالي الإيرادات
        """
        try:
            total = session.query(func.sum(Revenue.amount)).filter(
                Revenue.project_id == project_id
            ).scalar()
            
            return total or 0.0
            
        except Exception as e:
            print(f"❌ خطأ في حساب إجمالي إيرادات المشروع: {str(e)}")
            return 0.0
    
    @staticmethod
    def get_project_profit_loss(session, project_id):
        """
        حساب ربح/خسارة مشروع محدد
        
        Args:
            session: جلسة قاعدة البيانات
            project_id: معرف المشروع
        
        Returns:
            dict: معلومات الربح/الخسارة
        """
        try:
            project = session.query(Project).get(project_id)
            if not project:
                return {"error": "المشروع غير موجود"}
            
            # حساب الإيرادات الفعلية
            total_revenue = ProjectRevenueHelper.get_project_total_revenue(session, project_id)
            
            # حساب التكلفة الفعلية
            total_cost = project.total_cost or 0.0
            
            # حساب الربح/الخسارة
            profit_loss = total_revenue - total_cost
            
            # حساب نسبة الربح
            profit_margin = (profit_loss / total_revenue * 100) if total_revenue > 0 else 0
            
            return {
                "project_name": project.name,
                "budget": project.budget or 0.0,
                "total_revenue": total_revenue,
                "total_cost": total_cost,
                "profit_loss": profit_loss,
                "profit_margin": profit_margin,
                "status": project.status
            }
            
        except Exception as e:
            print(f"❌ خطأ في حساب ربح/خسارة المشروع: {str(e)}")
            return {"error": str(e)}
    
    @staticmethod
    def link_existing_revenue_to_project(session, revenue_id, project_id):
        """
        ربط إيراد موجود بمشروع محدد
        
        Args:
            session: جلسة قاعدة البيانات
            revenue_id: معرف الإيراد
            project_id: معرف المشروع
        
        Returns:
            bool: True في حالة النجاح
        """
        try:
            # التحقق من وجود الإيراد والمشروع
            revenue = session.query(Revenue).get(revenue_id)
            project = session.query(Project).get(project_id)
            
            if not revenue:
                print(f"❌ الإيراد بالمعرف {revenue_id} غير موجود!")
                return False
            
            if not project:
                print(f"❌ المشروع بالمعرف {project_id} غير موجود!")
                return False
            
            # ربط الإيراد بالمشروع
            revenue.project_id = project_id
            revenue.client_id = project.client_id  # تحديث العميل أيضاً
            
            session.commit()
            
            print(f"✅ تم ربط الإيراد '{revenue.title}' بالمشروع '{project.name}'")
            return True
            
        except Exception as e:
            session.rollback()
            print(f"❌ خطأ في ربط الإيراد بالمشروع: {str(e)}")
            return False
    
    @staticmethod
    def get_projects_revenue_summary(session, client_id=None):
        """
        الحصول على ملخص إيرادات المشاريع
        
        Args:
            session: جلسة قاعدة البيانات
            client_id: معرف العميل (اختياري)
        
        Returns:
            list: قائمة بملخص إيرادات المشاريع
        """
        try:
            query = session.query(
                Project.id,
                Project.name,
                Project.status,
                Project.budget,
                Project.total_cost,
                func.sum(Revenue.amount).label('total_revenue'),
                func.count(Revenue.id).label('revenue_count')
            ).outerjoin(Revenue, Project.id == Revenue.project_id)
            
            if client_id:
                query = query.filter(Project.client_id == client_id)
            
            results = query.group_by(Project.id).all()
            
            summary = []
            for result in results:
                total_revenue = result.total_revenue or 0.0
                profit_loss = total_revenue - (result.total_cost or 0.0)
                
                summary.append({
                    "project_id": result.id,
                    "project_name": result.name,
                    "status": result.status,
                    "budget": result.budget or 0.0,
                    "total_cost": result.total_cost or 0.0,
                    "total_revenue": total_revenue,
                    "revenue_count": result.revenue_count or 0,
                    "profit_loss": profit_loss,
                    "profit_margin": (profit_loss / total_revenue * 100) if total_revenue > 0 else 0
                })
            
            return summary
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على ملخص إيرادات المشاريع: {str(e)}")
            return []

# دوال مساعدة سريعة
def add_revenue_to_project(session, project_id, title, amount, **kwargs):
    """دالة مختصرة لإضافة إيراد لمشروع"""
    return ProjectRevenueHelper.add_project_revenue(session, project_id, title, amount, **kwargs)

def get_project_profit(session, project_id):
    """دالة مختصرة لحساب ربح المشروع"""
    result = ProjectRevenueHelper.get_project_profit_loss(session, project_id)
    return result.get('profit_loss', 0.0)

def get_project_revenues_total(session, project_id):
    """دالة مختصرة لحساب إجمالي إيرادات المشروع"""
    return ProjectRevenueHelper.get_project_total_revenue(session, project_id)

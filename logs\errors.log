2025-07-29 00:40:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:40:37.664148",
  "exception_type": "ModuleNotFoundError",
  "exception_message": "No module named 'sqlalchemy'",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:41:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:41:06.884669",
  "exception_type": "AssertionError",
  "exception_message": "Class <class 'sqlalchemy.sql.elements.SQLCoreOperations'> directly inherits TypingOnly but has additional attributes {'__static_attributes__', '__firstlineno__'}.",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:41:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:41:14.683286",
  "exception_type": "AssertionError",
  "exception_message": "Class <class 'sqlalchemy.sql.elements.SQLCoreOperations'> directly inherits TypingOnly but has additional attributes {'__firstlineno__', '__static_attributes__'}.",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:41:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:41:36.205982",
  "exception_type": "AssertionError",
  "exception_message": "Class <class 'sqlalchemy.sql.elements.SQLCoreOperations'> directly inherits TypingOnly but has additional attributes {'__static_attributes__', '__firstlineno__'}.",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:41:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:41:40.504775",
  "exception_type": "AssertionError",
  "exception_message": "Class <class 'sqlalchemy.sql.elements.SQLCoreOperations'> directly inherits TypingOnly but has additional attributes {'__static_attributes__', '__firstlineno__'}.",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:43:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:43:14.073909",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:43:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:43:14.077660",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:45:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:45:30.275924",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:45:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:45:30.280489",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:46:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:46:28.542945",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:46:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:46:28.548168",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:49:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:49:10.123811",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:49:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:49:10.126292",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:50:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:50:14.517563",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:50:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:50:14.519919",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:56:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:56:23.903560",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 00:56:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T00:56:23.907208",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:02:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:02:40.745309",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:02:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:02:40.747504",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:05:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:05:39.408885",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:05:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:05:39.413145",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:07:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:07:53.431457",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:07:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:07:53.433632",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:08:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:08:49.440677",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:08:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:08:49.443800",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:12:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:12:02.532312",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:12:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:12:02.534602",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:12:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:12:21.753547",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:12:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:12:21.755794",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:13:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:13:15.889796",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:13:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:13:15.895737",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:14:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:14:15.851067",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:14:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:14:15.855599",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:14:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:14:56.957836",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:14:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:14:56.961017",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:16:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:16:12.165178",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 01:16:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T01:16:12.167380",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:26:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:26:03.689216",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:26:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:26:03.703050",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:27:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:27:04.729823",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:27:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:27:04.733799",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:28:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:28:05.756326",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:28:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:28:05.760446",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:29:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:29:06.785132",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:29:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:29:06.789329",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:30:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:30:07.809005",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:30:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:30:07.812835",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:31:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:31:08.832019",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:31:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:31:08.835333",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:32:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:32:09.860092",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:32:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:32:09.865275",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:33:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:33:10.892764",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:33:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:33:10.898809",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:34:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:34:11.921197",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:34:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:34:11.924921",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:35:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:35:12.946095",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:35:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:35:12.949978",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:36:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:36:13.969433",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:36:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:36:13.973032",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:36:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:36:42.751499",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:36:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:36:42.755977",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:50:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:50:13.741354",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 02:50:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T02:50:13.748294",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:05:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:05:05.546228",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:05:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:05:05.549929",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:09:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:09:12.267711",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:09:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:09:12.274190",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:10:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:10:13.296071",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:10:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:10:13.299592",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:11:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:11:15.418640",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:11:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:11:15.427280",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:12:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:12:16.446996",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:12:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:12:16.452126",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:13:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:13:17.477176",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:13:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:13:17.482762",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:14:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:14:14.689151",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:14:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:14:14.695426",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:14:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:14:54.837504",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:14:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:14:54.841446",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:16:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:16:43.480861",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:16:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:16:43.488002",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:22:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:22:37.901807",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-29 03:22:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-29T03:22:37.906114",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223

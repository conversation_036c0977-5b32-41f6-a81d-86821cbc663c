# -*- coding: utf-8 -*-
"""
مُهيئ النظام الشامل
Comprehensive System Initializer
"""

import sys
import os
from pathlib import Path

# إضافة المسار الحالي لـ Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# استيراد الأنظمة الأساسية
try:
    from config import initialize_config
    BASIC_CONFIG_AVAILABLE = True
except ImportError as e:
    print(f"❌ خطأ في استيراد config: {e}")
    BASIC_CONFIG_AVAILABLE = False

try:
    from logging_config import smart_logger, log_info, log_error
    BASIC_LOGGING_AVAILABLE = True
except ImportError as e:
    print(f"❌ خطأ في استيراد logging_config: {e}")
    BASIC_LOGGING_AVAILABLE = False

BASIC_SYSTEMS_AVAILABLE = BASIC_CONFIG_AVAILABLE and BASIC_LOGGING_AVAILABLE

def safe_log_info(message):
    """تسجيل آمن للمعلومات"""
    try:
        if BASIC_LOGGING_AVAILABLE and 'log_info' in globals():
            log_info(message)
        else:
            print(f"ℹ️ {message}")
    except Exception as e:
        print(f"⚠️ تعذر تسجيل المعلومة: {e}")
        print(f"ℹ️ {message}")

# استيراد الأنظمة المتقدمة بطريقة آمنة
ADVANCED_SYSTEMS = {
    'backup': None,
    'permissions': None,
    'security': None,
    'performance': None
}

# محاولة استيراد كل نظام على حدة
def safe_import_system(module_name, system_name):
    """استيراد آمن للأنظمة"""
    try:
        if module_name == 'backup_manager':
            from backup_manager import backup_manager
            return backup_manager
        elif module_name == 'permissions_manager':
            from permissions_manager import permissions_manager
            return permissions_manager
        elif module_name == 'security_manager':
            from security_manager import security_manager
            return security_manager
        elif module_name == 'performance_monitor':
            from performance_monitor import performance_monitor
            return performance_monitor
    except Exception as e:
        print(f"⚠️ {system_name} غير متاح: {e}")
        return None

# تحميل الأنظمة
ADVANCED_SYSTEMS['backup'] = safe_import_system('backup_manager', 'النسخ الاحتياطي')
ADVANCED_SYSTEMS['permissions'] = safe_import_system('permissions_manager', 'الصلاحيات')
ADVANCED_SYSTEMS['security'] = safe_import_system('security_manager', 'الأمان')
ADVANCED_SYSTEMS['performance'] = safe_import_system('performance_monitor', 'مراقبة الأداء')

SYSTEMS_AVAILABLE = BASIC_SYSTEMS_AVAILABLE

class SystemInitializer:
    """مُهيئ النظام الشامل"""

    def __init__(self):
        self.initialized_systems = []
        self.failed_systems = []
        self.shutdown_called = False  # حماية من الاستدعاء المزدوج
        
    def initialize_all_systems(self):
        """تهيئة جميع الأنظمة"""
        print("🚀 بدء تهيئة الأنظمة المتقدمة...")

        if not SYSTEMS_AVAILABLE:
            print("⚠️ الأنظمة المتقدمة غير متاحة - سيتم تشغيل النظام الأساسي فقط")
            return False

        # عرض حالة الأنظمة المتقدمة
        available_systems = [name for name, system in ADVANCED_SYSTEMS.items() if system is not None]
        missing_systems = [name for name, system in ADVANCED_SYSTEMS.items() if system is None]

        if missing_systems:
            print(f"⚠️ أنظمة غير متاحة: {', '.join(missing_systems)}")
        if available_systems:
            print(f"✅ أنظمة متاحة: {', '.join(available_systems)}")
        
        # 1. تهيئة الإعدادات المركزية
        if self._initialize_config():
            self.initialized_systems.append("config")
        
        # 2. تهيئة نظام السجلات
        if self._initialize_logging():
            self.initialized_systems.append("logging")
        
        # 3. تهيئة نظام الأمان
        if self._initialize_security():
            self.initialized_systems.append("security")
        
        # 4. تهيئة نظام الصلاحيات
        if self._initialize_permissions():
            self.initialized_systems.append("permissions")
        
        # 5. تهيئة نظام النسخ الاحتياطي
        if ADVANCED_SYSTEMS.get('backup') and self._initialize_backup():
            self.initialized_systems.append("backup")
        elif not ADVANCED_SYSTEMS.get('backup'):
            self.failed_systems.append("backup")

        # 6. تهيئة نظام مراقبة الأداء
        if ADVANCED_SYSTEMS.get('performance') and self._initialize_performance():
            self.initialized_systems.append("performance")
        elif not ADVANCED_SYSTEMS.get('performance'):
            self.failed_systems.append("performance")
        
        # تقرير التهيئة
        self._print_initialization_report()
        
        return len(self.failed_systems) == 0
    
    def _initialize_config(self):
        """تهيئة الإعدادات المركزية"""
        try:
            success = initialize_config()
            if success:
                print("✅ تم تهيئة الإعدادات المركزية")
                return True
            else:
                print("❌ فشل في تهيئة الإعدادات المركزية")
                self.failed_systems.append("config")
                return False
        except Exception as e:
            print(f"❌ خطأ في تهيئة الإعدادات: {e}")
            self.failed_systems.append("config")
            return False
    
    def _initialize_logging(self):
        """تهيئة نظام السجلات"""
        try:
            # نظام السجلات يتم تهيئته تلقائياً عند الاستيراد
            safe_log_info("تم تهيئة نظام السجلات المتقدم")
            print("✅ تم تهيئة نظام السجلات المتقدم")
            return True
        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام السجلات: {e}")
            self.failed_systems.append("logging")
            return False
    
    def _initialize_security(self):
        """تهيئة نظام الأمان"""
        if not ADVANCED_SYSTEMS.get('security'):
            return False

        try:
            # اختبار النظام
            test_data = "اختبار التشفير"
            encrypted = ADVANCED_SYSTEMS['security'].encrypt_data(test_data)
            decrypted = ADVANCED_SYSTEMS['security'].decrypt_data(encrypted)

            if decrypted == test_data:
                if BASIC_SYSTEMS_AVAILABLE:
                    safe_log_info("تم تهيئة نظام الأمان والتشفير")
                print("✅ تم تهيئة نظام الأمان والتشفير")
                return True
            else:
                print("❌ فشل اختبار نظام التشفير")
                self.failed_systems.append("security")
                return False

        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام الأمان: {e}")
            self.failed_systems.append("security")
            return False
    
    def _initialize_permissions(self):
        """تهيئة نظام الصلاحيات"""
        if not ADVANCED_SYSTEMS.get('permissions'):
            return False

        try:
            # اختبار النظام
            test_user = ADVANCED_SYSTEMS['permissions'].authenticate_user("admin", "admin")
            if test_user:
                if BASIC_SYSTEMS_AVAILABLE:
                    safe_log_info("تم تهيئة نظام الصلاحيات")
                print("✅ تم تهيئة نظام الصلاحيات")
                return True
            else:
                print("❌ فشل اختبار نظام الصلاحيات")
                self.failed_systems.append("permissions")
                return False

        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام الصلاحيات: {e}")
            self.failed_systems.append("permissions")
            return False
    
    def _initialize_backup(self):
        """تهيئة نظام النسخ الاحتياطي"""
        if not ADVANCED_SYSTEMS.get('backup'):
            return False

        try:
            # بدء النسخ الاحتياطي التلقائي
            ADVANCED_SYSTEMS['backup'].start_automatic_backup()

            if BASIC_SYSTEMS_AVAILABLE:
                safe_log_info("تم تهيئة نظام النسخ الاحتياطي")
            print("✅ تم تهيئة نظام النسخ الاحتياطي التلقائي")
            return True

        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام النسخ الاحتياطي: {e}")
            self.failed_systems.append("backup")
            return False
    
    def _initialize_performance(self):
        """تهيئة نظام مراقبة الأداء"""
        if not ADVANCED_SYSTEMS.get('performance'):
            return False

        try:
            # بدء مراقبة الأداء
            ADVANCED_SYSTEMS['performance'].start_monitoring()

            if BASIC_SYSTEMS_AVAILABLE:
                safe_log_info("تم تهيئة نظام مراقبة الأداء")
            print("✅ تم تهيئة نظام مراقبة الأداء")
            return True

        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام مراقبة الأداء: {e}")
            self.failed_systems.append("performance")
            return False
    
    def _print_initialization_report(self):
        """طباعة تقرير التهيئة"""
        print("\n" + "="*60)
        print("📋 تقرير تهيئة الأنظمة المتقدمة")
        print("="*60)
        
        print(f"✅ الأنظمة المُهيأة بنجاح ({len(self.initialized_systems)}):")
        for system in self.initialized_systems:
            system_names = {
                "config": "الإعدادات المركزية",
                "logging": "نظام السجلات",
                "security": "نظام الأمان",
                "permissions": "نظام الصلاحيات",
                "backup": "نظام النسخ الاحتياطي",
                "performance": "نظام مراقبة الأداء"
            }
            print(f"   • {system_names.get(system, system)}")
        
        if self.failed_systems:
            print(f"\n❌ الأنظمة الفاشلة ({len(self.failed_systems)}):")
            for system in self.failed_systems:
                system_names = {
                    "config": "الإعدادات المركزية",
                    "logging": "نظام السجلات",
                    "security": "نظام الأمان",
                    "permissions": "نظام الصلاحيات",
                    "backup": "نظام النسخ الاحتياطي",
                    "performance": "نظام مراقبة الأداء"
                }
                print(f"   • {system_names.get(system, system)}")
        
        print("\n" + "="*60)
        
        if len(self.failed_systems) == 0:
            print("🎉 تم تهيئة جميع الأنظمة بنجاح!")
        else:
            print("⚠️ تم تهيئة البرنامج مع بعض القيود")
        
        print("="*60 + "\n")
    
    def shutdown_all_systems(self):
        """إغلاق جميع الأنظمة"""
        if not SYSTEMS_AVAILABLE or self.shutdown_called:
            return

        self.shutdown_called = True  # منع الاستدعاء المزدوج

        try:
            print("🔄 إغلاق الأنظمة المتقدمة...")

            # إيقاف مراقبة الأداء
            if "performance" in self.initialized_systems and ADVANCED_SYSTEMS.get('performance'):
                try:
                    ADVANCED_SYSTEMS['performance'].stop_monitoring()
                    print("✅ تم إيقاف نظام مراقبة الأداء")
                except:
                    print("⚠️ تعذر إيقاف نظام مراقبة الأداء")

            # إيقاف النسخ الاحتياطي التلقائي
            if "backup" in self.initialized_systems and ADVANCED_SYSTEMS.get('backup'):
                try:
                    ADVANCED_SYSTEMS['backup'].stop_automatic_backup()
                    print("✅ تم إيقاف نظام النسخ الاحتياطي")
                except:
                    print("⚠️ تعذر إيقاف نظام النسخ الاحتياطي")

            # تنظيف الجلسات المنتهية
            if "permissions" in self.initialized_systems and ADVANCED_SYSTEMS.get('permissions'):
                try:
                    ADVANCED_SYSTEMS['permissions'].cleanup_expired_sessions()
                    print("✅ تم تنظيف جلسات المستخدمين")
                except:
                    print("⚠️ تعذر تنظيف جلسات المستخدمين")

            # حفظ بيانات الأمان
            if "security" in self.initialized_systems and ADVANCED_SYSTEMS.get('security'):
                try:
                    ADVANCED_SYSTEMS['security'].backup_security_data()
                    print("✅ تم حفظ بيانات الأمان")
                except:
                    print("⚠️ تعذر حفظ بيانات الأمان")

            # تنظيف السجلات القديمة - تجاهل الأخطاء
            try:
                if "logging" in self.initialized_systems and BASIC_SYSTEMS_AVAILABLE:
                    smart_logger.cleanup_old_logs()
                    print("✅ تم تنظيف السجلات القديمة")
            except:
                print("⚠️ تعذر تنظيف السجلات القديمة")

            # تسجيل الإغلاق - تجاهل الأخطاء
            try:
                if BASIC_SYSTEMS_AVAILABLE:
                    safe_log_info("تم إغلاق جميع الأنظمة المتقدمة بنجاح")
            except:
                pass

            print("🎉 تم إغلاق جميع الأنظمة بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إغلاق الأنظمة: {e}")
    
    def get_system_status(self):
        """الحصول على حالة الأنظمة"""
        if not SYSTEMS_AVAILABLE:
            return {
                "available": False,
                "message": "الأنظمة المتقدمة غير متاحة"
            }
        
        return {
            "available": True,
            "initialized_systems": self.initialized_systems,
            "failed_systems": self.failed_systems,
            "total_systems": len(self.initialized_systems) + len(self.failed_systems),
            "success_rate": len(self.initialized_systems) / (len(self.initialized_systems) + len(self.failed_systems)) * 100 if (len(self.initialized_systems) + len(self.failed_systems)) > 0 else 0
        }

# إنشاء مثيل عام من مُهيئ النظام
system_initializer = SystemInitializer()

def initialize_advanced_systems():
    """دالة سريعة لتهيئة الأنظمة المتقدمة"""
    return system_initializer.initialize_all_systems()

def shutdown_advanced_systems():
    """دالة سريعة لإغلاق الأنظمة المتقدمة"""
    system_initializer.shutdown_all_systems()

def get_systems_status():
    """دالة سريعة للحصول على حالة الأنظمة"""
    try:
        return system_initializer.get_system_status()
    except Exception as e:
        print(f"خطأ في الحصول على حالة الأنظمة: {e}")
        return {
            "available": BASIC_SYSTEMS_AVAILABLE,
            "initialized_systems": ["config", "logging"] if BASIC_SYSTEMS_AVAILABLE else [],
            "failed_systems": [],
            "total_systems": 2 if BASIC_SYSTEMS_AVAILABLE else 0,
            "success_rate": 100 if BASIC_SYSTEMS_AVAILABLE else 0
        }

if __name__ == "__main__":
    # اختبار تهيئة الأنظمة
    print("🧪 اختبار تهيئة الأنظمة المتقدمة...")
    
    # تهيئة الأنظمة
    success = initialize_advanced_systems()
    
    # عرض حالة الأنظمة
    status = get_systems_status()
    print(f"\n📊 معدل نجاح التهيئة: {status.get('success_rate', 0):.1f}%")
    
    # انتظار قليل
    import time
    time.sleep(3)
    
    # إغلاق الأنظمة
    shutdown_advanced_systems()
    
    print("\n✅ تم اختبار مُهيئ النظام بنجاح")

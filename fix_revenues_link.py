#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح ربط الإيرادات بالمشاريع
تاريخ الإنشاء: 2025-01-27
الهدف: ربط الإيرادات الموجودة بالمشاريع المناسبة
"""

import sys
import os
from datetime import datetime

# إضافة المسار الحالي للبحث عن الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Revenue, Project, Client, engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import and_, or_

# إنشاء جلسة قاعدة البيانات
SessionLocal = sessionmaker(bind=engine)

def link_revenues_to_projects():
    """ربط الإيرادات الموجودة بالمشاريع المناسبة"""
    
    session = SessionLocal()
    
    try:
        print("🔄 بدء ربط الإيرادات بالمشاريع...")
        
        # الحصول على الإيرادات التي لها client_id ولكن ليس لها project_id
        unlinked_revenues = session.query(Revenue).filter(
            and_(
                Revenue.client_id.isnot(None),
                or_(Revenue.project_id.is_(None), Revenue.project_id == 0)
            )
        ).all()
        
        print(f"📊 تم العثور على {len(unlinked_revenues)} إيراد غير مرتبط بمشروع")
        
        if not unlinked_revenues:
            print("✅ جميع الإيرادات مرتبطة بالفعل!")
            return True
        
        linked_count = 0
        
        for revenue in unlinked_revenues:
            # البحث عن مشاريع العميل
            client_projects = session.query(Project).filter(
                Project.client_id == revenue.client_id
            ).order_by(
                # ترتيب حسب الأولوية: قيد التنفيذ، ثم التخطيط، ثم المكتمل
                Project.status.desc(),
                Project.start_date.desc()
            ).all()
            
            if client_projects:
                # اختيار أفضل مشروع للربط
                best_project = None
                
                # البحث عن مشروع قيد التنفيذ أولاً
                for project in client_projects:
                    if project.status == 'in_progress':
                        best_project = project
                        break
                
                # إذا لم يوجد مشروع قيد التنفيذ، ابحث عن مشروع قيد التخطيط
                if not best_project:
                    for project in client_projects:
                        if project.status == 'planning':
                            best_project = project
                            break
                
                # إذا لم يوجد، اختر أحدث مشروع
                if not best_project:
                    best_project = client_projects[0]
                
                # ربط الإيراد بالمشروع
                revenue.project_id = best_project.id
                linked_count += 1
                
                print(f"✅ تم ربط الإيراد '{revenue.title}' ({revenue.amount:,.2f} ج.م) بالمشروع '{best_project.name}'")
            
            else:
                print(f"⚠️ لم يتم العثور على مشاريع للعميل {revenue.client_id} - الإيراد: {revenue.title}")
        
        # حفظ التغييرات
        session.commit()
        
        print(f"✅ تم ربط {linked_count} إيراد بالمشاريع بنجاح!")
        
        # إحصائيات نهائية
        total_revenues = session.query(Revenue).count()
        linked_revenues = session.query(Revenue).filter(Revenue.project_id.isnot(None)).count()
        
        print(f"📊 الإحصائيات النهائية:")
        print(f"   - إجمالي الإيرادات: {total_revenues}")
        print(f"   - الإيرادات المرتبطة بمشاريع: {linked_revenues}")
        print(f"   - الإيرادات غير المرتبطة: {total_revenues - linked_revenues}")
        
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في ربط الإيرادات: {str(e)}")
        return False
        
    finally:
        session.close()

def show_revenue_project_summary():
    """عرض ملخص الإيرادات والمشاريع"""
    
    session = SessionLocal()
    
    try:
        print("\n" + "="*60)
        print("📊 ملخص الإيرادات والمشاريع")
        print("="*60)
        
        # إحصائيات المشاريع
        projects = session.query(Project).all()
        print(f"🏢 إجمالي المشاريع: {len(projects)}")
        
        for project in projects:
            project_revenues = session.query(Revenue).filter(Revenue.project_id == project.id).all()
            total_revenue = sum(r.amount for r in project_revenues)
            
            print(f"   📋 {project.name}")
            print(f"      💰 الإيرادات: {total_revenue:,.2f} ج.م ({len(project_revenues)} إيراد)")
            print(f"      💸 التكلفة: {project.total_cost or 0:,.2f} ج.م")
            print(f"      📈 الربح: {total_revenue - (project.total_cost or 0):,.2f} ج.م")
            print(f"      🎯 الحالة: {project.status}")
            print()
        
        # الإيرادات غير المرتبطة
        unlinked_revenues = session.query(Revenue).filter(
            or_(Revenue.project_id.is_(None), Revenue.project_id == 0)
        ).all()
        
        if unlinked_revenues:
            print(f"⚠️ إيرادات غير مرتبطة بمشاريع: {len(unlinked_revenues)}")
            for revenue in unlinked_revenues:
                client_name = revenue.client.name if revenue.client else "غير محدد"
                print(f"   - {revenue.title}: {revenue.amount:,.2f} ج.م (العميل: {client_name})")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الملخص: {str(e)}")
        
    finally:
        session.close()

def main():
    """تشغيل إصلاح ربط الإيرادات"""
    
    print("🚀 بدء إصلاح ربط الإيرادات بالمشاريع")
    print("="*50)
    
    # ربط الإيرادات بالمشاريع
    if link_revenues_to_projects():
        print("\n✅ تم إصلاح ربط الإيرادات بنجاح!")
        
        # عرض الملخص
        show_revenue_project_summary()
        
        print("\n🎉 الآن يمكنك رؤية الإيرادات في تقرير المشاريع!")
        
    else:
        print("\n❌ فشل في إصلاح ربط الإيرادات!")
        return False
    
    return True

if __name__ == "__main__":
    main()

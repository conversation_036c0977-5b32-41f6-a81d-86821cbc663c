# -*- coding: utf-8 -*-
"""
اختبار دمج قسم الإعدادات في التبويبات
Test Settings Integration in Tabs
"""

import sys
import os
from pathlib import Path

# إضافة المسار الحالي
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_settings_integration():
    """اختبار دمج الإعدادات"""
    print("🧪 اختبار دمج قسم الإعدادات...")
    
    # اختبار استيراد الأنظمة المتقدمة
    try:
        from config import AppConfig
        print("✅ تم استيراد config بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد config: {e}")
        return False
    
    # اختبار استيراد واجهة الإعدادات
    try:
        from ui.settings import SettingsWidget
        print("✅ تم استيراد SettingsWidget بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد SettingsWidget: {e}")
        return False
    
    # اختبار استيراد النافذة الرئيسية
    try:
        from ui.main_window import MainWindow
        print("✅ تم استيراد MainWindow بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد MainWindow: {e}")
        return False
    
    print("🎉 جميع الاستيرادات نجحت!")
    return True

def test_tab_count():
    """اختبار عدد التبويبات"""
    print("\n📊 اختبار عدد التبويبات...")
    
    # قائمة التبويبات المتوقعة
    expected_tabs = [
        "لوحة المعلومات",    # 0
        "العملاء",           # 1
        "الموردين",          # 2
        "العمال",            # 3
        "المشاريع",          # 4
        "المخازن",           # 5
        "المصروفات",         # 6
        "الإيرادات",         # 7
        "الفواتير",          # 8
        "الأقساط",           # 9
        "الإشعارات",         # 10
        "التقارير",          # 11
        "الإعدادات"          # 12
    ]
    
    print(f"📋 التبويبات المتوقعة ({len(expected_tabs)}):")
    for i, tab_name in enumerate(expected_tabs):
        print(f"   {i:2d}. {tab_name}")
    
    print(f"\n✅ إجمالي التبويبات المتوقعة: {len(expected_tabs)}")
    return True

def test_shortcuts():
    """اختبار اختصارات لوحة المفاتيح"""
    print("\n⌨️ اختبار اختصارات لوحة المفاتيح...")
    
    shortcuts = {
        "F1": "لوحة المعلومات",
        "F2": "العملاء", 
        "F3": "الموردين",
        "F4": "العمال",
        "F5": "المشاريع",
        "F6": "المخازن",
        "F7": "المصروفات",
        "F8": "الإيرادات",
        "F9": "الفواتير",
        "Shift+F9": "الأقساط",
        "F10": "الإشعارات",
        "F11": "التقارير",
        "F12": "الإعدادات"  # هذا هو المهم!
    }
    
    print("🎹 اختصارات لوحة المفاتيح:")
    for shortcut, tab_name in shortcuts.items():
        print(f"   {shortcut:8s} → {tab_name}")
    
    print(f"\n✅ اختصار F12 مربوط بقسم الإعدادات")
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار دمج قسم الإعدادات في التبويبات")
    print("=" * 60)
    
    tests = [
        ("استيراد الأنظمة", test_settings_integration),
        ("عدد التبويبات", test_tab_count),
        ("اختصارات لوحة المفاتيح", test_shortcuts),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                failed += 1
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print("📋 نتائج الاختبار:")
    print(f"✅ الاختبارات الناجحة: {passed}")
    print(f"❌ الاختبارات الفاشلة: {failed}")
    print(f"📊 معدل النجاح: {(passed / (passed + failed)) * 100:.1f}%")
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن تشغيل البرنامج واختبار قسم الإعدادات:")
        print("   1. python main.py")
        print("   2. اضغط زر ⚙️ الإعدادات أو F12")
        print("   3. ستجد قسم الإعدادات كتبويب عادي!")
        return True
    else:
        print(f"\n⚠️ {failed} اختبار فشل")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

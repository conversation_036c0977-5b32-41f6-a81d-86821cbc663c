# -*- coding: utf-8 -*-
"""
اختبار واجهة الإعدادات المتقدمة
Test Advanced Settings UI
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# إضافة مسار ui للاستيراد
sys.path.insert(0, 'ui')

try:
    from ui.settings import SettingsWidget
    SETTINGS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: واجهة الإعدادات غير متاحة: {e}")
    SETTINGS_AVAILABLE = False

class TestWindow(QMainWindow):
    """نافذة اختبار لواجهة الإعدادات"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة الاختبار"""
        self.setWindowTitle("🧪 اختبار واجهة الإعدادات المتقدمة")
        self.setGeometry(100, 100, 900, 700)
        
        # الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        if SETTINGS_AVAILABLE:
            # إضافة واجهة الإعدادات
            self.settings_widget = SettingsWidget()
            layout.addWidget(self.settings_widget)
            
            print("✅ تم تحميل واجهة الإعدادات بنجاح")
        else:
            # رسالة خطأ
            error_button = QPushButton("❌ واجهة الإعدادات غير متاحة\n💡 تأكد من تثبيت المتطلبات")
            error_button.setStyleSheet("""
                QPushButton {
                    background-color: #fef3c7;
                    color: #f59e0b;
                    border: 2px solid #f59e0b;
                    border-radius: 8px;
                    padding: 20px;
                    font-size: 14px;
                }
            """)
            layout.addWidget(error_button)

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار واجهة الإعدادات المتقدمة...")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = TestWindow()
    window.show()
    
    print("🎉 تم تشغيل اختبار واجهة الإعدادات")
    print("💡 اضغط على الأزرار لاختبار الوظائف المختلفة")
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

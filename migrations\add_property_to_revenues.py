#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة ربط العقارات بالإيرادات
تاريخ الإنشاء: 2025-01-27
الهدف: إضافة حقل property_id إلى جدول الإيرادات لربط الإيرادات بالعقارات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database import engine, Session
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_property_to_revenues():
    """إضافة حقل property_id إلى جدول الإيرادات"""
    session = Session()
    
    try:
        logger.info("🔄 بدء إضافة حقل العقار إلى جدول الإيرادات...")
        
        # التحقق من وجود العمود أولاً
        check_column_query = text("""
            SELECT COUNT(*) as count
            FROM pragma_table_info('revenues')
            WHERE name = 'property_id'
        """)
        
        result = session.execute(check_column_query).fetchone()
        
        if result.count > 0:
            logger.info("✅ حقل property_id موجود بالفعل في جدول الإيرادات")
            return True
        
        # إضافة العمود الجديد
        logger.info("📝 إضافة حقل property_id إلى جدول الإيرادات...")
        add_column_query = text("""
            ALTER TABLE revenues 
            ADD COLUMN property_id INTEGER 
            REFERENCES properties(id)
        """)
        
        session.execute(add_column_query)
        
        # إنشاء فهرس على العمود الجديد
        logger.info("📊 إنشاء فهرس على حقل property_id...")
        create_index_query = text("""
            CREATE INDEX IF NOT EXISTS idx_revenue_property_id 
            ON revenues(property_id)
        """)
        
        session.execute(create_index_query)
        
        # إنشاء فهرس مركب للعقار والتاريخ
        logger.info("📊 إنشاء فهرس مركب للعقار والتاريخ...")
        create_compound_index_query = text("""
            CREATE INDEX IF NOT EXISTS idx_revenue_property_date 
            ON revenues(property_id, date)
        """)
        
        session.execute(create_compound_index_query)
        
        # إنشاء فهرس مركب للعقار والمبلغ
        logger.info("📊 إنشاء فهرس مركب للعقار والمبلغ...")
        create_amount_index_query = text("""
            CREATE INDEX IF NOT EXISTS idx_revenue_property_amount 
            ON revenues(property_id, amount)
        """)
        
        session.execute(create_amount_index_query)
        
        # حفظ التغييرات
        session.commit()
        logger.info("✅ تم إضافة حقل العقار إلى جدول الإيرادات بنجاح!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إضافة حقل العقار: {str(e)}")
        session.rollback()
        return False
        
    finally:
        session.close()

def verify_migration():
    """التحقق من نجاح عملية التحديث"""
    session = Session()
    
    try:
        logger.info("🔍 التحقق من نجاح عملية التحديث...")
        
        # التحقق من وجود العمود
        check_query = text("""
            SELECT COUNT(*) as count
            FROM pragma_table_info('revenues')
            WHERE name = 'property_id'
        """)
        
        result = session.execute(check_query).fetchone()
        
        if result.count > 0:
            logger.info("✅ تم التحقق من وجود حقل property_id بنجاح")
            
            # التحقق من وجود الفهارس
            index_check_query = text("""
                SELECT name FROM sqlite_master 
                WHERE type='index' 
                AND name IN ('idx_revenue_property_id', 'idx_revenue_property_date', 'idx_revenue_property_amount')
            """)
            
            indexes = session.execute(index_check_query).fetchall()
            logger.info(f"📊 تم العثور على {len(indexes)} فهرس من أصل 3")
            
            for index in indexes:
                logger.info(f"  ✅ فهرس: {index.name}")
            
            return True
        else:
            logger.error("❌ لم يتم العثور على حقل property_id")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في التحقق من التحديث: {str(e)}")
        return False
        
    finally:
        session.close()

def main():
    """تشغيل عملية التحديث"""
    logger.info("🚀 بدء عملية ربط الإيرادات بالعقارات...")
    
    # تشغيل التحديث
    if add_property_to_revenues():
        # التحقق من النجاح
        if verify_migration():
            logger.info("🎉 تم ربط الإيرادات بالعقارات بنجاح!")
            print("\n" + "="*60)
            print("✅ تم ربط الإيرادات بالعقارات بنجاح!")
            print("📋 يمكنك الآن:")
            print("   • ربط الإيرادات بالعقارات عند إضافة إيراد جديد")
            print("   • عرض العقار المرتبط في جدول الإيرادات")
            print("   • عرض تفاصيل العقار في نافذة تفاصيل الإيراد")
            print("   • تصفية الإيرادات حسب العقار")
            print("="*60)
        else:
            logger.error("❌ فشل في التحقق من التحديث")
    else:
        logger.error("❌ فشل في تحديث قاعدة البيانات")

if __name__ == "__main__":
    main()

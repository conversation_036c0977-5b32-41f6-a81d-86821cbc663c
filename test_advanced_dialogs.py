#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع نوافذ التحذير المتطورة للعملاء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer

# استيراد النوافذ المتطورة الجديدة
from ui.clients import (
    ClientWarningDialog, 
    ClientErrorDialog, 
    ClientInfoDialog, 
    ClientConfirmationDialog,
    ClientSuccessDialog,
    ClientProgressDialog,
    ClientMultiChoiceDialog,
    DeleteClientDialog,
    show_advanced_warning,
    show_advanced_error,
    show_advanced_info,
    show_advanced_confirmation,
    show_advanced_success,
    show_advanced_progress,
    show_advanced_multi_choice,
    fix_dialog_display_issues
)

class TestAdvancedDialogsWindow(QMainWindow):
    """نافذة اختبار شاملة لجميع النوافذ المتطورة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار شامل لنوافذ التحذير المتطورة للعملاء")
        self.setGeometry(100, 100, 600, 500)
        
        # تطبيق إصلاح مشكلة النوافذ
        fix_dialog_display_issues()
        
        # إنشاء الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان رئيسي
        title_label = QPushButton("🧪 اختبار النوافذ المتطورة للعملاء")
        title_label.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2563EB, stop:1 #7C3AED);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
        """)
        title_label.setEnabled(False)
        layout.addWidget(title_label)
        
        # مجموعة أزرار النوافذ الأساسية
        basic_group = QHBoxLayout()
        
        warning_btn = QPushButton("⚠️ تحذير")
        warning_btn.clicked.connect(self.test_warning)
        self.style_test_button(warning_btn, '#F59E0B')
        basic_group.addWidget(warning_btn)
        
        error_btn = QPushButton("❌ خطأ")
        error_btn.clicked.connect(self.test_error)
        self.style_test_button(error_btn, '#EF4444')
        basic_group.addWidget(error_btn)
        
        info_btn = QPushButton("ℹ️ معلومات")
        info_btn.clicked.connect(self.test_info)
        self.style_test_button(info_btn, '#3B82F6')
        basic_group.addWidget(info_btn)
        
        layout.addLayout(basic_group)
        
        # مجموعة أزرار النوافذ التفاعلية
        interactive_group = QHBoxLayout()
        
        confirm_btn = QPushButton("❓ تأكيد")
        confirm_btn.clicked.connect(self.test_confirmation)
        self.style_test_button(confirm_btn, '#8B5CF6')
        interactive_group.addWidget(confirm_btn)
        
        success_btn = QPushButton("✅ نجاح")
        success_btn.clicked.connect(self.test_success)
        self.style_test_button(success_btn, '#10B981')
        interactive_group.addWidget(success_btn)
        
        progress_btn = QPushButton("⏳ تقدم")
        progress_btn.clicked.connect(self.test_progress)
        self.style_test_button(progress_btn, '#06B6D4')
        interactive_group.addWidget(progress_btn)
        
        layout.addLayout(interactive_group)
        
        # مجموعة أزرار النوافذ المتقدمة
        advanced_group = QHBoxLayout()
        
        multi_choice_btn = QPushButton("🔀 خيارات متعددة")
        multi_choice_btn.clicked.connect(self.test_multi_choice)
        self.style_test_button(multi_choice_btn, '#F97316')
        advanced_group.addWidget(multi_choice_btn)
        
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.clicked.connect(self.test_delete)
        self.style_test_button(delete_btn, '#DC2626')
        advanced_group.addWidget(delete_btn)
        
        layout.addLayout(advanced_group)
        
        # زر اختبار شامل
        comprehensive_btn = QPushButton("🚀 اختبار شامل لجميع النوافذ")
        comprehensive_btn.clicked.connect(self.test_all_dialogs)
        self.style_test_button(comprehensive_btn, '#7C3AED', large=True)
        layout.addWidget(comprehensive_btn)
        
        # معلومات الاختبار
        info_text = QPushButton("""
📋 معلومات الاختبار:
• جميع النوافذ مطابقة لتصميم نافذة حذف الأقساط
• تم إصلاح مشكلة النوافذ التي تظهر وتختفي لوحدها
• النوافذ تستخدم تصميم متطور مع ألوان متدرجة
• شريط العنوان مخصص مع أيقونات متطورة
        """)
        info_text.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                color: #E5E7EB;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                text-align: left;
                margin: 10px;
            }
        """)
        info_text.setEnabled(False)
        layout.addWidget(info_text)
        
    def style_test_button(self, button, color, large=False):
        """تطبيق تصميم على أزرار الاختبار"""
        size = "14px" if not large else "16px"
        padding = "10px 15px" if not large else "15px 20px"
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {color}CC);
                color: white;
                border: none;
                border-radius: 8px;
                padding: {padding};
                font-size: {size};
                font-weight: bold;
                margin: 5px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}CC, stop:1 {color}99);
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}99, stop:1 {color}66);
            }}
        """)
        
    def test_warning(self):
        """اختبار نافذة التحذير"""
        show_advanced_warning(
            self, 
            "تحذير", 
            "هذه رسالة تحذير تجريبية لاختبار النافذة المتطورة الجديدة.\n\nالنافذة مطابقة لتصميم نافذة حذف الأقساط مع ألوان متدرجة وشريط عنوان مخصص."
        )
        
    def test_error(self):
        """اختبار نافذة الخطأ"""
        show_advanced_error(
            self, 
            "خطأ", 
            "هذه رسالة خطأ تجريبية لاختبار النافذة المتطورة الجديدة.\n\nتم إصلاح مشكلة النوافذ التي تظهر وتختفي لوحدها."
        )
        
    def test_info(self):
        """اختبار نافذة المعلومات"""
        show_advanced_info(
            self, 
            "معلومات", 
            "هذه رسالة معلومات تجريبية لاختبار النافذة المتطورة الجديدة.\n\nجميع النوافذ تستخدم نفس التصميم الموحد."
        )
        
    def test_confirmation(self):
        """اختبار نافذة التأكيد"""
        result = show_advanced_confirmation(
            self, 
            "تأكيد", 
            "هل تريد المتابعة؟\n\nهذه رسالة تأكيد تجريبية لاختبار النافذة المتطورة الجديدة."
        )
        
        if result:
            show_advanced_success(self, "نتيجة", "تم اختيار 'نعم' ✅")
        else:
            show_advanced_info(self, "نتيجة", "تم اختيار 'لا' ❌")
            
    def test_success(self):
        """اختبار نافذة النجاح"""
        show_advanced_success(
            self, 
            "نجح", 
            "تمت العملية بنجاح! 🎉\n\nهذه رسالة نجاح تجريبية لاختبار النافذة المتطورة الجديدة."
        )
        
    def test_progress(self):
        """اختبار نافذة التقدم"""
        progress_dialog = show_advanced_progress(
            self, 
            "جاري العمل", 
            "جاري تنفيذ العملية، يرجى الانتظار...\n\nهذه نافذة تقدم تجريبية."
        )
        
        # إغلاق النافذة بعد 3 ثوان
        QTimer.singleShot(3000, progress_dialog.close)
        
    def test_multi_choice(self):
        """اختبار نافذة الخيارات المتعددة"""
        choices = ["✅ موافق", "❌ رفض", "⏸️ تأجيل", "ℹ️ مزيد من المعلومات"]
        result = show_advanced_multi_choice(
            self, 
            "اختيار", 
            "يرجى اختيار أحد الخيارات التالية:\n\nهذه نافذة خيارات متعددة تجريبية.",
            choices
        )
        
        if result:
            show_advanced_info(self, "اختيارك", f"تم اختيار: {result}")
            
    def test_delete(self):
        """اختبار نافذة الحذف"""
        # إنشاء عميل وهمي للاختبار
        class MockClient:
            def __init__(self):
                self.name = "عميل تجريبي"
                self.balance = 1500.0
                
        mock_client = MockClient()
        dialog = DeleteClientDialog(mock_client, self)
        
        if dialog.exec_():
            show_advanced_success(self, "تم", "تم تأكيد الحذف (تجريبي)")
        else:
            show_advanced_info(self, "ملغي", "تم إلغاء عملية الحذف")
            
    def test_all_dialogs(self):
        """اختبار شامل لجميع النوافذ"""
        dialogs = [
            ("تحذير", self.test_warning),
            ("خطأ", self.test_error),
            ("معلومات", self.test_info),
            ("نجاح", self.test_success),
        ]
        
        for name, func in dialogs:
            func()

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تطبيق نمط عربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق نمط عام للتطبيق
    app.setStyleSheet("""
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #0F172A, stop:0.3 #1E293B, stop:0.7 #334155, stop:1 #475569);
        }
    """)
    
    window = TestAdvancedDialogsWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

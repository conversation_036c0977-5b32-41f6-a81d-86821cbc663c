#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نوافذ التحذير المتطورة للعملاء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# استيراد النوافذ المتطورة الجديدة
from ui.clients import (
    ClientWarningDialog, 
    ClientErrorDialog, 
    ClientInfoDialog, 
    ClientConfirmationDialog,
    show_advanced_warning,
    show_advanced_error,
    show_advanced_info,
    show_advanced_confirmation
)

class TestWindow(QMainWindow):
    """نافذة اختبار النوافذ المتطورة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار نوافذ التحذير المتطورة للعملاء")
        self.setGeometry(100, 100, 400, 300)
        
        # إنشاء الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # أزرار الاختبار
        warning_btn = QPushButton("اختبار نافذة التحذير")
        warning_btn.clicked.connect(self.test_warning)
        layout.addWidget(warning_btn)
        
        error_btn = QPushButton("اختبار نافذة الخطأ")
        error_btn.clicked.connect(self.test_error)
        layout.addWidget(error_btn)
        
        info_btn = QPushButton("اختبار نافذة المعلومات")
        info_btn.clicked.connect(self.test_info)
        layout.addWidget(info_btn)
        
        confirm_btn = QPushButton("اختبار نافذة التأكيد")
        confirm_btn.clicked.connect(self.test_confirmation)
        layout.addWidget(confirm_btn)
        
    def test_warning(self):
        """اختبار نافذة التحذير"""
        show_advanced_warning(
            self, 
            "تحذير", 
            "هذه رسالة تحذير تجريبية لاختبار النافذة المتطورة الجديدة"
        )
        
    def test_error(self):
        """اختبار نافذة الخطأ"""
        show_advanced_error(
            self, 
            "خطأ", 
            "هذه رسالة خطأ تجريبية لاختبار النافذة المتطورة الجديدة"
        )
        
    def test_info(self):
        """اختبار نافذة المعلومات"""
        show_advanced_info(
            self, 
            "معلومات", 
            "هذه رسالة معلومات تجريبية لاختبار النافذة المتطورة الجديدة"
        )
        
    def test_confirmation(self):
        """اختبار نافذة التأكيد"""
        result = show_advanced_confirmation(
            self, 
            "تأكيد", 
            "هل تريد المتابعة؟ هذه رسالة تأكيد تجريبية لاختبار النافذة المتطورة الجديدة"
        )
        
        if result:
            show_advanced_info(self, "نتيجة", "تم اختيار 'نعم'")
        else:
            show_advanced_info(self, "نتيجة", "تم اختيار 'لا'")

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تطبيق نمط عربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

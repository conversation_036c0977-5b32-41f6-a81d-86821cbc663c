#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Migration script to add quantity and unit_price fields to purchases table
"""

import sqlite3
import os
import sys

def migrate_database():
    """إضافة حقلي الكمية وسعر الوحدة إلى جدول المشتريات"""
    
    # مسار قاعدة البيانات
    db_path = 'accounting.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 بدء عملية التحديث...")
        
        # التحقق من وجود الحقول أولاً
        cursor.execute("PRAGMA table_info(purchases)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة حقل الكمية إذا لم يكن موجوداً
        if 'quantity' not in columns:
            print("➕ إضافة حقل الكمية...")
            cursor.execute("""
                ALTER TABLE purchases 
                ADD COLUMN quantity REAL DEFAULT 1.0
            """)
            
            # إنشاء فهرس على حقل الكمية
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_purchases_quantity 
                ON purchases(quantity)
            """)
            print("✅ تم إضافة حقل الكمية بنجاح")
        else:
            print("ℹ️ حقل الكمية موجود بالفعل")
        
        # إضافة حقل سعر الوحدة إذا لم يكن موجوداً
        if 'unit_price' not in columns:
            print("➕ إضافة حقل سعر الوحدة...")
            cursor.execute("""
                ALTER TABLE purchases 
                ADD COLUMN unit_price REAL DEFAULT 0.0
            """)
            
            # إنشاء فهرس على حقل سعر الوحدة
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_purchases_unit_price 
                ON purchases(unit_price)
            """)
            print("✅ تم إضافة حقل سعر الوحدة بنجاح")
        else:
            print("ℹ️ حقل سعر الوحدة موجود بالفعل")
        
        # تحديث المشتريات الموجودة لحساب سعر الوحدة من المبلغ الإجمالي
        print("🔄 تحديث البيانات الموجودة...")
        cursor.execute("""
            UPDATE purchases 
            SET unit_price = total_amount / quantity 
            WHERE quantity > 0 AND unit_price = 0.0
        """)
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
        print("📊 تم إضافة حقلي الكمية وسعر الوحدة إلى جدول المشتريات")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🚀 بدء تحديث قاعدة البيانات...")
    print("=" * 50)
    
    success = migrate_database()
    
    print("=" * 50)
    if success:
        print("🎉 تم التحديث بنجاح!")
        print("💡 يمكنك الآن استخدام حقلي الكمية وسعر الوحدة في المشتريات")
    else:
        print("💥 فشل في التحديث!")
        sys.exit(1)

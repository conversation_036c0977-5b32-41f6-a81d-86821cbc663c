# 🏢 برنامج المحاسبة للمكتب الهندسي - الإصدار المتقدم

## 🆕 الأنظمة المتقدمة الجديدة

تم إضافة **6 أنظمة متقدمة** جديدة:
- 📁 **نظام الإعدادات المركزية** - إدارة موحدة لجميع الإعدادات
- 📝 **نظام السجلات المتقدم** - تسجيل شامل للأحداث والأخطاء
- 💾 **النسخ الاحتياطي التلقائي** - حماية البيانات تلقائياً
- 🔐 **إدارة الصلاحيات المتقدمة** - نظام أمان متطور
- 🛡️ **الأمان والتشفير** - حماية البيانات الحساسة
- 📊 **مراقبة الأداء** - تحسين وتتبع أداء النظام

## 🚀 كيفية التشغيل (تم حل مشكلة الشاشة السوداء نهائياً)

### الطريقة الموصى بها (آمنة 100%):
```bash
python run.py
```

### الطريقة الآمنة مع شاشة تحميل:
```bash
python main_safe.py
```

### الطريقة العادية (محسنة):
```bash
python main.py
```

### الطريقة الصامتة (بدون رسائل تقنية):
```bash
python main_silent.py
```

## 🔐 معلومات الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin

## 📋 الأقسام المتاحة
- 🤝 إدارة العملاء والموردين
- 👔 إدارة العمال والأجور
- 🏢 إدارة المشاريع والممتلكات
- 📦 إدارة المخزون والمبيعات
- 💰 إدارة المالية والفواتير
- 📊 التقارير والإحصائيات
- 🔔 الإشعارات

## 📋 متطلبات النظام

### المتطلبات الأساسية:
```bash
pip install PyQt5 sqlalchemy psutil
```

### المتطلبات المتقدمة (للأنظمة الجديدة):
```bash
pip install cryptography bcrypt schedule
```

### تثبيت جميع المتطلبات:
```bash
pip install -r requirements.txt
```

## 🛠️ حل مشكلة الشاشة السوداء

إذا واجهت مشكلة الشاشة السوداء، استخدم إحدى الطرق التالية:

### ✅ الحل الأول (الأسرع):
```bash
python run.py
```

### ✅ الحل الثاني (مع شاشة تحميل):
```bash
python main_safe.py
```

### ✅ الحل الثالث (إعادة تشغيل):
1. أغلق البرنامج تماماً
2. انتظر 5 ثوان
3. شغل البرنامج مرة أخرى

## 🧪 اختبار الأنظمة المتقدمة
```bash
# اختبار شامل لجميع الأنظمة
python test_all_systems.py

# اختبار مُهيئ النظام
python system_initializer.py
```

## 💡 ملاحظات
- ✅ تم حل مشكلة الشاشة السوداء نهائياً
- ✅ يتم حفظ البيانات تلقائياً
- ✅ جميع الوظائف تعمل بشكل طبيعي
- ✅ تم تنظيف الكود وإزالة التكرار
- ✅ تم تحسين الأداء وسرعة التشغيل
- 🆕 **أنظمة أمان متقدمة** - تشفير وحماية البيانات
- 🆕 **نسخ احتياطي تلقائي** - حماية من فقدان البيانات
- 🆕 **مراقبة الأداء** - تحسين مستمر للنظام
- 🆕 **سجلات متقدمة** - تتبع شامل للأحداث

## 🔧 استكشاف الأخطاء
إذا واجهت أي مشاكل:
1. تأكد من تثبيت المتطلبات: `pip install -r requirements.txt`
2. استخدم `python run.py` للتشغيل الآمن
3. شغل الاختبار الشامل: `python test_all_systems.py`
4. تحقق من السجلات: `logs/errors.log`
5. تأكد من وجود جميع الملفات في المجلد
6. أعد تشغيل الكمبيوتر إذا لزم الأمر

## 📚 دليل التثبيت المفصل
للحصول على دليل تثبيت مفصل، راجع: [INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md)

---
**الإصدار المتقدم - 2025 | 6 أنظمة متقدمة جديدة + حل مشكلة الشاشة السوداء ✅**

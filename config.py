# -*- coding: utf-8 -*-
"""
ملف الإعدادات المركزي للبرنامج
Central Configuration File for the Application
"""

import os
from pathlib import Path

# ===== إعدادات التطبيق الأساسية =====
class AppConfig:
    """إعدادات التطبيق الأساسية"""
    
    # معلومات التطبيق
    APP_NAME = "Advanced Management System"
    APP_VERSION = "16.8.0"
    APP_AUTHOR = "Programmer Engineer | Khale<PERSON>"
    APP_TITLE = "👨‍💻 Programmer Engineer | <PERSON>haled <PERSON> - Smart Finish"
    
    # مسارات التطبيق
    BASE_DIR = Path(__file__).parent.absolute()
    DATA_DIR = BASE_DIR / "data"
    BACKUP_DIR = BASE_DIR / "backups"
    LOGS_DIR = BASE_DIR / "logs"
    DOCUMENTS_DIR = BASE_DIR / "documents"
    RESOURCES_DIR = BASE_DIR / "resources"
    TEMP_DIR = BASE_DIR / "temp"
    
    # إنشاء المجلدات إذا لم تكن موجودة
    @classmethod
    def create_directories(cls):
        """إنشاء المجلدات المطلوبة"""
        directories = [cls.DATA_DIR, cls.BACKUP_DIR, cls.LOGS_DIR, 
                      cls.DOCUMENTS_DIR, cls.TEMP_DIR]
        for directory in directories:
            directory.mkdir(exist_ok=True)

# ===== إعدادات قاعدة البيانات =====
class DatabaseConfig:
    """إعدادات قاعدة البيانات"""
    
    # معلومات الاتصال
    DB_TYPE = "sqlite"
    DB_NAME = "accounting.db"
    DB_PATH = AppConfig.BASE_DIR / DB_NAME
    DB_URL = f"sqlite:///{DB_PATH}"
    
    # إعدادات الاتصال
    CONNECTION_TIMEOUT = 60
    CACHED_STATEMENTS = 1000
    POOL_SIZE = 50
    MAX_OVERFLOW = 100
    POOL_TIMEOUT = 30
    POOL_RECYCLE = 7200
    
    # إعدادات الأداء
    PRAGMA_SETTINGS = {
        "journal_mode": "WAL",
        "synchronous": "NORMAL",
        "cache_size": -64000,  # 64MB
        "temp_store": "MEMORY",
        "mmap_size": *********,  # 256MB
        "busy_timeout": 60000,
        "page_size": 4096,
        "wal_autocheckpoint": 1000,
        "threads": 4
    }

# ===== إعدادات الواجهة =====
class UIConfig:
    """إعدادات واجهة المستخدم"""
    
    # إعدادات النافذة
    WINDOW_MIN_WIDTH = 1200
    WINDOW_MIN_HEIGHT = 800
    WINDOW_TITLE = AppConfig.APP_TITLE
    
    # إعدادات الخط
    DEFAULT_FONT_FAMILY = "Arial"
    DEFAULT_FONT_SIZE = 10
    
    # إعدادات اللغة والاتجاه
    LANGUAGE = "ar"
    TEXT_DIRECTION = "rtl"  # Right to Left
    
    # إعدادات الألوان
    COLORS = {
        "primary": "#2563eb",
        "secondary": "#64748b",
        "success": "#10b981",
        "warning": "#f59e0b",
        "danger": "#ef4444",
        "background": "#cbd5e1",
        "surface": "#f8fafc",
        "text_primary": "#1e293b",
        "text_secondary": "#64748b"
    }
    
    # إعدادات الأنماط
    STYLE_THEME = "Fusion"
    HIGH_DPI_SCALING = True

# ===== إعدادات الأمان =====
class SecurityConfig:
    """إعدادات الأمان والحماية"""
    
    # إعدادات كلمات المرور
    PASSWORD_MIN_LENGTH = 4
    PASSWORD_HASH_ALGORITHM = "pbkdf2_sha256"
    PASSWORD_SALT_LENGTH = 32
    
    # إعدادات الجلسة
    SESSION_TIMEOUT = 3600  # ساعة واحدة
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = 300  # 5 دقائق
    
    # إعدادات التشفير
    ENCRYPTION_KEY_LENGTH = 32
    ENCRYPTION_ALGORITHM = "AES-256-GCM"
    
    # إعدادات الأمان العامة
    ENABLE_SQL_INJECTION_PROTECTION = True
    ENABLE_XSS_PROTECTION = True
    ENABLE_CSRF_PROTECTION = True

# ===== إعدادات النسخ الاحتياطي =====
class BackupConfig:
    """إعدادات النسخ الاحتياطي"""
    
    # إعدادات التوقيت
    AUTO_BACKUP_ENABLED = True
    BACKUP_INTERVAL_HOURS = 6  # كل 6 ساعات
    BACKUP_TIME_DAILY = "02:00"  # الساعة 2 صباحاً
    
    # إعدادات الحفظ
    MAX_BACKUP_FILES = 30  # الاحتفاظ بـ 30 نسخة احتياطية
    BACKUP_COMPRESSION = True
    BACKUP_ENCRYPTION = True
    
    # أنواع النسخ الاحتياطي
    BACKUP_TYPES = {
        "full": "نسخة كاملة",
        "incremental": "نسخة تزايدية",
        "differential": "نسخة تفاضلية"
    }
    
    # مسار النسخ الاحتياطي
    BACKUP_PATH = AppConfig.BACKUP_DIR
    
    # تنسيق اسم الملف
    BACKUP_FILENAME_FORMAT = "accounting_backup_{timestamp}.db"

# ===== إعدادات السجلات =====
class LoggingConfig:
    """إعدادات تسجيل الأحداث والأخطاء"""
    
    # مستويات السجلات
    LOG_LEVEL = "INFO"
    LOG_LEVELS = {
        "DEBUG": 10,
        "INFO": 20,
        "WARNING": 30,
        "ERROR": 40,
        "CRITICAL": 50
    }
    
    # إعدادات الملفات
    LOG_FILE_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_FILE_BACKUP_COUNT = 5
    LOG_FILE_ENCODING = "utf-8"
    
    # أنواع السجلات
    LOG_FILES = {
        "main": "application.log",
        "database": "database.log",
        "security": "security.log",
        "performance": "performance.log",
        "errors": "errors.log"
    }
    
    # تنسيق السجلات
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# ===== إعدادات الأداء =====
class PerformanceConfig:
    """إعدادات مراقبة الأداء"""
    
    # إعدادات المراقبة
    PERFORMANCE_MONITORING_ENABLED = True
    MEMORY_MONITORING_ENABLED = True
    DATABASE_MONITORING_ENABLED = True
    
    # حدود الأداء
    MAX_MEMORY_USAGE_MB = 512  # 512MB
    MAX_QUERY_TIME_SECONDS = 5  # 5 ثوان
    MAX_RESPONSE_TIME_MS = 1000  # ثانية واحدة
    
    # إعدادات التقارير
    PERFORMANCE_REPORT_INTERVAL = 3600  # كل ساعة
    PERFORMANCE_LOG_SLOW_QUERIES = True
    PERFORMANCE_LOG_MEMORY_USAGE = True

# ===== إعدادات التصدير والطباعة =====
class ExportConfig:
    """إعدادات التصدير والطباعة"""
    
    # أنواع التصدير المدعومة
    SUPPORTED_FORMATS = ["pdf", "excel", "csv", "word"]
    
    # إعدادات PDF
    PDF_PAGE_SIZE = "A4"
    PDF_ORIENTATION = "portrait"
    PDF_MARGIN = 20
    PDF_FONT = "Arial"
    PDF_FONT_SIZE = 12
    
    # إعدادات Excel
    EXCEL_DEFAULT_SHEET_NAME = "البيانات"
    EXCEL_AUTO_FIT_COLUMNS = True
    EXCEL_INCLUDE_CHARTS = True
    
    # إعدادات CSV
    CSV_DELIMITER = ","
    CSV_ENCODING = "utf-8-sig"  # لدعم العربية
    CSV_INCLUDE_HEADERS = True

# ===== إعدادات الإشعارات =====
class NotificationConfig:
    """إعدادات الإشعارات"""
    
    # إعدادات عامة
    NOTIFICATIONS_ENABLED = True
    NOTIFICATION_SOUND_ENABLED = True
    NOTIFICATION_POPUP_ENABLED = True
    
    # أنواع الإشعارات
    NOTIFICATION_TYPES = {
        "info": "معلومات",
        "warning": "تحذير", 
        "error": "خطأ",
        "success": "نجح"
    }
    
    # إعدادات التوقيت
    NOTIFICATION_DISPLAY_TIME = 5000  # 5 ثوان
    NOTIFICATION_CHECK_INTERVAL = 60000  # دقيقة واحدة

# ===== إعدادات التطبيق المتقدمة =====
class AdvancedConfig:
    """إعدادات متقدمة للتطبيق"""
    
    # إعدادات التحديث
    AUTO_UPDATE_ENABLED = False
    UPDATE_CHECK_INTERVAL = 86400  # يوم واحد
    UPDATE_SERVER_URL = "https://updates.smartfinish.com"
    
    # إعدادات التطوير
    DEBUG_MODE = False
    DEVELOPMENT_MODE = False
    TESTING_MODE = False
    
    # إعدادات الشبكة
    NETWORK_TIMEOUT = 30
    MAX_CONCURRENT_CONNECTIONS = 10
    
    # إعدادات الذاكرة
    MEMORY_CACHE_SIZE = 100  # MB
    DISK_CACHE_SIZE = 500   # MB

# ===== دالة تهيئة الإعدادات =====
def initialize_config():
    """تهيئة جميع الإعدادات"""
    try:
        # إنشاء المجلدات المطلوبة
        AppConfig.create_directories()
        
        print("✅ تم تحميل الإعدادات المركزية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحميل الإعدادات: {e}")
        return False

# تهيئة الإعدادات عند استيراد الملف
if __name__ != "__main__":
    initialize_config()

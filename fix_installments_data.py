#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح البيانات الناقصة في جدول الأقساط
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_db, get_session, Installment
from utils import fix_existing_installments_data

def main():
    """إصلاح البيانات الناقصة في الأقساط"""
    print("🔧 بدء إصلاح البيانات الناقصة في جدول الأقساط")
    print("=" * 60)
    
    # إعداد قاعدة البيانات
    init_db()
    session = get_session()
    
    try:
        # عرض الأقساط الحالية قبل الإصلاح
        print("\n📋 الأقساط الحالية قبل الإصلاح:")
        installments = session.query(Installment).filter(
            Installment.notes.like('%قسط تلقائي%')
        ).all()
        
        for installment in installments:
            client_name = installment.client.name if installment.client else "غير محدد"
            description = getattr(installment, 'description', None) or "غير محدد"
            print(f"   • القسط #{installment.id}:")
            print(f"     - العميل المرتبط: {client_name}")
            print(f"     - الوصف: {description}")
            print(f"     - المبلغ: {installment.total_amount:,.2f} ج.م")
            
            # استخراج اسم الكيان من الملاحظات
            if installment.notes:
                lines = installment.notes.split('\n')
                for line in lines:
                    if line.startswith('الكيان:'):
                        entity_from_notes = line.replace('الكيان: ', '').strip()
                        print(f"     - الكيان من الملاحظات: {entity_from_notes}")
                        break
            print()
        
        # تشغيل إصلاح البيانات
        print("\n🔧 تشغيل إصلاح البيانات...")
        fixed_count = fix_existing_installments_data(session)
        
        # عرض النتائج بعد الإصلاح
        print(f"\n📊 النتائج:")
        print(f"   • عدد الأقساط المُصلحة: {fixed_count}")
        
        # عرض الأقساط بعد الإصلاح
        print("\n📋 الأقساط بعد الإصلاح:")
        installments_after = session.query(Installment).filter(
            Installment.notes.like('%قسط تلقائي%')
        ).all()
        
        for installment in installments_after:
            client_name = installment.client.name if installment.client else "غير محدد"
            description = getattr(installment, 'description', None) or "غير محدد"
            print(f"   • القسط #{installment.id}:")
            print(f"     - العميل المرتبط: {client_name}")
            print(f"     - الوصف المُحدث: {description}")
            print(f"     - المبلغ: {installment.total_amount:,.2f} ج.م")
            print()
        
        print("\n" + "=" * 60)
        print("🎉 اكتمل إصلاح البيانات بنجاح!")
        print("✅ الآن ستظهر أسماء العملاء والموردين بشكل صحيح في جدول الأقساط")
        
    except Exception as e:
        print(f"\n❌ خطأ في إصلاح البيانات: {str(e)}")
        session.rollback()
        
    finally:
        session.close()

if __name__ == "__main__":
    main()

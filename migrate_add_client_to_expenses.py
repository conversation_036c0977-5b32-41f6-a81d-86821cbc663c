#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عمود العميل إلى جدول المصروفات
Add Client Column to Expenses Table

هذا الملف يقوم بتحديث قاعدة البيانات لإضافة عمود client_id إلى جدول expenses
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import engine, get_session
from sqlalchemy import text
import sqlite3

def migrate_add_client_to_expenses():
    """إضافة عمود العميل إلى جدول المصروفات"""
    
    print("🔧 بدء تحديث قاعدة البيانات...")
    print("📋 إضافة عمود client_id إلى جدول expenses")
    
    try:
        # الاتصال بقاعدة البيانات مباشرة
        connection = engine.connect()
        
        # فحص إذا كان العمود موجود بالفعل
        print("🔍 فحص وجود عمود client_id...")
        
        try:
            result = connection.execute(text("PRAGMA table_info(expenses)"))
            columns = [row[1] for row in result.fetchall()]
            
            print(f"📊 الأعمدة الحالية في جدول expenses: {columns}")
            
            if 'client_id' in columns:
                print("✅ عمود client_id موجود بالفعل")
                return True
            
        except Exception as pragma_error:
            print(f"⚠️ خطأ في فحص الأعمدة: {str(pragma_error)}")
        
        # إضافة العمود الجديد
        print("🔨 إضافة عمود client_id...")
        
        try:
            # إضافة العمود
            connection.execute(text("ALTER TABLE expenses ADD COLUMN client_id INTEGER"))
            print("✅ تم إضافة عمود client_id")
            
            # إضافة فهرس للأداء
            connection.execute(text("CREATE INDEX IF NOT EXISTS idx_expenses_client_id ON expenses(client_id)"))
            print("✅ تم إضافة فهرس idx_expenses_client_id")
            
            # إضافة فهرس مركب للتاريخ والعميل
            connection.execute(text("CREATE INDEX IF NOT EXISTS idx_expense_client_date ON expenses(client_id, date)"))
            print("✅ تم إضافة فهرس idx_expense_client_date")
            
            # تأكيد التغييرات
            connection.commit()
            print("💾 تم حفظ التغييرات")
            
        except Exception as alter_error:
            print(f"❌ خطأ في إضافة العمود: {str(alter_error)}")
            connection.rollback()
            return False
        
        # التحقق من نجاح العملية
        print("🔍 التحقق من نجاح العملية...")
        
        try:
            result = connection.execute(text("PRAGMA table_info(expenses)"))
            columns = [row[1] for row in result.fetchall()]
            
            if 'client_id' in columns:
                print("✅ تم التحقق من وجود عمود client_id")
                print(f"📊 الأعمدة الجديدة: {columns}")
                
                # عرض عدد المصروفات
                result = connection.execute(text("SELECT COUNT(*) FROM expenses"))
                count = result.fetchone()[0]
                print(f"📊 عدد المصروفات في الجدول: {count}")
                
                return True
            else:
                print("❌ فشل في إضافة العمود")
                return False
                
        except Exception as verify_error:
            print(f"❌ خطأ في التحقق: {str(verify_error)}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في تحديث قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'connection' in locals():
            connection.close()

def test_updated_database():
    """اختبار قاعدة البيانات المحدثة"""
    
    print("\n🧪 اختبار قاعدة البيانات المحدثة...")
    
    try:
        session = get_session()
        
        # استيراد النماذج
        from database import Expense, Client, Supplier
        
        # فحص إمكانية الاستعلام
        print("🔍 فحص إمكانية الاستعلام...")
        
        # عدد المصروفات
        total_expenses = session.query(Expense).count()
        print(f"📊 إجمالي المصروفات: {total_expenses}")
        
        # عدد المصروفات المرتبطة بعملاء
        expenses_with_clients = session.query(Expense).filter(Expense.client_id.isnot(None)).count()
        print(f"📊 المصروفات المرتبطة بعملاء: {expenses_with_clients}")
        
        # عدد العملاء
        total_clients = session.query(Client).count()
        print(f"📊 إجمالي العملاء: {total_clients}")
        
        # اختبار إنشاء مصروف جديد مع عميل
        if total_clients > 0:
            print("🔧 اختبار إنشاء مصروف مع عميل...")
            
            client = session.query(Client).first()
            
            test_expense = Expense(
                title="مصروف اختبار التحديث",
                amount=100.0,
                category="اختبار",
                client_id=client.id,
                notes="مصروف لاختبار التحديث"
            )
            
            session.add(test_expense)
            session.commit()
            
            print(f"✅ تم إنشاء مصروف اختبار مرتبط بالعميل: {client.name}")
            
            # حذف المصروف الاختباري
            session.delete(test_expense)
            session.commit()
            print("🗑️ تم حذف المصروف الاختباري")
        
        print("✅ قاعدة البيانات تعمل بشكل صحيح!")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'session' in locals():
            session.close()

if __name__ == "__main__":
    print("🚀 بدء تحديث قاعدة البيانات")
    print("=" * 60)
    
    success = migrate_add_client_to_expenses()
    
    if success:
        test_updated_database()
        print("\n" + "=" * 60)
        print("🎉 تم تحديث قاعدة البيانات بنجاح!")
        print("✅ يمكن الآن استخدام ربط المصروفات بالعملاء")
    else:
        print("\n" + "=" * 60)
        print("❌ فشل في تحديث قاعدة البيانات")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع نوافذ التحذير المتطورة للأقساط
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer

# استيراد النوافذ المتطورة الجديدة
from ui.installments import (
    InstallmentWarningDialog,
    InstallmentErrorDialog,
    InstallmentInfoDialog,
    InstallmentConfirmationDialog,
    InstallmentSuccessDialog,
    InstallmentProgressDialog,
    InstallmentMultiChoiceDialog,
    DeleteInstallmentDialog,
    show_installment_advanced_warning,
    show_installment_advanced_error,
    show_installment_advanced_info,
    show_installment_advanced_confirmation,
    show_installment_advanced_success,
    show_installment_advanced_progress,
    show_installment_advanced_multi_choice,
    ensure_installment_dialog_stays_visible
)

class TestInstallmentsWindow(QMainWindow):
    """نافذة اختبار النوافذ المتطورة للأقساط"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 اختبار نوافذ التحذير المتطورة للأقساط")
        self.setGeometry(100, 100, 800, 600)
        
        # إعداد الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان رئيسي
        title_label = QPushButton("💰 اختبار نوافذ التحذير المتطورة للأقساط")
        title_label.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        title_label.setEnabled(False)
        layout.addWidget(title_label)
        
        # الصف الأول - النوافذ الأساسية
        row1 = QHBoxLayout()
        
        warning_btn = QPushButton("⚠️ نافذة تحذير")
        warning_btn.clicked.connect(self.test_warning_dialog)
        self.style_button(warning_btn, '#F59E0B')
        row1.addWidget(warning_btn)
        
        error_btn = QPushButton("❌ نافذة خطأ")
        error_btn.clicked.connect(self.test_error_dialog)
        self.style_button(error_btn, '#EF4444')
        row1.addWidget(error_btn)
        
        info_btn = QPushButton("ℹ️ نافذة معلومات")
        info_btn.clicked.connect(self.test_info_dialog)
        self.style_button(info_btn, '#3B82F6')
        row1.addWidget(info_btn)
        
        layout.addLayout(row1)
        
        # الصف الثاني - النوافذ المتقدمة
        row2 = QHBoxLayout()
        
        confirm_btn = QPushButton("❓ نافذة تأكيد")
        confirm_btn.clicked.connect(self.test_confirmation_dialog)
        self.style_button(confirm_btn, '#8B5CF6')
        row2.addWidget(confirm_btn)
        
        success_btn = QPushButton("✅ نافذة نجاح")
        success_btn.clicked.connect(self.test_success_dialog)
        self.style_button(success_btn, '#10B981')
        row2.addWidget(success_btn)
        
        delete_btn = QPushButton("🗑️ نافذة حذف")
        delete_btn.clicked.connect(self.test_delete_dialog)
        self.style_button(delete_btn, '#DC2626')
        row2.addWidget(delete_btn)
        
        layout.addLayout(row2)
        
        # الصف الثالث - اختبار الدوال المساعدة
        row3 = QHBoxLayout()
        
        helper_warning_btn = QPushButton("⚠️ دالة التحذير")
        helper_warning_btn.clicked.connect(self.test_helper_warning)
        self.style_button(helper_warning_btn, '#F59E0B')
        row3.addWidget(helper_warning_btn)
        
        helper_error_btn = QPushButton("❌ دالة الخطأ")
        helper_error_btn.clicked.connect(self.test_helper_error)
        self.style_button(helper_error_btn, '#EF4444')
        row3.addWidget(helper_error_btn)
        
        helper_info_btn = QPushButton("ℹ️ دالة المعلومات")
        helper_info_btn.clicked.connect(self.test_helper_info)
        self.style_button(helper_info_btn, '#3B82F6')
        row3.addWidget(helper_info_btn)
        
        layout.addLayout(row3)
        
        # الصف الرابع - المزيد من الدوال المساعدة
        row4 = QHBoxLayout()
        
        helper_confirm_btn = QPushButton("❓ دالة التأكيد")
        helper_confirm_btn.clicked.connect(self.test_helper_confirmation)
        self.style_button(helper_confirm_btn, '#8B5CF6')
        row4.addWidget(helper_confirm_btn)
        
        helper_success_btn = QPushButton("✅ دالة النجاح")
        helper_success_btn.clicked.connect(self.test_helper_success)
        self.style_button(helper_success_btn, '#10B981')
        row4.addWidget(helper_success_btn)
        
        test_all_btn = QPushButton("🧪 اختبار جميع النوافذ")
        test_all_btn.clicked.connect(self.test_all_dialogs)
        self.style_button(test_all_btn, '#6366F1')
        row4.addWidget(test_all_btn)

        layout.addLayout(row4)

        # الصف الخامس - النوافذ المتقدمة الجديدة
        row5 = QHBoxLayout()

        progress_btn = QPushButton("⏳ نافذة التقدم")
        progress_btn.clicked.connect(self.test_progress_dialog)
        self.style_button(progress_btn, '#6366F1')
        row5.addWidget(progress_btn)

        multi_choice_btn = QPushButton("🔀 نافذة خيارات متعددة")
        multi_choice_btn.clicked.connect(self.test_multi_choice_dialog)
        self.style_button(multi_choice_btn, '#A855F7')
        row5.addWidget(multi_choice_btn)

        helper_progress_btn = QPushButton("⏳ دالة التقدم")
        helper_progress_btn.clicked.connect(self.test_helper_progress)
        self.style_button(helper_progress_btn, '#6366F1')
        row5.addWidget(helper_progress_btn)

        layout.addLayout(row5)
        
        # إضافة مساحة فارغة
        layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق الاختبار")
        close_btn.clicked.connect(self.close)
        self.style_button(close_btn, '#6B7280')
        layout.addWidget(close_btn)
    
    def style_button(self, button, color):
        """تطبيق تصميم على الأزرار"""
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 12px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
                transform: translateY(0px);
            }}
        """)
    
    def test_warning_dialog(self):
        """اختبار نافذة التحذير"""
        dialog = InstallmentWarningDialog(
            self, 
            "تحذير مهم", 
            "هذا مثال على نافذة تحذير متطورة للأقساط.\nتم تصميمها لتتطابق مع نافذة الحذف المرجعية.",
            "⚠️"
        )
        dialog.exec_()
    
    def test_error_dialog(self):
        """اختبار نافذة الخطأ"""
        dialog = InstallmentErrorDialog(
            self, 
            "خطأ في النظام", 
            "حدث خطأ غير متوقع في النظام.\nيرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.",
            "❌"
        )
        dialog.exec_()
    
    def test_info_dialog(self):
        """اختبار نافذة المعلومات"""
        dialog = InstallmentInfoDialog(
            self, 
            "معلومات مفيدة", 
            "هذه نافذة معلومات متطورة للأقساط.\nيمكن استخدامها لعرض معلومات مهمة للمستخدم.",
            "ℹ️"
        )
        dialog.exec_()
    
    def test_confirmation_dialog(self):
        """اختبار نافذة التأكيد"""
        dialog = InstallmentConfirmationDialog(
            self, 
            "تأكيد العملية", 
            "هل أنت متأكد من أنك تريد المتابعة؟\nهذا الإجراء لا يمكن التراجع عنه.",
            "❓"
        )
        result = dialog.exec_()
        if dialog.result:
            self.test_helper_success()
        else:
            self.test_helper_info()
    
    def test_success_dialog(self):
        """اختبار نافذة النجاح"""
        dialog = InstallmentSuccessDialog(
            self, 
            "تمت العملية بنجاح", 
            "تم تنفيذ العملية بنجاح!\nجميع البيانات محفوظة بأمان.",
            "✅"
        )
        dialog.exec_()
    
    def test_delete_dialog(self):
        """اختبار نافذة الحذف"""
        # إنشاء كائن وهمي للقسط
        class MockInstallment:
            def __init__(self):
                self.total_amount = 5000
                self.status = 'pending'
                self.client = MockClient()
        
        class MockClient:
            def __init__(self):
                self.name = "عميل تجريبي"
        
        mock_installment = MockInstallment()
        dialog = DeleteInstallmentDialog(self, mock_installment)
        result = dialog.exec_()
        if result:
            self.test_helper_success()
    
    def test_helper_warning(self):
        """اختبار دالة التحذير المساعدة"""
        show_installment_advanced_warning(
            self, 
            "تحذير من الدالة المساعدة", 
            "هذا تحذير تم عرضه باستخدام الدالة المساعدة."
        )
    
    def test_helper_error(self):
        """اختبار دالة الخطأ المساعدة"""
        show_installment_advanced_error(
            self, 
            "خطأ من الدالة المساعدة", 
            "هذا خطأ تم عرضه باستخدام الدالة المساعدة."
        )
    
    def test_helper_info(self):
        """اختبار دالة المعلومات المساعدة"""
        show_installment_advanced_info(
            self, 
            "معلومات من الدالة المساعدة", 
            "هذه معلومات تم عرضها باستخدام الدالة المساعدة."
        )
    
    def test_helper_confirmation(self):
        """اختبار دالة التأكيد المساعدة"""
        result = show_installment_advanced_confirmation(
            self, 
            "تأكيد من الدالة المساعدة", 
            "هل تريد المتابعة؟ (باستخدام الدالة المساعدة)"
        )
        if result:
            show_installment_advanced_success(self, "ممتاز!", "اخترت نعم!")
        else:
            show_installment_advanced_info(self, "حسناً", "اخترت لا.")
    
    def test_helper_success(self):
        """اختبار دالة النجاح المساعدة"""
        show_installment_advanced_success(
            self, 
            "نجح من الدالة المساعدة", 
            "هذا نجاح تم عرضه باستخدام الدالة المساعدة."
        )
    
    def test_progress_dialog(self):
        """اختبار نافذة التقدم"""
        dialog = InstallmentProgressDialog(
            self,
            "جاري المعالجة",
            "يرجى الانتظار بينما يتم معالجة الأقساط...\nهذا قد يستغرق بضع ثوان.",
            "⏳"
        )
        dialog.show()
        # إغلاق تلقائي بعد 3 ثوان
        QTimer.singleShot(3000, dialog.accept)

    def test_multi_choice_dialog(self):
        """اختبار نافذة الخيارات المتعددة"""
        choices = [
            "تصدير إلى PDF",
            "تصدير إلى Excel",
            "تصدير إلى CSV",
            "طباعة التقرير",
            "إرسال بالإيميل"
        ]

        dialog = InstallmentMultiChoiceDialog(
            self,
            "اختر نوع التصدير",
            "يرجى اختيار نوع التصدير المطلوب من الخيارات التالية:",
            choices,
            "🔀"
        )
        result = dialog.exec_()
        if dialog.selected_choice:
            show_installment_advanced_success(
                self,
                "تم الاختيار",
                f"اخترت: {dialog.selected_choice}"
            )

    def test_helper_progress(self):
        """اختبار دالة التقدم المساعدة"""
        progress_dialog = show_installment_advanced_progress(
            self,
            "جاري التحميل",
            "يتم تحميل بيانات الأقساط من الخادم..."
        )
        # محاكاة عملية طويلة
        QTimer.singleShot(2500, progress_dialog.accept)

    def test_all_dialogs(self):
        """اختبار جميع النوافذ بالتتابع"""
        QTimer.singleShot(500, self.test_warning_dialog)
        QTimer.singleShot(1500, self.test_error_dialog)
        QTimer.singleShot(2500, self.test_info_dialog)
        QTimer.singleShot(3500, self.test_success_dialog)
        QTimer.singleShot(4500, self.test_progress_dialog)
        QTimer.singleShot(6000, self.test_multi_choice_dialog)
        QTimer.singleShot(7500, lambda: show_installment_advanced_info(
            self, "انتهى الاختبار", "تم اختبار جميع النوافذ بنجاح!"
        ))

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق تصميم عام
    app.setStyleSheet("""
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.5 #e2e8f0, stop:1 #cbd5e0);
        }
    """)
    
    window = TestInstallmentsWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

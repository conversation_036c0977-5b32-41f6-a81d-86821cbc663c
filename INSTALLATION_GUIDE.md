# 🚀 دليل التثبيت والتشغيل - الأنظمة المتقدمة

## 📋 نظرة عامة على الأنظمة الجديدة

تم إضافة **6 أنظمة متقدمة** جديدة للبرنامج:

### ✅ الأولوية الأولى (مُنفذة):
1. **📁 ملف إعدادات مركزي** (`config.py`)
2. **📝 نظام تسجيل الأخطاء** (`logging_config.py`) 
3. **💾 نظام النسخ الاحتياطي التلقائي** (`backup_manager.py`)

### ✅ الأولوية الثانية (مُنفذة):
4. **🔐 نظام إدارة الصلاحيات المتقدم** (`permissions_manager.py`)
5. **🛡️ إعدادات الأمان والتشفير** (`security_manager.py`)
6. **📊 نظام مراقبة الأداء** (`performance_monitor.py`)

---

## 🔧 متطلبات التثبيت

### المتطلبات الأساسية:
```bash
Python 3.13.3 أو أحدث
```

### المكتبات المطلوبة:
```bash
# المكتبات الأساسية (موجودة مسبقاً)
PyQt5==5.15.10
SQLAlchemy==2.0.23
psutil==5.9.6

# المكتبات الجديدة للأنظمة المتقدمة
cryptography==41.0.7    # للتشفير والأمان
bcrypt==4.1.2           # لتشفير كلمات المرور
schedule==1.2.0         # للنسخ الاحتياطي التلقائي
```

---

## 📦 خطوات التثبيت

### الطريقة الأولى: التثبيت التلقائي (موصى بها)
```bash
# 1. تفعيل البيئة الافتراضية
venv\Scripts\activate

# 2. تثبيت جميع المتطلبات
pip install -r requirements.txt

# 3. تشغيل البرنامج
python main.py
```

### الطريقة الثانية: التثبيت اليدوي
```bash
# تفعيل البيئة الافتراضية
venv\Scripts\activate

# تثبيت المكتبات الجديدة واحدة تلو الأخرى
pip install cryptography==41.0.7
pip install bcrypt==4.1.2
pip install schedule==1.2.0

# تشغيل البرنامج
python main.py
```

### الطريقة الثالثة: التشغيل بدون الأنظمة المتقدمة
```bash
# إذا فشل التثبيت، يمكن تشغيل البرنامج بالأنظمة الأساسية فقط
python main.py
```

---

## 🎯 التحقق من التثبيت

### اختبار الأنظمة المتقدمة:
```bash
# تشغيل اختبار شامل للأنظمة
python system_initializer.py
```

### رسائل النجاح المتوقعة:
```
🚀 بدء تهيئة الأنظمة المتقدمة...
✅ تم تهيئة الإعدادات المركزية
✅ تم تهيئة نظام السجلات المتقدم
✅ تم تهيئة نظام الأمان والتشفير
✅ تم تهيئة نظام الصلاحيات
✅ تم تهيئة نظام النسخ الاحتياطي التلقائي
✅ تم تهيئة نظام مراقبة الأداء
🎉 تم تهيئة جميع الأنظمة بنجاح!
```

---

## 📁 الملفات والمجلدات الجديدة

### الملفات الأساسية:
- `config.py` - الإعدادات المركزية
- `logging_config.py` - نظام السجلات
- `backup_manager.py` - النسخ الاحتياطي
- `permissions_manager.py` - الصلاحيات
- `security_manager.py` - الأمان والتشفير
- `performance_monitor.py` - مراقبة الأداء
- `system_initializer.py` - مُهيئ النظام

### المجلدات الجديدة (تُنشأ تلقائياً):
- `logs/` - ملفات السجلات
- `data/` - البيانات المشفرة
- `temp/` - الملفات المؤقتة

---

## 🚀 طرق التشغيل

### 1. التشغيل العادي (مع الأنظمة المتقدمة):
```bash
python main.py
```

### 2. التشغيل الآمن (إذا كانت هناك مشاكل):
```bash
python run.py
```

### 3. التشغيل مع شاشة تحميل:
```bash
python main_safe.py
```

### 4. التشغيل الصامت:
```bash
python main_silent.py
```

---

## 🔍 استكشاف الأخطاء

### المشكلة: "الأنظمة المتقدمة غير متاحة"
**الحل:**
```bash
pip install cryptography bcrypt schedule
```

### المشكلة: خطأ في التشفير
**الحل:**
```bash
pip uninstall cryptography
pip install cryptography==41.0.7
```

### المشكلة: خطأ في bcrypt
**الحل:**
```bash
pip install --upgrade pip
pip install bcrypt==4.1.2
```

### المشكلة: خطأ في schedule
**الحل:**
```bash
pip install schedule==1.2.0
```

---

## 📊 مراقبة الأنظمة

### فحص حالة الأنظمة:
```python
from system_initializer import get_systems_status
status = get_systems_status()
print(f"معدل النجاح: {status['success_rate']:.1f}%")
```

### عرض السجلات:
```bash
# عرض السجل الرئيسي
type logs\application.log

# عرض سجل الأخطاء
type logs\errors.log

# عرض سجل الأمان
type logs\security.log
```

### فحص النسخ الاحتياطية:
```python
from backup_manager import backup_manager
backups = backup_manager.get_backup_list()
print(f"عدد النسخ الاحتياطية: {len(backups)}")
```

---

## 🎉 الميزات الجديدة

### 🔐 الأمان المتقدم:
- تشفير البيانات الحساسة
- تشفير كلمات المرور بـ bcrypt
- حماية من SQL Injection
- تنظيف المدخلات الضارة

### 📝 السجلات المتقدمة:
- سجلات ملونة في وحدة التحكم
- تصنيف السجلات (رئيسي، أمان، أداء، أخطاء)
- تنظيف السجلات القديمة تلقائياً
- تسجيل الاستثناءات مع التفاصيل

### 💾 النسخ الاحتياطي الذكي:
- نسخ احتياطي تلقائي كل 6 ساعات
- ضغط النسخ الاحتياطية
- التحقق من سلامة النسخ
- تنظيف النسخ القديمة

### 🔑 إدارة الصلاحيات:
- 5 أدوار مختلفة للمستخدمين
- 25+ صلاحية مختلفة
- إدارة الجلسات المتقدمة
- سجل تدقيق شامل

### 📊 مراقبة الأداء:
- مراقبة استخدام الذاكرة والمعالج
- تتبع الاستعلامات البطيئة
- تنبيهات الأداء التلقائية
- تقارير أداء مفصلة

### ⚙️ الإعدادات المركزية:
- إعدادات موحدة لجميع الأنظمة
- سهولة التخصيص والتعديل
- إعدادات منفصلة لكل نظام
- إنشاء المجلدات تلقائياً

---

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من السجلات:** `logs/errors.log`
2. **شغل الاختبار:** `python system_initializer.py`
3. **تحقق من المتطلبات:** `pip list`
4. **أعد تثبيت المكتبات:** `pip install -r requirements.txt --force-reinstall`

---

**✅ تم تطوير وتنفيذ جميع الأنظمة المطلوبة بنجاح!**

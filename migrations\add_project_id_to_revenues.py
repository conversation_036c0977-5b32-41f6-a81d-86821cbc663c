#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Migration: إضافة حقل project_id إلى جدول الإيرادات
تاريخ الإنشاء: 2025-01-27
الهدف: ربط الإيرادات مباشرة بالمشاريع
"""

import sqlite3
import os
import sys
from datetime import datetime

def add_project_id_to_revenues():
    """إضافة حقل project_id إلى جدول revenues"""
    
    # مسار قاعدة البيانات
    db_path = "business_management.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(revenues)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'project_id' in columns:
            print("✅ العمود project_id موجود بالفعل في جدول revenues")
            conn.close()
            return True
        
        print("🔄 إضافة العمود project_id إلى جدول revenues...")
        
        # إضافة العمود الجديد
        cursor.execute("""
            ALTER TABLE revenues 
            ADD COLUMN project_id INTEGER 
            REFERENCES projects(id)
        """)
        
        # إنشاء فهرس على العمود الجديد
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_revenue_project_id 
            ON revenues(project_id)
        """)
        
        # إنشاء فهرس مركب للمشروع والتاريخ
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_revenue_project_date 
            ON revenues(project_id, date)
        """)
        
        # إنشاء فهرس مركب للمشروع والمبلغ
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_revenue_project_amount 
            ON revenues(project_id, amount)
        """)
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إضافة العمود project_id بنجاح!")
        print("✅ تم إنشاء الفهارس المطلوبة!")
        
        # التحقق من النتيجة
        cursor.execute("PRAGMA table_info(revenues)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'project_id' in columns:
            print("✅ تم التحقق من إضافة العمود بنجاح!")
        else:
            print("❌ فشل في إضافة العمود!")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return False

def update_existing_revenues():
    """تحديث الإيرادات الموجودة لربطها بالمشاريع حسب العميل"""
    
    db_path = "business_management.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 تحديث الإيرادات الموجودة لربطها بالمشاريع...")
        
        # الحصول على الإيرادات التي لها client_id ولكن ليس لها project_id
        cursor.execute("""
            SELECT r.id, r.client_id, r.title, r.amount, r.date
            FROM revenues r
            WHERE r.client_id IS NOT NULL 
            AND r.project_id IS NULL
        """)
        
        revenues_to_update = cursor.fetchall()
        
        if not revenues_to_update:
            print("✅ لا توجد إيرادات تحتاج تحديث")
            conn.close()
            return True
        
        updated_count = 0
        
        for revenue_id, client_id, title, amount, date in revenues_to_update:
            # البحث عن مشروع نشط للعميل
            cursor.execute("""
                SELECT id, name, status 
                FROM projects 
                WHERE client_id = ? 
                AND status IN ('planning', 'in_progress', 'completed')
                ORDER BY 
                    CASE status 
                        WHEN 'in_progress' THEN 1
                        WHEN 'planning' THEN 2
                        WHEN 'completed' THEN 3
                    END,
                    start_date DESC
                LIMIT 1
            """, (client_id,))
            
            project = cursor.fetchone()
            
            if project:
                project_id, project_name, project_status = project
                
                # ربط الإيراد بالمشروع
                cursor.execute("""
                    UPDATE revenues 
                    SET project_id = ? 
                    WHERE id = ?
                """, (project_id, revenue_id))
                
                updated_count += 1
                print(f"✅ تم ربط الإيراد '{title}' بالمشروع '{project_name}'")
        
        conn.commit()
        print(f"✅ تم تحديث {updated_count} إيراد بنجاح!")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإيرادات: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return False

def main():
    """تشغيل Migration"""
    print("=" * 60)
    print("🚀 بدء Migration: إضافة ربط الإيرادات بالمشاريع")
    print("=" * 60)
    
    # إضافة العمود
    if not add_project_id_to_revenues():
        print("❌ فشل في إضافة العمود!")
        return False
    
    # تحديث البيانات الموجودة
    if not update_existing_revenues():
        print("❌ فشل في تحديث البيانات الموجودة!")
        return False
    
    print("=" * 60)
    print("✅ تم إكمال Migration بنجاح!")
    print("✅ الآن الإيرادات مرتبطة مباشرة بالمشاريع!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية لاختبار ربط الإيرادات بالمشاريع
"""

import os
import sys
from datetime import datetime, timedelta
import random

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import engine
from sqlalchemy import text

def create_test_data():
    """إنشاء بيانات تجريبية"""
    
    try:
        print("🔄 إنشاء بيانات تجريبية...")
        
        with engine.connect() as conn:
            # إنشاء عميل تجريبي
            conn.execute(text("""
                INSERT OR IGNORE INTO clients (id, name, address, email, phone, notes, balance)
                VALUES (999, 'عميل تجريبي', 'القاهرة', '<EMAIL>', '01234567890', 'عميل للاختبار', 0)
            """))
            
            # إنشاء مشروع تجريبي
            conn.execute(text("""
                INSERT OR IGNORE INTO projects (id, name, description, client_id, budget, total_cost, status, start_date, expected_end_date)
                VALUES (999, 'مشروع تجريبي', 'مشروع للاختبار', 999, 100000, 50000, 'in_progress', :start_date, :end_date)
            """), {
                "start_date": datetime.now() - timedelta(days=30),
                "end_date": datetime.now() + timedelta(days=60)
            })
            
            # إنشاء إيرادات تجريبية مرتبطة بالمشروع
            test_revenues = [
                ("دفعة أولى من المشروع", 30000),
                ("دفعة ثانية من المشروع", 25000),
                ("دفعة نهائية من المشروع", 20000),
            ]
            
            for title, amount in test_revenues:
                conn.execute(text("""
                    INSERT OR IGNORE INTO revenues (title, amount, date, category, client_id, project_id, notes)
                    VALUES (:title, :amount, :date, 'إيرادات المشاريع', 999, 999, 'إيراد تجريبي')
                """), {
                    "title": title,
                    "amount": amount,
                    "date": datetime.now() - timedelta(days=random.randint(1, 30))
                })
            
            # إنشاء مشروع ثاني
            conn.execute(text("""
                INSERT OR IGNORE INTO projects (id, name, description, client_id, budget, total_cost, status, start_date, expected_end_date)
                VALUES (998, 'مشروع تجريبي ثاني', 'مشروع آخر للاختبار', 999, 80000, 40000, 'completed', :start_date, :end_date)
            """), {
                "start_date": datetime.now() - timedelta(days=90),
                "end_date": datetime.now() - timedelta(days=10)
            })
            
            # إيرادات للمشروع الثاني
            conn.execute(text("""
                INSERT OR IGNORE INTO revenues (title, amount, date, category, client_id, project_id, notes)
                VALUES ('إيراد المشروع المكتمل', 80000, :date, 'إيرادات المشاريع', 999, 998, 'إيراد من مشروع مكتمل')
            """), {"date": datetime.now() - timedelta(days=15)})
            
            conn.commit()
            
            print("✅ تم إنشاء البيانات التجريبية بنجاح!")
            
            # عرض الإحصائيات
            result = conn.execute(text("""
                SELECT 
                    p.name as project_name,
                    p.status,
                    p.budget,
                    p.total_cost,
                    COUNT(r.id) as revenue_count,
                    COALESCE(SUM(r.amount), 0) as total_revenue
                FROM projects p
                LEFT JOIN revenues r ON p.id = r.project_id
                WHERE p.id IN (999, 998)
                GROUP BY p.id, p.name, p.status, p.budget, p.total_cost
            """))
            
            print("\n📊 إحصائيات البيانات التجريبية:")
            for row in result.fetchall():
                project_name, status, budget, total_cost, revenue_count, total_revenue = row
                profit = total_revenue - (total_cost or 0)
                print(f"   🏢 {project_name}")
                print(f"      📊 الحالة: {status}")
                print(f"      💰 الميزانية: {budget:,.2f} ج.م")
                print(f"      💸 التكلفة: {total_cost or 0:,.2f} ج.م")
                print(f"      💵 الإيرادات: {total_revenue:,.2f} ج.م ({revenue_count} إيراد)")
                print(f"      📈 الربح: {profit:,.2f} ج.م")
                print()
            
            return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")
        return False

if __name__ == "__main__":
    create_test_data()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عمود project_id إلى جدول revenues
"""

import os
import sys
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import engine
from sqlalchemy import text

def add_project_column():
    """إضافة عمود project_id إلى جدول revenues"""
    
    try:
        print("🔄 إضافة عمود project_id إلى جدول revenues...")
        
        with engine.connect() as conn:
            # التحقق من وجود العمود
            result = conn.execute(text("PRAGMA table_info(revenues)"))
            columns = [row[1] for row in result.fetchall()]
            
            if 'project_id' in columns:
                print("✅ العمود project_id موجود بالفعل")
                return True
            
            # إضافة العمود
            conn.execute(text("ALTER TABLE revenues ADD COLUMN project_id INTEGER"))
            
            # إنشاء فهرس
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_revenue_project_id ON revenues(project_id)"))
            
            conn.commit()
            
            print("✅ تم إضافة العمود project_id بنجاح!")
            
            # التحقق مرة أخرى
            result = conn.execute(text("PRAGMA table_info(revenues)"))
            columns = [row[1] for row in result.fetchall()]
            print(f"📋 أعمدة الجدول الآن: {columns}")
            
            return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {str(e)}")
        return False

def link_revenues_simple():
    """ربط الإيرادات بالمشاريع بطريقة مبسطة"""
    
    try:
        print("🔄 ربط الإيرادات بالمشاريع...")
        
        with engine.connect() as conn:
            # الحصول على الإيرادات غير المرتبطة
            result = conn.execute(text("""
                SELECT r.id, r.client_id, r.title, r.amount
                FROM revenues r
                WHERE r.project_id IS NULL AND r.client_id IS NOT NULL
            """))
            
            unlinked_revenues = result.fetchall()
            print(f"📊 تم العثور على {len(unlinked_revenues)} إيراد غير مرتبط")
            
            linked_count = 0
            
            for revenue in unlinked_revenues:
                revenue_id, client_id, title, amount = revenue
                
                # البحث عن مشروع للعميل
                project_result = conn.execute(text("""
                    SELECT id, name FROM projects 
                    WHERE client_id = :client_id 
                    ORDER BY 
                        CASE status 
                            WHEN 'in_progress' THEN 1
                            WHEN 'planning' THEN 2
                            WHEN 'completed' THEN 3
                            ELSE 4
                        END,
                        start_date DESC
                    LIMIT 1
                """), {"client_id": client_id})
                
                project = project_result.fetchone()
                
                if project:
                    project_id, project_name = project
                    
                    # ربط الإيراد بالمشروع
                    conn.execute(text("""
                        UPDATE revenues 
                        SET project_id = :project_id 
                        WHERE id = :revenue_id
                    """), {"project_id": project_id, "revenue_id": revenue_id})
                    
                    linked_count += 1
                    print(f"✅ تم ربط '{title}' ({amount:,.2f} ج.م) بالمشروع '{project_name}'")
            
            conn.commit()
            print(f"✅ تم ربط {linked_count} إيراد بالمشاريع!")
            
            return True
        
    except Exception as e:
        print(f"❌ خطأ في ربط الإيرادات: {str(e)}")
        return False

def main():
    """تشغيل العملية"""
    
    print("🚀 بدء إضافة عمود project_id وربط الإيرادات")
    print("="*50)
    
    # إضافة العمود
    if not add_project_column():
        print("❌ فشل في إضافة العمود!")
        return False
    
    # ربط الإيرادات
    if not link_revenues_simple():
        print("❌ فشل في ربط الإيرادات!")
        return False
    
    print("\n✅ تم إكمال العملية بنجاح!")
    print("🎉 الآن يمكنك رؤية الإيرادات في تقرير المشاريع!")
    
    return True

if __name__ == "__main__":
    main()

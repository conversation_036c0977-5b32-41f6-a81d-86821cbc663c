# -*- coding: utf-8 -*-
"""
تشغيل البرنامج مع التركيز على قسم الإعدادات
Run Program with Focus on Settings Section
"""

import sys
import os
from pathlib import Path

def main():
    """تشغيل البرنامج والانتقال لقسم الإعدادات"""
    print("🚀 تشغيل البرنامج مع التركيز على قسم الإعدادات...")
    
    try:
        # استيراد البرنامج الرئيسي
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt, QTimer
        from ui.main_window import MainWindow
        from database import init_db
        
        # إعداد التطبيق
        app = QApplication(sys.argv)
        app.setStyle("Fusion")
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد قاعدة البيانات
        init_db()
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        window.show()
        
        # الانتقال لقسم الإعدادات بعد ثانيتين
        def switch_to_settings():
            try:
                # التبديل إلى تبويب الإعدادات (رقم 12)
                window.tabs.setCurrentIndex(12)
                print("✅ تم التبديل إلى قسم الإعدادات")
            except Exception as e:
                print(f"❌ خطأ في التبديل للإعدادات: {e}")
        
        # تأخير التبديل للإعدادات
        QTimer.singleShot(2000, switch_to_settings)
        
        print("🎉 تم تشغيل البرنامج بنجاح!")
        print("💡 سيتم التبديل لقسم الإعدادات تلقائياً...")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
نظام تسجيل الأخطاء والأحداث المتقدم
Advanced Logging System for Error and Event Tracking
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
import traceback
import json
from config import LoggingConfig, AppConfig

class ColoredFormatter(logging.Formatter):
    """مُنسق ملون للسجلات في وحدة التحكم"""
    
    # ألوان ANSI
    COLORS = {
        'DEBUG': '\033[36m',      # سماوي
        'INFO': '\033[32m',       # أخضر
        'WARNING': '\033[33m',    # أصفر
        'ERROR': '\033[31m',      # أحمر
        'CRITICAL': '\033[35m',   # بنفسجي
        'RESET': '\033[0m'        # إعادة تعيين
    }
    
    def format(self, record):
        # إضافة اللون للمستوى
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class DatabaseLogHandler(logging.Handler):
    """معالج لحفظ السجلات في قاعدة البيانات"""
    
    def __init__(self, session=None):
        super().__init__()
        self.session = session
        
    def emit(self, record):
        """حفظ السجل في قاعدة البيانات"""
        if not self.session:
            return
            
        try:
            # تحويل السجل إلى نص
            log_entry = self.format(record)
            
            # حفظ في قاعدة البيانات (يمكن إضافة جدول خاص بالسجلات)
            # هذا مثال بسيط - يمكن تطويره أكثر
            pass
            
        except Exception:
            # تجنب الأخطاء في نظام السجلات نفسه
            pass

class SmartLogger:
    """نظام السجلات الذكي المتقدم"""
    
    def __init__(self):
        self.loggers = {}
        self.handlers = {}
        self.setup_logging()
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        try:
            # إنشاء مجلد السجلات
            AppConfig.LOGS_DIR.mkdir(exist_ok=True)
            
            # إعداد السجلات المختلفة
            self._setup_main_logger()
            self._setup_database_logger()
            self._setup_security_logger()
            self._setup_performance_logger()
            self._setup_error_logger()
            
            print("✅ تم إعداد نظام السجلات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد نظام السجلات: {e}")
    
    def _setup_main_logger(self):
        """إعداد السجل الرئيسي"""
        logger = logging.getLogger('main')
        logger.setLevel(getattr(logging, LoggingConfig.LOG_LEVEL))
        
        # معالج الملف
        file_handler = logging.handlers.RotatingFileHandler(
            AppConfig.LOGS_DIR / LoggingConfig.LOG_FILES['main'],
            maxBytes=LoggingConfig.LOG_FILE_MAX_SIZE,
            backupCount=LoggingConfig.LOG_FILE_BACKUP_COUNT,
            encoding=LoggingConfig.LOG_FILE_ENCODING
        )
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(ColoredFormatter(LoggingConfig.LOG_FORMAT))
        
        # تنسيق الملف
        file_formatter = logging.Formatter(
            LoggingConfig.LOG_FORMAT,
            datefmt=LoggingConfig.DATE_FORMAT
        )
        file_handler.setFormatter(file_formatter)
        
        # إضافة المعالجات
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        self.loggers['main'] = logger
        self.handlers['main_file'] = file_handler
        self.handlers['main_console'] = console_handler
    
    def _setup_database_logger(self):
        """إعداد سجل قاعدة البيانات"""
        logger = logging.getLogger('database')
        logger.setLevel(logging.INFO)
        
        file_handler = logging.handlers.RotatingFileHandler(
            AppConfig.LOGS_DIR / LoggingConfig.LOG_FILES['database'],
            maxBytes=LoggingConfig.LOG_FILE_MAX_SIZE,
            backupCount=LoggingConfig.LOG_FILE_BACKUP_COUNT,
            encoding=LoggingConfig.LOG_FILE_ENCODING
        )
        
        formatter = logging.Formatter(
            "%(asctime)s - DATABASE - %(levelname)s - %(message)s",
            datefmt=LoggingConfig.DATE_FORMAT
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        self.loggers['database'] = logger
        self.handlers['database'] = file_handler
    
    def _setup_security_logger(self):
        """إعداد سجل الأمان"""
        logger = logging.getLogger('security')
        logger.setLevel(logging.WARNING)
        
        file_handler = logging.handlers.RotatingFileHandler(
            AppConfig.LOGS_DIR / LoggingConfig.LOG_FILES['security'],
            maxBytes=LoggingConfig.LOG_FILE_MAX_SIZE,
            backupCount=LoggingConfig.LOG_FILE_BACKUP_COUNT,
            encoding=LoggingConfig.LOG_FILE_ENCODING
        )
        
        formatter = logging.Formatter(
            "%(asctime)s - SECURITY - %(levelname)s - %(message)s",
            datefmt=LoggingConfig.DATE_FORMAT
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        self.loggers['security'] = logger
        self.handlers['security'] = file_handler
    
    def _setup_performance_logger(self):
        """إعداد سجل الأداء"""
        logger = logging.getLogger('performance')
        logger.setLevel(logging.INFO)
        
        file_handler = logging.handlers.RotatingFileHandler(
            AppConfig.LOGS_DIR / LoggingConfig.LOG_FILES['performance'],
            maxBytes=LoggingConfig.LOG_FILE_MAX_SIZE,
            backupCount=LoggingConfig.LOG_FILE_BACKUP_COUNT,
            encoding=LoggingConfig.LOG_FILE_ENCODING
        )
        
        formatter = logging.Formatter(
            "%(asctime)s - PERFORMANCE - %(levelname)s - %(message)s",
            datefmt=LoggingConfig.DATE_FORMAT
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        self.loggers['performance'] = logger
        self.handlers['performance'] = file_handler
    
    def _setup_error_logger(self):
        """إعداد سجل الأخطاء"""
        logger = logging.getLogger('errors')
        logger.setLevel(logging.ERROR)
        
        file_handler = logging.handlers.RotatingFileHandler(
            AppConfig.LOGS_DIR / LoggingConfig.LOG_FILES['errors'],
            maxBytes=LoggingConfig.LOG_FILE_MAX_SIZE,
            backupCount=LoggingConfig.LOG_FILE_BACKUP_COUNT,
            encoding=LoggingConfig.LOG_FILE_ENCODING
        )
        
        formatter = logging.Formatter(
            "%(asctime)s - ERROR - %(levelname)s - %(message)s - %(pathname)s:%(lineno)d",
            datefmt=LoggingConfig.DATE_FORMAT
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        self.loggers['errors'] = logger
        self.handlers['errors'] = file_handler
    
    def get_logger(self, name='main'):
        """الحصول على سجل محدد"""
        return self.loggers.get(name, self.loggers['main'])
    
    def log_exception(self, exception, context="", logger_name='errors'):
        """تسجيل استثناء مع تفاصيل كاملة"""
        logger = self.get_logger(logger_name)
        
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'context': context,
            'traceback': traceback.format_exc()
        }
        
        logger.error(f"Exception occurred: {json.dumps(error_info, ensure_ascii=False, indent=2)}")
    
    def log_security_event(self, event_type, details, severity='WARNING'):
        """تسجيل حدث أمني"""
        logger = self.get_logger('security')
        
        security_event = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'details': details,
            'severity': severity
        }
        
        log_method = getattr(logger, severity.lower(), logger.warning)
        log_method(f"Security Event: {json.dumps(security_event, ensure_ascii=False, indent=2)}")
    
    def log_performance_metric(self, metric_name, value, unit='', context=''):
        """تسجيل مقياس أداء"""
        logger = self.get_logger('performance')
        
        performance_metric = {
            'timestamp': datetime.now().isoformat(),
            'metric_name': metric_name,
            'value': value,
            'unit': unit,
            'context': context
        }
        
        logger.info(f"Performance Metric: {json.dumps(performance_metric, ensure_ascii=False, indent=2)}")
    
    def log_database_operation(self, operation, table, details='', execution_time=None):
        """تسجيل عملية قاعدة بيانات"""
        logger = self.get_logger('database')
        
        db_operation = {
            'timestamp': datetime.now().isoformat(),
            'operation': operation,
            'table': table,
            'details': details,
            'execution_time': execution_time
        }
        
        logger.info(f"Database Operation: {json.dumps(db_operation, ensure_ascii=False, indent=2)}")
    
    def cleanup_old_logs(self, days_to_keep=30):
        """تنظيف السجلات القديمة"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            for log_file in AppConfig.LOGS_DIR.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    self.get_logger().info(f"تم حذف السجل القديم: {log_file.name}")
                    
        except Exception as e:
            self.log_exception(e, "خطأ في تنظيف السجلات القديمة")

    def log_info(self, message, logger_name='main'):
        """تسجيل معلومة"""
        try:
            self.get_logger(logger_name).info(message)
        except Exception:
            pass

    def log_warning(self, message, logger_name='main'):
        """تسجيل تحذير"""
        try:
            self.get_logger(logger_name).warning(message)
        except Exception:
            pass

    def log_error(self, message, logger_name='main'):
        """تسجيل خطأ"""
        try:
            self.get_logger(logger_name).error(message)
        except Exception:
            pass

    def log_debug(self, message, logger_name='main'):
        """تسجيل تصحيح"""
        try:
            self.get_logger(logger_name).debug(message)
        except Exception:
            pass

# إنشاء مثيل عام من نظام السجلات
smart_logger = SmartLogger()

# دوال مساعدة سريعة
def log_info(message, logger_name='main'):
    """تسجيل معلومة"""
    smart_logger.get_logger(logger_name).info(message)

def log_warning(message, logger_name='main'):
    """تسجيل تحذير"""
    smart_logger.get_logger(logger_name).warning(message)

def log_error(message, logger_name='main'):
    """تسجيل خطأ"""
    smart_logger.get_logger(logger_name).error(message)

def log_debug(message, logger_name='main'):
    """تسجيل معلومة تطوير"""
    smart_logger.get_logger(logger_name).debug(message)

def log_critical(message, logger_name='main'):
    """تسجيل خطأ حرج"""
    smart_logger.get_logger(logger_name).critical(message)

# معالج الاستثناءات العام
def handle_exception(exc_type, exc_value, exc_traceback):
    """معالج الاستثناءات العام للتطبيق"""
    if issubclass(exc_type, KeyboardInterrupt):
        # السماح بـ Ctrl+C
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # تسجيل الاستثناء
    smart_logger.log_exception(exc_value, "Unhandled Exception")

# تعيين معالج الاستثناءات العام
sys.excepthook = handle_exception

if __name__ == "__main__":
    # اختبار نظام السجلات
    log_info("تم بدء تشغيل نظام السجلات")
    log_warning("هذا تحذير تجريبي")
    log_error("هذا خطأ تجريبي")
    
    try:
        # محاولة إثارة استثناء للاختبار
        raise ValueError("خطأ تجريبي للاختبار")
    except Exception as e:
        smart_logger.log_exception(e, "اختبار تسجيل الاستثناءات")
    
    print("✅ تم اختبار نظام السجلات بنجاح")

# نظام ربط الأرصدة السالبة بقسم الأقساط تلقائياً (مبسط)

## نظرة عامة
تم تطوير نظام تلقائي مبسط لربط العملاء والموردين بقسم الأقساط عند وجود أي عميل أو مورد برصيد سالب. يتم عرض **اسم العميل أو المورد ورصيده فقط** في قسم الأقساط تلقائياً بدون تدخل يدوي.

## كيفية عمل النظام

### 1. الربط التلقائي الفوري
- **عند إضافة عميل جديد**: إذا كان الرصيد سالباً، يتم إنشاء قسط تلقائياً
- **عند تعديل رصيد عميل**: إذا تحول الرصيد من موجب إلى سالب، يتم إنشاء قسط تلقائياً
- **عند إضافة مورد جديد**: إذا كان الرصيد سالباً، يتم إنشاء قسط تلقائياً
- **عند تعديل رصيد مورد**: إذا تحول الرصيد من موجب إلى سالب، يتم إنشاء قسط تلقائياً

### 2. معالجة الموردين (مبسطة)
نظراً لأن قسم الأقساط مخصص للعملاء، يتم إنشاء "عميل افتراضي - الموردين" لربط الموردين بقسم الأقساط، ولكن **يتم عرض اسم المورد الأصلي** في الجدول.

### 3. تفاصيل القسط التلقائي (مبسطة)
- **رقم القسط**: يتم إنشاؤه تلقائياً بتنسيق INST-YYYY-XXXX
- **اسم العميل/المورد**: يظهر الاسم الأصلي (للعملاء) أو "مورد: اسم المورد" (للموردين)
- **المبلغ الإجمالي**: يساوي القيمة المطلقة للرصيد السالب
- **المبلغ المدفوع**: 0 (لم يتم الدفع بعد)
- **المبلغ المتبقي**: يساوي المبلغ الإجمالي
- **الحالة**: "مستحق" (pending)
- **تاريخ الاستحقاق**: اليوم الحالي + 30 يوم
- **الملاحظات**: ملاحظات مبسطة تحتوي على اسم الكيان والمبلغ والتاريخ فقط

## الملفات المحدثة

### 1. `database.py`
- **`update_client_balance()`**: محدثة لإنشاء أقساط تلقائية
- **`update_supplier_balance()`**: محدثة لإنشاء أقساط تلقائية
- **`auto_check_and_create_installments()`**: دالة للفحص الشامل
- **`update_balance_with_installment_check()`**: دالة موحدة للتحديث مع التحقق

### 2. `utils/__init__.py`
- **`auto_create_installment_for_negative_balance()`**: إنشاء قسط تلقائي
- **`check_and_create_installments_for_negative_balances()`**: فحص شامل للأرصدة السالبة

### 3. `ui/clients.py`
- **`AddClientDialog.save_changes()`**: محدثة للربط التلقائي
- **`EditClientAmountDialog.save_changes()`**: محدثة لاستخدام النظام التلقائي

### 4. `ui/suppliers.py`
- **`AddSupplierDialog.save_changes()`**: محدثة للربط التلقائي
- **`EditSupplierAmountDialog.save_changes()`**: محدثة لاستخدام النظام التلقائي

## مثال على الاستخدام المبسط

### إضافة عميل برصيد سالب
```python
# عند إضافة عميل جديد برصيد -1000 ج.م
new_client = Client(
    name="أحمد محمد",
    balance=-1000.0  # رصيد سالب
)
session.add(new_client)
session.commit()

# سيتم تلقائياً:
# 1. إنشاء قسط برقم INST-2025-XXXX
# 2. عرض "أحمد محمد" في جدول الأقساط
# 3. تعيين المبلغ الإجمالي = 1000 ج.م
# 4. ملاحظات مبسطة: "قسط تلقائي للرصيد السالب"
```

### تعديل رصيد مورد ليصبح سالباً
```python
# عند تعديل رصيد مورد من 500 ج.م إلى -1500 ج.م
update_supplier_balance(session, supplier_id, 2000, 'subtract', True)

# سيتم تلقائياً:
# 1. إنشاء قسط برقم INST-2025-XXXX
# 2. عرض "مورد: شركة المواد" في جدول الأقساط
# 3. تعيين المبلغ الإجمالي = 1500 ج.م
# 4. ملاحظات مبسطة تحتوي على اسم المورد والمبلغ
```

## المزايا

### 1. التلقائية الكاملة
- لا حاجة لتدخل يدوي
- يعمل فوراً عند تحديث الأرصدة
- يغطي جميع حالات إنشاء وتعديل العملاء والموردين

### 2. الشمولية
- يشمل العملاء والموردين
- يتعامل مع جميع طرق تحديث الأرصدة
- يحافظ على تكامل البيانات

### 3. الأمان
- معالجة شاملة للأخطاء
- تراجع تلقائي في حالة الفشل
- تسجيل مفصل للعمليات

### 4. المرونة
- إمكانية تعطيل الميزة عند الحاجة
- دعم للفحص الشامل للأرصدة الموجودة
- سهولة الصيانة والتطوير

## الاختبار
تم إنشاء ملف `test_auto_installments.py` لاختبار النظام والتأكد من عمله بشكل صحيح.

## الخلاصة
النظام المبسط يعمل الآن بشكل تلقائي كامل، حيث يتم ربط أي عميل أو مورد برصيد سالب بقسم الأقساط فوراً وتلقائياً. **يظهر في جدول الأقساط اسم العميل أو المورد الأصلي ورصيده فقط** دون تعقيدات إضافية أو بيانات مفصلة غير ضرورية.

### المميزات الجديدة:
- **عرض مبسط**: اسم العميل/المورد ورصيده فقط
- **ملاحظات مختصرة**: معلومات أساسية بدون تفاصيل معقدة
- **واجهة واضحة**: عرض اسم المورد الأصلي بدلاً من "عميل افتراضي"
- **أداء محسن**: تقليل البيانات المحفوظة والمعروضة

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة إنشاء قاعدة البيانات مع العلاقات الجديدة
"""

import os
import sys
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Base, engine, Revenue, Project, Client

def recreate_database():
    """إعادة إنشاء قاعدة البيانات"""
    
    try:
        print("🔄 إعادة إنشاء قاعدة البيانات...")
        
        # إنشاء جميع الجداول
        Base.metadata.create_all(bind=engine)
        
        print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        
        # التحقق من الجداول
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        print(f"📊 الجداول الموجودة: {tables}")
        
        # التحقق من أعمدة جدول revenues
        if 'revenues' in tables:
            columns = inspector.get_columns('revenues')
            column_names = [col['name'] for col in columns]
            print(f"📋 أعمدة جدول revenues: {column_names}")
            
            if 'project_id' in column_names:
                print("✅ العمود project_id موجود في جدول revenues")
            else:
                print("❌ العمود project_id غير موجود في جدول revenues")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعادة إنشاء قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    recreate_database()

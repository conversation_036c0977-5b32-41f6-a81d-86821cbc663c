#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Migration script to add product_id and category fields to purchases table
"""

import sqlite3
import os
from datetime import datetime

def migrate_database():
    """إضافة حقول product_id و category إلى جدول purchases"""
    
    db_path = 'accounting.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 بدء عملية إضافة الحقول الجديدة...")
        
        # التحقق من وجود الحقول أولاً
        cursor.execute("PRAGMA table_info(purchases)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة حقل product_id إذا لم يكن موجوداً
        if 'product_id' not in columns:
            cursor.execute("""
                ALTER TABLE purchases 
                ADD COLUMN product_id INTEGER REFERENCES inventory(id)
            """)
            print("✅ تم إضافة حقل product_id بنجاح")
        else:
            print("ℹ️ حقل product_id موجود بالفعل")
        
        # إضافة حقل category إذا لم يكن موجوداً
        if 'category' not in columns:
            cursor.execute("""
                ALTER TABLE purchases 
                ADD COLUMN category TEXT
            """)
            print("✅ تم إضافة حقل category بنجاح")
        else:
            print("ℹ️ حقل category موجود بالفعل")
        
        # إنشاء فهارس للحقول الجديدة
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_purchases_product_id ON purchases(product_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_purchases_category ON purchases(category)")
            print("✅ تم إنشاء الفهارس بنجاح")
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم إنشاء بعض الفهارس: {e}")
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم حفظ جميع التغييرات بنجاح")
        
        # التحقق من النتيجة النهائية
        cursor.execute("PRAGMA table_info(purchases)")
        final_columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 أعمدة جدول purchases الحالية: {', '.join(final_columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عملية Migration: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()
            print("🔒 تم إغلاق الاتصال بقاعدة البيانات")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 بدء عملية Migration لإضافة حقول المنتج والفئة")
    print("=" * 60)
    
    success = migrate_database()
    
    print("=" * 60)
    if success:
        print("✅ تمت عملية Migration بنجاح!")
        print("💡 يمكنك الآن استخدام حقول المنتج والفئة في المشتريات")
    else:
        print("❌ فشلت عملية Migration!")
        print("💡 يرجى التحقق من الأخطاء أعلاه وإعادة المحاولة")
    print("=" * 60)

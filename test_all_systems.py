# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الأنظمة المتقدمة
Comprehensive Test for All Advanced Systems
"""

import sys
import time
from pathlib import Path

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🔧 اختبار الاستيرادات الأساسية...")
    
    try:
        import config
        print("✅ تم استيراد config بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد config: {e}")
        return False
    
    try:
        import logging_config
        print("✅ تم استيراد logging_config بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد logging_config: {e}")
        return False
    
    try:
        import backup_manager
        print("✅ تم استيراد backup_manager بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد backup_manager: {e}")
        return False
    
    try:
        import permissions_manager
        print("✅ تم استيراد permissions_manager بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد permissions_manager: {e}")
        return False
    
    try:
        import security_manager
        print("✅ تم استيراد security_manager بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد security_manager: {e}")
        return False
    
    try:
        import performance_monitor
        print("✅ تم استيراد performance_monitor بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد performance_monitor: {e}")
        return False
    
    try:
        import system_initializer
        print("✅ تم استيراد system_initializer بنجاح")
    except ImportError as e:
        print(f"❌ فشل استيراد system_initializer: {e}")
        return False
    
    return True

def test_config_system():
    """اختبار نظام الإعدادات"""
    print("\n🔧 اختبار نظام الإعدادات...")
    
    try:
        from config import AppConfig, DatabaseConfig, SecurityConfig
        
        # اختبار الإعدادات الأساسية
        assert AppConfig.APP_NAME == "Advanced Management System"
        assert DatabaseConfig.DB_TYPE == "sqlite"
        assert SecurityConfig.PASSWORD_MIN_LENGTH >= 4
        
        print("✅ نظام الإعدادات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام الإعدادات: {e}")
        return False

def test_logging_system():
    """اختبار نظام السجلات"""
    print("\n📝 اختبار نظام السجلات...")
    
    try:
        from logging_config import smart_logger, log_info, log_warning, log_error
        
        # اختبار تسجيل الرسائل
        log_info("رسالة اختبار معلوماتية")
        log_warning("رسالة اختبار تحذيرية")
        log_error("رسالة اختبار خطأ")
        
        # اختبار تسجيل الاستثناءات
        try:
            raise ValueError("استثناء تجريبي للاختبار")
        except Exception as e:
            smart_logger.log_exception(e, "اختبار تسجيل الاستثناءات")
        
        print("✅ نظام السجلات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام السجلات: {e}")
        return False

def test_security_system():
    """اختبار نظام الأمان"""
    print("\n🛡️ اختبار نظام الأمان...")
    
    try:
        from security_manager import security_manager
        
        # اختبار تشفير كلمات المرور
        password = "test123"
        hashed = security_manager.hash_password(password)
        assert security_manager.verify_password(password, hashed)
        print("✅ تشفير كلمات المرور يعمل")
        
        # اختبار تشفير البيانات
        data = "بيانات سرية للاختبار"
        encrypted = security_manager.encrypt_data(data)
        decrypted = security_manager.decrypt_data(encrypted)
        assert decrypted == data
        print("✅ تشفير البيانات يعمل")
        
        # اختبار تنظيف المدخلات
        dangerous_input = "<script>alert('hack')</script>بيانات عادية"
        cleaned = security_manager.sanitize_input(dangerous_input)
        assert "<script>" not in cleaned
        print("✅ تنظيف المدخلات يعمل")
        
        print("✅ نظام الأمان يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام الأمان: {e}")
        return False

def test_permissions_system():
    """اختبار نظام الصلاحيات"""
    print("\n🔑 اختبار نظام الصلاحيات...")
    
    try:
        from permissions_manager import permissions_manager, Permission, UserRole
        
        # اختبار المصادقة
        user = permissions_manager.authenticate_user("admin", "admin")
        assert user is not None
        print("✅ مصادقة المستخدم تعمل")
        
        # اختبار الصلاحيات
        has_perm = permissions_manager.has_permission(user['role'], Permission.MANAGE_USERS)
        assert has_perm == True
        print("✅ فحص الصلاحيات يعمل")
        
        # اختبار الجلسة
        session = permissions_manager.validate_session(user['session_token'])
        assert session is not None
        print("✅ إدارة الجلسات تعمل")
        
        print("✅ نظام الصلاحيات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام الصلاحيات: {e}")
        return False

def test_backup_system():
    """اختبار نظام النسخ الاحتياطي"""
    print("\n💾 اختبار نظام النسخ الاحتياطي...")
    
    try:
        from backup_manager import backup_manager
        
        # اختبار إنشاء نسخة احتياطية
        backup_info = backup_manager.create_backup("full", compress=True)
        if backup_info:
            print("✅ إنشاء النسخة الاحتياطية يعمل")
            
            # اختبار التحقق من النسخة
            is_valid = backup_manager.verify_backup(backup_info['path'])
            if is_valid:
                print("✅ التحقق من النسخة الاحتياطية يعمل")
            else:
                print("⚠️ التحقق من النسخة الاحتياطية فشل (قد يكون طبيعي)")
        else:
            print("⚠️ إنشاء النسخة الاحتياطية فشل (قاعدة البيانات قد تكون غير موجودة)")
        
        # اختبار قائمة النسخ
        backups = backup_manager.get_backup_list()
        print(f"✅ تم العثور على {len(backups)} نسخة احتياطية")
        
        print("✅ نظام النسخ الاحتياطي يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام النسخ الاحتياطي: {e}")
        return False

def test_performance_system():
    """اختبار نظام مراقبة الأداء"""
    print("\n📊 اختبار نظام مراقبة الأداء...")
    
    try:
        from performance_monitor import performance_monitor
        
        # بدء المراقبة
        performance_monitor.start_monitoring()
        print("✅ بدء مراقبة الأداء يعمل")
        
        # انتظار قليل لجمع البيانات
        time.sleep(3)
        
        # أخذ لقطة ذاكرة
        performance_monitor.take_memory_snapshot("اختبار النظام")
        print("✅ أخذ لقطة الذاكرة يعمل")
        
        # الحصول على تقرير
        report = performance_monitor.get_performance_report(1)
        if report:
            print(f"✅ تم إنشاء تقرير الأداء ({report.get('data_points', 0)} نقطة بيانات)")
        
        # إيقاف المراقبة
        performance_monitor.stop_monitoring()
        print("✅ إيقاف مراقبة الأداء يعمل")
        
        print("✅ نظام مراقبة الأداء يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام مراقبة الأداء: {e}")
        return False

def test_system_initializer():
    """اختبار مُهيئ النظام"""
    print("\n🚀 اختبار مُهيئ النظام...")
    
    try:
        from system_initializer import system_initializer, get_systems_status
        
        # الحصول على حالة الأنظمة
        status = get_systems_status()
        print(f"✅ حالة الأنظمة: {status.get('success_rate', 0):.1f}% نجاح")
        
        # اختبار الإغلاق الآمن
        system_initializer.shutdown_all_systems()
        print("✅ إغلاق الأنظمة يعمل")
        
        print("✅ مُهيئ النظام يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مُهيئ النظام: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء الاختبار الشامل لجميع الأنظمة المتقدمة")
    print("=" * 60)
    
    tests = [
        ("الاستيرادات الأساسية", test_basic_imports),
        ("نظام الإعدادات", test_config_system),
        ("نظام السجلات", test_logging_system),
        ("نظام الأمان", test_security_system),
        ("نظام الصلاحيات", test_permissions_system),
        ("نظام النسخ الاحتياطي", test_backup_system),
        ("نظام مراقبة الأداء", test_performance_system),
        ("مُهيئ النظام", test_system_initializer),
    ]
    
    passed_tests = 0
    failed_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                failed_tests += 1
        except Exception as e:
            print(f"❌ خطأ غير متوقع في اختبار {test_name}: {e}")
            failed_tests += 1
    
    # تقرير النتائج
    print("\n" + "=" * 60)
    print("📋 تقرير نتائج الاختبار")
    print("=" * 60)
    print(f"✅ الاختبارات الناجحة: {passed_tests}")
    print(f"❌ الاختبارات الفاشلة: {failed_tests}")
    print(f"📊 معدل النجاح: {(passed_tests / (passed_tests + failed_tests)) * 100:.1f}%")
    
    if failed_tests == 0:
        print("\n🎉 جميع الاختبارات نجحت! الأنظمة جاهزة للاستخدام")
        return True
    else:
        print(f"\n⚠️ {failed_tests} اختبار فشل. يرجى مراجعة الأخطاء أعلاه")
        print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات: إضافة ربط المصروفات بالمشاريع
تاريخ الإنشاء: 2025-01-27
الهدف: إضافة حقل project_id إلى جدول المصروفات لربطها بالمشاريع
"""

import sys
import os
from sqlalchemy import text, inspect

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import engine, Session

def add_project_to_expenses():
    """إضافة حقل project_id إلى جدول المصروفات"""
    
    print("🚀 بدء تحديث قاعدة البيانات: ربط المصروفات بالمشاريع")
    print("="*70)
    
    try:
        # إنشاء جلسة قاعدة البيانات
        session = Session()
        
        # التحقق من وجود الجدول والعمود
        inspector = inspect(engine)
        
        # التحقق من وجود جدول المصروفات
        if 'expenses' not in inspector.get_table_names():
            print("❌ جدول المصروفات غير موجود!")
            return False
        
        # التحقق من وجود جدول المشاريع
        if 'projects' not in inspector.get_table_names():
            print("❌ جدول المشاريع غير موجود!")
            return False
        
        # الحصول على أعمدة جدول المصروفات
        columns = inspector.get_columns('expenses')
        column_names = [col['name'] for col in columns]
        
        # التحقق من وجود العمود project_id
        if 'project_id' in column_names:
            print("✅ العمود project_id موجود بالفعل في جدول المصروفات")
        else:
            print("📝 إضافة العمود project_id إلى جدول المصروفات...")
            
            # إضافة العمود project_id
            session.execute(text("""
                ALTER TABLE expenses 
                ADD COLUMN project_id INTEGER
            """))
            
            print("✅ تم إضافة العمود project_id بنجاح")
        
        # إنشاء المفتاح الخارجي إذا لم يكن موجوداً
        print("📝 إنشاء المفتاح الخارجي للمشروع...")
        
        try:
            # محاولة إضافة المفتاح الخارجي
            session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_expense_project_id 
                ON expenses(project_id)
            """))
            
            print("✅ تم إنشاء فهرس project_id بنجاح")
            
        except Exception as fk_error:
            print(f"⚠️ تحذير في إنشاء الفهرس: {fk_error}")
        
        # إنشاء الفهارس المركبة الجديدة
        print("📝 إنشاء الفهارس المركبة الجديدة...")
        
        indexes_to_create = [
            ("idx_expense_project_date", "project_id, date"),
            ("idx_expense_project_amount", "project_id, amount")
        ]
        
        for index_name, columns in indexes_to_create:
            try:
                session.execute(text(f"""
                    CREATE INDEX IF NOT EXISTS {index_name} 
                    ON expenses({columns})
                """))
                print(f"✅ تم إنشاء الفهرس {index_name}")
            except Exception as idx_error:
                print(f"⚠️ تحذير في إنشاء الفهرس {index_name}: {idx_error}")
        
        # حفظ التغييرات
        session.commit()
        print("💾 تم حفظ جميع التغييرات في قاعدة البيانات")
        
        # التحقق من نجاح التحديث
        print("\n" + "="*70)
        print("🔍 التحقق من نجاح التحديث...")
        
        # إعادة فحص الجدول
        updated_columns = inspector.get_columns('expenses')
        updated_column_names = [col['name'] for col in updated_columns]
        
        if 'project_id' in updated_column_names:
            print("✅ تأكيد: العمود project_id موجود في جدول المصروفات")
            
            # عرض معلومات العمود
            project_column = next((col for col in updated_columns if col['name'] == 'project_id'), None)
            if project_column:
                print(f"📊 نوع البيانات: {project_column['type']}")
                print(f"📊 يقبل NULL: {project_column['nullable']}")
        else:
            print("❌ خطأ: العمود project_id غير موجود!")
            return False
        
        # إحصائيات الجدول
        result = session.execute(text("SELECT COUNT(*) FROM expenses")).scalar()
        print(f"📊 إجمالي المصروفات في الجدول: {result}")
        
        result_with_project = session.execute(text("""
            SELECT COUNT(*) FROM expenses 
            WHERE project_id IS NOT NULL
        """)).scalar()
        print(f"📊 المصروفات المرتبطة بمشاريع: {result_with_project}")
        
        print("\n" + "="*70)
        print("🎉 تم إكمال تحديث قاعدة البيانات بنجاح!")
        print("✅ يمكن الآن ربط المصروفات بالمشاريع")
        print("="*70)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        session.rollback()
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        session.close()

def verify_migration():
    """التحقق من نجاح التحديث"""
    print("\n🔍 التحقق النهائي من التحديث...")
    
    try:
        session = Session()
        
        # اختبار الاستعلام الجديد
        result = session.execute(text("""
            SELECT e.id, e.title, e.amount, p.name as project_name
            FROM expenses e
            LEFT JOIN projects p ON e.project_id = p.id
            LIMIT 5
        """))
        
        print("📋 عينة من المصروفات مع المشاريع:")
        print("-" * 60)
        print(f"{'ID':<5} {'العنوان':<20} {'المبلغ':<15} {'المشروع':<15}")
        print("-" * 60)
        
        for row in result:
            expense_id = row[0]
            title = (row[1] or "غير محدد")[:18]
            amount = f"{row[2]:,.0f}" if row[2] else "0"
            project = (row[3] or "غير مرتبط")[:13]
            
            print(f"{expense_id:<5} {title:<20} {amount:<15} {project:<15}")
        
        print("✅ الاستعلام يعمل بشكل صحيح!")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return False

def main():
    """تشغيل التحديث"""
    print("🔧 تحديث قاعدة البيانات: ربط المصروفات بالمشاريع")
    print(f"📅 التاريخ: {os.path.basename(__file__)}")
    print("="*70)
    
    # تشغيل التحديث
    success = add_project_to_expenses()
    
    if success:
        # التحقق من النتيجة
        verify_migration()
        print("\n🎉 تم إكمال التحديث بنجاح!")
        print("💡 يمكنك الآن استخدام ربط المصروفات بالمشاريع في التطبيق")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")
        print("💡 يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة")

if __name__ == "__main__":
    main()

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView,
                            QDialog, QComboBox, QDateEdit, QDoubleSpinBox,
                            QFileDialog, QMenu, QAction, QSizePolicy, QFrame,
                            QTextBrowser, QGridLayout, QScrollArea, QGroupBox)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QPainter, QTextDocument, QPixmap, QBrush, QPen, QIcon, QLinearGradient
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Installment, InstallmentItem, Client
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency,
                    generate_installment_number, format_quantity)
import datetime
import re

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel)
# from ui.common_dialogs import WarningDialog  # لم تعد مستخدمة

# استيراد TitleBarStyler مع معالجة الأخطاء
try:
    from ui.title_bar_utils import TitleBarStyler
except ImportError:
    # إذا لم يكن المودول متاحاً، نستخدم دالة بديلة
    class TitleBarStyler:
        @staticmethod
        def apply_advanced_title_bar_styling(dialog):
            pass
from ui.title_bar_utils import TitleBarStyler
from ui.multi_selection_mixin import MultiSelectionMixin
from sqlalchemy import func


# ═══════════════════════════════════════════════════════════════════════════════════
# نوافذ التحذير المتطورة للأقساط - مطابقة لنافذة الحذف المرجعية
# ═══════════════════════════════════════════════════════════════════════════════════

class InstallmentWarningDialog(QDialog):
    """نافذة تحذير متطورة للأقساط مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="تحذير", message="", icon="⚠️"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.icon = icon
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon} {self.title} - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(251, 191, 36, 0.2),
                    stop:0.5 rgba(245, 158, 11, 0.3),
                    stop:1 rgba(217, 119, 6, 0.2));
                border: 2px solid rgba(251, 191, 36, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التحذير
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'info')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444',
                    'success': '#10B981',
                    'warning': '#F59E0B'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للأقساط"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(251, 191, 36))
            gradient.setColorAt(0.7, QColor(245, 158, 11))
            gradient.setColorAt(1, QColor(217, 119, 6))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InstallmentErrorDialog(QDialog):
    """نافذة خطأ متطورة للأقساط مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="خطأ", message="", icon="❌"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.icon = icon
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon} {self.title} - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة الخطأ
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("❌ إغلاق")
        self.style_advanced_button(ok_button, 'danger')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444',
                    'success': '#10B981',
                    'warning': '#F59E0B'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للأقساط"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InstallmentInfoDialog(QDialog):
    """نافذة معلومات متطورة للأقساط مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="معلومات", message="", icon="ℹ️"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.icon = icon
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon} {self.title} - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(37, 99, 235, 0.3),
                    stop:1 rgba(29, 78, 216, 0.2));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المعلومات
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("ℹ️ موافق")
        self.style_advanced_button(ok_button, 'info')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444',
                    'success': '#10B981',
                    'warning': '#F59E0B'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للأقساط"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(59, 130, 246))
            gradient.setColorAt(0.7, QColor(37, 99, 235))
            gradient.setColorAt(1, QColor(29, 78, 216))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InstallmentConfirmationDialog(QDialog):
    """نافذة تأكيد متطورة للأقساط مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="تأكيد", message="", icon="❓"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.icon = icon
        self.parent_widget = parent
        self.result = False
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon} {self.title} - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(139, 92, 246, 0.2),
                    stop:0.5 rgba(124, 58, 237, 0.3),
                    stop:1 rgba(109, 40, 217, 0.2));
                border: 2px solid rgba(139, 92, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التأكيد
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        no_button = QPushButton("❌ لا")
        self.style_advanced_button(no_button, 'danger')
        no_button.clicked.connect(self.reject_action)

        yes_button = QPushButton("✅ نعم")
        self.style_advanced_button(yes_button, 'success')
        yes_button.clicked.connect(self.accept_action)

        buttons_layout.addWidget(no_button)
        buttons_layout.addWidget(yes_button)
        layout.addLayout(buttons_layout)

    def accept_action(self):
        """قبول التأكيد"""
        self.result = True
        self.accept()

    def reject_action(self):
        """رفض التأكيد"""
        self.result = False
        self.reject()

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444',
                    'success': '#10B981',
                    'warning': '#F59E0B'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للأقساط"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(139, 92, 246))
            gradient.setColorAt(0.7, QColor(124, 58, 237))
            gradient.setColorAt(1, QColor(109, 40, 217))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InstallmentSuccessDialog(QDialog):
    """نافذة نجاح متطورة للأقساط مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="نجح", message="", icon="✅"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.icon = icon
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon} {self.title} - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(16, 185, 129, 0.2),
                    stop:0.5 rgba(5, 150, 105, 0.3),
                    stop:1 rgba(4, 120, 87, 0.2));
                border: 2px solid rgba(16, 185, 129, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة النجاح
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ ممتاز")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444',
                    'success': '#10B981',
                    'warning': '#F59E0B'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للأقساط"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(16, 185, 129))
            gradient.setColorAt(0.7, QColor(5, 150, 105))
            gradient.setColorAt(1, QColor(4, 120, 87))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InstallmentProgressDialog(QDialog):
    """نافذة تقدم العمليات المتطورة للأقساط مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="جاري العمل", message="", icon="⏳"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.icon = icon
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon} {self.title} - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint)  # بدون زر إغلاق
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 200)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(99, 102, 241, 0.2),
                    stop:0.5 rgba(79, 70, 229, 0.3),
                    stop:1 rgba(67, 56, 202, 0.2));
                border: 2px solid rgba(99, 102, 241, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التقدم
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للأقساط"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(99, 102, 241))
            gradient.setColorAt(0.7, QColor(79, 70, 229))
            gradient.setColorAt(1, QColor(67, 56, 202))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InstallmentMultiChoiceDialog(QDialog):
    """نافذة خيارات متعددة متطورة للأقساط مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="اختر", message="", choices=[], icon="🔀"):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.choices = choices or []
        self.icon = icon
        self.parent_widget = parent
        self.selected_choice = None
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon} {self.title} - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(450, 300)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon} {self.title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(168, 85, 247, 0.2),
                    stop:0.5 rgba(147, 51, 234, 0.3),
                    stop:1 rgba(126, 34, 206, 0.2));
                border: 2px solid rgba(168, 85, 247, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة الخيارات
        if self.message:
            message_label = QLabel(self.message)
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 10px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(message_label)

        # الأزرار للخيارات
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(5)

        for i, choice in enumerate(self.choices):
            button = QPushButton(f"{i+1}. {choice}")
            self.style_advanced_button(button, 'info')
            button.clicked.connect(lambda checked, c=choice: self.select_choice(c))
            buttons_layout.addWidget(button)

        layout.addLayout(buttons_layout)

        # زر الإلغاء
        cancel_layout = QHBoxLayout()
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)
        cancel_layout.addWidget(cancel_button)
        layout.addLayout(cancel_layout)

    def select_choice(self, choice):
        """اختيار خيار"""
        self.selected_choice = choice
        self.accept()

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444',
                    'success': '#10B981',
                    'warning': '#F59E0B'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                        margin: 2px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للأقساط"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(168, 85, 247))
            gradient.setColorAt(0.7, QColor(147, 51, 234))
            gradient.setColorAt(1, QColor(126, 34, 206))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


# ═══════════════════════════════════════════════════════════════════════════════════
# دوال مساعدة لعرض النوافذ المتطورة للأقساط
# ═══════════════════════════════════════════════════════════════════════════════════

def ensure_installment_dialog_stays_visible(dialog):
    """التأكد من أن النافذة تبقى مرئية"""
    if dialog:
        dialog.setModal(True)
        dialog.raise_()
        dialog.activateWindow()
        dialog.show()
        return dialog

def show_installment_advanced_warning(parent, title, message, icon="⚠️"):
    """عرض نافذة تحذير متطورة للأقساط"""
    dialog = InstallmentWarningDialog(parent, title, message, icon)
    return ensure_installment_dialog_stays_visible(dialog).exec_()

def show_installment_advanced_error(parent, title, message, icon="❌"):
    """عرض نافذة خطأ متطورة للأقساط"""
    dialog = InstallmentErrorDialog(parent, title, message, icon)
    return ensure_installment_dialog_stays_visible(dialog).exec_()

def show_installment_advanced_info(parent, title, message, icon="ℹ️"):
    """عرض نافذة معلومات متطورة للأقساط"""
    dialog = InstallmentInfoDialog(parent, title, message, icon)
    return ensure_installment_dialog_stays_visible(dialog).exec_()

def show_installment_advanced_confirmation(parent, title, message, icon="❓"):
    """عرض نافذة تأكيد متطورة للأقساط"""
    dialog = InstallmentConfirmationDialog(parent, title, message, icon)
    ensure_installment_dialog_stays_visible(dialog).exec_()
    return dialog.result

def show_installment_advanced_success(parent, title, message, icon="✅"):
    """عرض نافذة نجاح متطورة للأقساط"""
    dialog = InstallmentSuccessDialog(parent, title, message, icon)
    return ensure_installment_dialog_stays_visible(dialog).exec_()

def show_installment_advanced_progress(parent, title, message, icon="⏳"):
    """عرض نافذة تقدم العمليات متطورة للأقساط"""
    dialog = InstallmentProgressDialog(parent, title, message, icon)
    return ensure_installment_dialog_stays_visible(dialog)

def show_installment_advanced_multi_choice(parent, title, message, choices, icon="🔀"):
    """عرض نافذة خيارات متعددة متطورة للأقساط"""
    dialog = InstallmentMultiChoiceDialog(parent, title, message, choices, icon)
    ensure_installment_dialog_stays_visible(dialog).exec_()
    return dialog.selected_choice


class DeleteInstallmentDialog(QDialog):
    """نافذة حذف القسط مطابقة لنافذة حذف الإشعار"""

    def __init__(self, parent=None, installment=None):
        super().__init__(parent)
        self.installment = installment
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الإشعار"""
        self.setWindowTitle("💰 حذف - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("💰 حذف القسط")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات القسط مضغوطة
        if self.installment:
            # تحديد اسم العميل أو المشروع
            client_name = "غير محدد"
            if hasattr(self.installment, 'client') and self.installment.client:
                client_name = self.installment.client.name[:10] + ('...' if len(self.installment.client.name) > 10 else '')
            elif hasattr(self.installment, 'project') and self.installment.project:
                client_name = self.installment.project.name[:10] + ('...' if len(self.installment.project.name) > 10 else '')

            info_text = f"💰 {client_name}"

            # إضافة المبلغ
            amount = f"{self.installment.total_amount:.0f} جنيه"
            info_text += f" | {amount}"

            # إضافة حالة الدفع
            status_map = {
                'pending': '⏰',
                'paid': '✅',
                'overdue': '🔴',
                'cancelled': '❌'
            }
            status_icon = status_map.get(self.installment.status, '❓')
            info_text += f" | {status_icon}"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("💰 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للإشعارات"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "💰")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InstallmentsWidget(QWidget, MultiSelectionMixin):
    """واجهة إدارة الأقساط مع التحديد المتعدد - مطابقة للفواتير"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.selected_items = []
        self.init_ui()

    def safe_call_method(self, method_name):
        """استدعاء آمن للدوال مع فحص وجودها"""
        try:
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                if callable(method):
                    method()
                    return True

            # إذا لم توجد الدالة، عرض رسالة
            self.show_info_message(f"ميزة {method_name} قيد التطوير")
            return False

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تنفيذ العملية: {str(e)}")
            return False

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إنشاء العنوان الرئيسي مطابق للفواتير
        self.create_main_title(main_layout)

        # إنشاء شريط البحث مطابق للفواتير
        self.create_search_section(main_layout)

        # إنشاء الجدول مطابق للفواتير
        self.create_table_section(main_layout)

        # إنشاء شريط الأزرار مطابق للفواتير
        self.create_buttons_section(main_layout)

        # تعيين التخطيط للنافذة
        self.setLayout(main_layout)

        # ربط الأحداث وتهيئة حالة الأزرار
        self.connect_events()

        # تهيئة حالة الأزرار (جميع الأزرار معطلة حتى التحديد)
        QTimer.singleShot(100, self.initialize_button_states)

        # تحميل البيانات
        self.load_data()

    def create_main_title(self, layout):
        """إنشاء العنوان الرئيسي مطابق للفواتير"""
        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🏦 إدارة الأقساط المتطورة - نظام شامل ومتقدم لإدارة الأقساط مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        layout.addWidget(title_label)

    def create_search_section(self, layout):
        """إنشاء قسم البحث مطابق للفواتير"""
        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للفواتير
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث برقم القسط، العميل أو الملاحظات...")
        self.search_edit.textChanged.connect(self.filter_installments)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
        """)



        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        layout.addWidget(top_frame)

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة مطابقة تماماً للعملاء"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        # تخطيط أفقي للإطار
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(0)

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 25px;
                max-height: 25px;
                padding: 0px;
                margin: 0px;
                text-align: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 25px;
                max-height: 25px;
                padding: 0px;
                margin: 0px;
                text-align: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        self.status_filter_frame.setLayout(filter_layout)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة مطابقة تماماً للعملاء"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("جميع الحالات", None),
            ("👥 أقساط العملاء", "clients"),
            ("🏪 أقساط الموردين", "suppliers"),
            ("⏰ أقساط معلقة", "pending"),
            ("✅ أقساط مسددة", "paid"),
            ("🔴 أقساط متأخرة", "overdue"),
            ("❌ أقساط ملغية", "cancelled")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_installments()

    def create_table_section(self, layout):
        """إنشاء قسم الجدول مطابق للفواتير"""
        # إنشاء جدول الأقساط المتطور والمحسن
        self.create_advanced_installments_table()

        layout.addWidget(self.installments_table, 1)  # إعطاء الجدول أولوية في التمدد

    def _old_create_table_section_delete_me(self, layout):
        """إنشاء أزرار الأسهم بشكل آمن"""
        try:
            arrow_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 25px;
                    max-height: 25px;
                    padding: 0px;
                    margin: 0px;
                    text-align: center;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                    transition: all 0.2s ease;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(47, 109, 245, 0.9),
                        stop:0.2 rgba(69, 140, 255, 0.8),
                        stop:0.4 rgba(106, 175, 255, 0.7),
                        stop:0.6 rgba(149, 102, 255, 0.8),
                        stop:0.8 rgba(134, 68, 247, 0.9),
                        stop:1 rgba(119, 50, 227, 0.8));
                    border: 3px solid rgba(106, 175, 255, 0.8);
                    transform: translateY(-1px) scale(1.05);
                    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(27, 89, 225, 1.0),
                        stop:0.2 rgba(49, 120, 235, 0.9),
                        stop:0.4 rgba(86, 155, 240, 0.8),
                        stop:0.6 rgba(129, 82, 236, 0.9),
                        stop:0.8 rgba(114, 48, 227, 1.0),
                        stop:1 rgba(99, 30, 207, 0.9));
                    border: 2px solid rgba(86, 155, 240, 0.9);
                    transform: translateY(0px) scale(0.98);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
                }
            """

            # زر السهم الأيسر
            self.left_arrow = QPushButton("▼")
            self.left_arrow.setStyleSheet(arrow_style)

            # زر القائمة (سهم يمين)
            self.filter_menu_button = QPushButton("▼")
            self.filter_menu_button.setStyleSheet(arrow_style)

        except Exception as e:
            print(f"خطأ في إنشاء الأسهم: {e}")
            # إنشاء أزرار بسيطة كبديل
            self.left_arrow = QPushButton("▼")
            self.filter_menu_button = QPushButton("▼")

    def _create_filter_label(self):
        """إنشاء تسمية التصفية بشكل آمن"""
        try:
            label_style = """
                QLabel {
                    background: transparent;
                    border: none;
                    font-size: 16px;
                    font-weight: 700;
                    color: #1f2937;
                    text-align: center;
                    padding: 0px;
                    margin: 0px;
                    min-width: 400px;
                    max-width: 400px;
                }
                QLabel:hover {
                    color: #1e40af;
                    font-weight: 800;
                    text-shadow: 0 1px 2px rgba(30, 64, 175, 0.3);
                }
            """

            self.current_filter_label = QLabel("جميع الحالات")
            self.current_filter_label.setStyleSheet(label_style)
            self.current_filter_label.setAlignment(Qt.AlignCenter)

        except Exception as e:
            print(f"خطأ في إنشاء التسمية: {e}")
            # إنشاء تسمية بسيطة كبديل
            self.current_filter_label = QLabel("جميع الحالات")
            self.current_filter_label.setAlignment(Qt.AlignCenter)

    def _create_filter_menu_safe(self):
        """إنشاء القائمة المنسدلة بشكل آمن"""
        try:
            self.filter_menu = QMenu(self)

            # تطبيق التصميم
            menu_style = """
                QMenu {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 4px;
                    padding: 8px;
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 16px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    min-width: 520px;
                    max-width: 520px;
                }
                QAction {
                    background: transparent;
                    color: #1f2937;
                    font-size: 16px;
                    font-weight: 700;
                    padding: 12px 20px;
                    margin: 2px;
                    border-radius: 8px;
                    text-align: center;
                    min-height: 25px;
                    border: 2px solid transparent;
                    transition: all 0.3s ease;
                }
                QAction:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.15),
                        stop:0.2 rgba(96, 165, 250, 0.2),
                        stop:0.4 rgba(139, 92, 246, 0.15),
                        stop:0.6 rgba(124, 58, 237, 0.2),
                        stop:0.8 rgba(109, 40, 217, 0.15),
                        stop:1 rgba(96, 165, 250, 0.1));
                    color: #1e40af;
                    border: 2px solid rgba(96, 165, 250, 0.4);
                    font-weight: 800;
                    transform: scale(1.02);
                    box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                }
                QAction:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.2),
                        stop:0.2 rgba(59, 130, 246, 0.25),
                        stop:0.4 rgba(96, 165, 250, 0.2),
                        stop:0.6 rgba(139, 92, 246, 0.25),
                        stop:0.8 rgba(124, 58, 237, 0.2),
                        stop:1 rgba(109, 40, 217, 0.15));
                    color: #1e3a8a;
                    border: 2px solid rgba(59, 130, 246, 0.6);
                    font-weight: 900;
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
                }
            """

            self.filter_menu.setStyleSheet(menu_style)

            # إضافة العناصر
            self._add_filter_options()

        except Exception as e:
            print(f"خطأ في إنشاء القائمة: {e}")
            # إنشاء قائمة بسيطة كبديل
            self.filter_menu = QMenu(self)

    def _add_filter_options(self):
        """إضافة خيارات التصفية بشكل آمن"""
        try:
            filter_options = [
                ("جميع الحالات", None),
                ("👥 أقساط العملاء", "clients"),
                ("🏪 أقساط الموردين", "suppliers"),
                ("⏰ أقساط معلقة", "pending"),
                ("✅ أقساط مسددة", "paid"),
                ("🚨 أقساط متأخرة", "overdue"),
                ("🚫 أقساط ملغية", "cancelled")
            ]

            for text, value in filter_options:
                try:
                    # إنشاء عنصر مع توسيط النص المثالي
                    centered_text = f"{text:^40}"
                    action = QAction(centered_text, self)
                    action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
                    self.filter_menu.addAction(action)
                except Exception as item_error:
                    print(f"خطأ في إضافة عنصر القائمة {text}: {item_error}")
                    continue

        except Exception as e:
            print(f"خطأ في إضافة خيارات التصفية: {e}")

    def _connect_filter_events(self):
        """ربط أحداث التصفية بشكل آمن"""
        try:
            # ربط الأزرار بالقائمة
            if hasattr(self, 'filter_menu_button'):
                self.filter_menu_button.clicked.connect(self.show_filter_menu)
            if hasattr(self, 'left_arrow'):
                self.left_arrow.clicked.connect(self.show_filter_menu)

            # إضافة ميزة الضغط على أي مكان في الإطار
            if hasattr(self, 'status_filter_frame'):
                self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
            if hasattr(self, 'current_filter_label'):
                self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

            # جعل الإطار قابل للتركيز
            if hasattr(self, 'status_filter_frame'):
                self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        except Exception as e:
            print(f"خطأ في ربط الأحداث: {e}")

    def _create_fallback_filter(self):
        """إنشاء تصفية بديلة بسيطة في حالة الفشل"""
        try:
            self.status_filter_frame = QLabel("جميع الحالات")
            self.status_filter_frame.setStyleSheet("""
                QLabel {
                    background: #f8f9fa;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #495057;
                    min-width: 200px;
                    text-align: center;
                }
            """)
            self.status_filter_frame.setAlignment(Qt.AlignCenter)
            self.current_filter_value = None
            self.status_filter = self.status_filter_frame
            print("✅ تم إنشاء تصفية بديلة بسيطة")
        except Exception as e:
            print(f"❌ خطأ حرج في إنشاء التصفية البديلة: {e}")
            # في أسوأ الحالات، إنشاء عنصر فارغ
            self.status_filter_frame = QLabel("تصفية")
            self.current_filter_value = None
            self.status_filter = self.status_filter_frame



    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_installments()

    def create_table_section(self, layout):
        """إنشاء قسم الجدول مطابق للفواتير"""
        # إنشاء جدول الأقساط المتطور والمحسن
        self.create_advanced_installments_table()

        layout.addWidget(self.installments_table, 1)  # إعطاء الجدول أولوية في التمدد

    def create_advanced_installments_table(self):
        """إنشاء جدول الأقساط المتطور والنظيف مطابق للفواتير"""
        # إنشاء الجدول
        self.installments_table = QTableWidget()
        self.installments_table.setColumnCount(9)

        # عناوين الأعمدة مع الأيقونات مطابقة للعملاء (مع عمود ID)
        headers = [
            "🔢 ID",
            "🧑‍💼 العميل",
            "💰 إجمالي المبلغ",
            "✅ المبلغ المسدد",
            "⏳ المبلغ المتبقي",
            "📊 حالة الدفع",
            "📅 تاريخ الإنشاء",
            "⏰ موعد الاستحقاق",
            "📝 الملاحظات"
        ]
        self.installments_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مع التحديد المتعدد
        self.installments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.installments_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.installments_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.installments_table.setAlternatingRowColors(False)
        self.installments_table.setSortingEnabled(True)

        # إعداد التحديد المتعدد
        self.init_multi_selection(self.installments_table)

        # إعدادات الصفوف والأعمدة
        self.installments_table.verticalHeader().setDefaultSectionSize(50)
        self.installments_table.verticalHeader().setVisible(False)

        header = self.installments_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setSectionResizeMode(QHeaderView.Stretch)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.installments_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.installments_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق التصميم والتفاعل
        self.apply_table_style()
        self.add_watermark_to_installments_table()

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول مطابق للفواتير"""
        self.installments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def setup_table_interactions(self):
        """إعداد تفاعلات الجدول مطابق للفواتير"""
        try:
            # ربط إشارات الجدول
            self.installments_table.itemSelectionChanged.connect(self.on_selection_changed)
            self.installments_table.itemDoubleClicked.connect(self.view_installment)

            # إعداد قائمة النقر بالزر الأيمن
            self.installments_table.setContextMenuPolicy(Qt.CustomContextMenu)
            self.installments_table.customContextMenuRequested.connect(self.show_context_menu)

        except Exception as e:
            print(f"خطأ في إعداد تفاعلات الجدول: {e}")

    def show_context_menu(self, position):
        """عرض قائمة النقر بالزر الأيمن"""
        try:
            if self.installments_table.itemAt(position):
                menu = QMenu(self)

                view_action = QAction("👁️ عرض التفاصيل", self)
                view_action.triggered.connect(self.view_installment)
                menu.addAction(view_action)

                pay_action = QAction("💰 دفع قسط", self)
                pay_action.triggered.connect(self.pay_installment)
                menu.addAction(pay_action)

                edit_action = QAction("✏️ تعديل", self)
                edit_action.triggered.connect(self.edit_installment)
                menu.addAction(edit_action)

                delete_action = QAction("🗑️ حذف", self)
                delete_action.triggered.connect(self.delete_installment)
                menu.addAction(delete_action)

                menu.exec_(self.installments_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في عرض قائمة السياق: {e}")

    def add_watermark_to_installments_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.installments_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.installments_table.viewport())
                paint_watermark(painter, self.installments_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.installments_table.paintEvent = new_paint_event

    def create_buttons_section(self, layout):
        """إنشاء قسم الأزرار مطابق للفواتير"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للفواتير
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار مطابقة للفواتير

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة قسط")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_installment)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_installment)

        # زر دفع قسط
        self.pay_button = QPushButton("💰 دفع قسط")
        self.style_advanced_button(self.pay_button, 'warning')
        self.pay_button.clicked.connect(self.pay_installment)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_installment)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # لون مطابق للموردين والعمال
        self.refresh_button.clicked.connect(self.refresh_data)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')  # لون مطابق للعملاء والموردين والعمال
        self.view_button.clicked.connect(self.view_installment)

        # زر التصدير مع علامة القائمة المنسدلة
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'black', has_menu=True)  # لون مطابق للعملاء والموردين والعمال

        # إنشاء قائمة التصدير
        self.create_export_menu()

        # زر إدارة الوثائق
        self.documents_button = QPushButton("📁 إدارة الوثائق")
        self.style_advanced_button(self.documents_button, 'gray')  # لون مطابق للعملاء
        self.documents_button.clicked.connect(self.manage_documents)

        # زر الاتصالات (واتساب)
        self.whatsapp_button = QPushButton("📞 إتصال واتساب")
        self.style_advanced_button(self.whatsapp_button, 'warning')  # لون مطابق للعملاء
        self.whatsapp_button.clicked.connect(self.show_whatsapp_options)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 إحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # لون مطابق للموردين والعمال
        self.statistics_button.clicked.connect(self.show_statistics)

        # زر إظهار/إخفاء الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'indigo')  # لون مطابق للمصروفات والإيرادات والعملاء
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()



        # إضافة جميع الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.pay_button)
        actions_layout.addWidget(self.documents_button)
        actions_layout.addWidget(self.whatsapp_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.columns_visibility_button)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        layout.addWidget(bottom_frame)

    def create_export_menu(self):
        """إنشاء قائمة التصدير مطابقة تماماً للعملاء"""
        try:
            # إنشاء قائمة التصدير مع تحديد العرض والموضع
            export_menu = QMenu(self)

            # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
            export_menu.setStyleSheet("""
                QMenu {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 12px;
                    padding: 8px;
                    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                    font-weight: 700;
                    font-size: 13px;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                               0 5px 15px rgba(59, 130, 246, 0.2),
                               inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    min-width: 200px;
                }
                QMenu::item {
                    background: transparent;
                    padding: 10px 5px 10px 5px;
                    margin: 2px;
                    border: none;
                    border-radius: 8px;
                    color: #ffffff;
                    font-weight: 700;
                    font-size: 13px;
                    text-align: left;
                    min-height: 20px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    padding-left: 110px !important;
                }
                QMenu::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.6),
                        stop:1 rgba(124, 58, 237, 0.7));
                    border: 1px solid rgba(255, 255, 255, 0.4);
                    border-radius: 8px;
                    color: #ffffff;
                    font-weight: 800;
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                               0 0 15px rgba(96, 165, 250, 0.3),
                               inset 0 1px 0 rgba(255, 255, 255, 0.2);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    transform: scale(1.02);
                }
                QMenu::item:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(96, 165, 250, 0.3),
                        stop:0.5 rgba(139, 92, 246, 0.25),
                        stop:1 rgba(124, 58, 237, 0.3));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
                }
                QMenu::separator {
                    height: 2px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, 0.2),
                        stop:0.5 rgba(96, 165, 250, 0.4),
                        stop:1 rgba(255, 255, 255, 0.2));
                    margin: 3px 8px;
                    border: none;
                    border-radius: 1px;
                }
            """)

            # قسم التصدير الأساسي
            excel_action = QAction("📊 تصدير Excel متقدم", self)
            excel_action.triggered.connect(self.export_to_excel_advanced)
            export_menu.addAction(excel_action)

            csv_action = QAction("📄 تصدير CSV شامل", self)
            csv_action.triggered.connect(self.export_to_csv_advanced)
            export_menu.addAction(csv_action)

            # فاصل
            export_menu.addSeparator()

            # قسم التقارير المتقدمة
            detailed_action = QAction("📊 تقرير تفصيلي", self)
            detailed_action.triggered.connect(self.export_detailed_report)
            export_menu.addAction(detailed_action)

            balance_action = QAction("💰 تقرير الأرصدة", self)
            balance_action.triggered.connect(self.export_balance_report)
            export_menu.addAction(balance_action)

            # فاصل
            export_menu.addSeparator()

            # قسم التصدير المخصص
            custom_action = QAction("⚙️ تصدير مخصص", self)
            custom_action.triggered.connect(self.export_custom)
            export_menu.addAction(custom_action)

            backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
            backup_action.triggered.connect(self.export_backup)
            export_menu.addAction(backup_action)

            restore_action = QAction("📥 استعادة نسخة احتياطية", self)
            restore_action.triggered.connect(self.restore_backup)
            export_menu.addAction(restore_action)

            # تخصيص موضع وعرض القائمة
            def show_export_menu():
                """عرض قائمة التصدير فوق الزر مباشرة بنفس العرض"""
                # الحصول على موضع الزر (فوق الزر)
                button_pos = self.export_button.mapToGlobal(self.export_button.rect().topLeft())

                # تحديد عرض القائمة مع تكبير ربع درجة
                button_width = self.export_button.width()
                export_menu.setFixedWidth(max(button_width, 190))

                # ترحيل القائمة نصف درجة لليسار
                button_pos.setX(button_pos.x() - 10)

                # حساب ارتفاع القائمة لرفعها فوق الزر
                menu_height = export_menu.sizeHint().height()
                button_pos.setY(button_pos.y() - menu_height)

                # عرض القائمة في الموضع المحدد
                export_menu.exec_(button_pos)

            # ربط الزر بالدالة المخصصة بدلاً من setMenu
            self.export_button.clicked.connect(show_export_menu)

        except Exception as e:
            print(f"خطأ في إنشاء قائمة التصدير: {e}")

    def show_statistics(self):
        """عرض إحصائيات الأقساط مطابقة للعملاء"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QFont

            # إنشاء نافذة الإحصائيات مع ألوان الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات الأقساط")
            dialog.setModal(True)
            dialog.resize(500, 600)

            # إزالة علامة الاستفهام وتحسين شريط العنوان مطابق للبرنامج
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

            # تخصيص شريط العنوان مطابق للبرنامج
            try:
                from ui.title_bar_utils import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(dialog)
            except Exception as e:
                print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للنافذة الحوارية: {e}")

            # تطبيق نمط النافذة مطابق تماماً لنافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # التخطيط الرئيسي
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 15, 20, 15)
            layout.setSpacing(12)

            # حاوي العنوان مطابق لنافذة الإحصائيات
            title_container = QWidget()
            title_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 8px;
                }
            """)

            title_inner_layout = QVBoxLayout(title_container)
            title_inner_layout.setContentsMargins(0, 0, 0, 0)
            title_inner_layout.setSpacing(5)

            # الأيقونة والعنوان الرئيسي
            main_title = QLabel("📊 إحصائيات الأقساط")
            main_title.setAlignment(Qt.AlignCenter)
            main_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 30px;
                    font-weight: bold;
                    background: transparent;
                    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                    padding: 8px;
                }
            """)

            # العنوان الفرعي التوضيحي
            subtitle = QLabel("تحليل شامل لبيانات الأقساط والمدفوعات")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 14px;
                    font-weight: normal;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 4px;
                }
            """)

            title_inner_layout.addWidget(main_title)
            title_inner_layout.addWidget(subtitle)
            layout.addWidget(title_container)

            # حساب الإحصائيات
            installments = self.session.query(Installment).order_by(Installment.id.asc()).all()

            total_installments = len(installments)
            total_amount = sum(i.total_amount or 0 for i in installments)
            paid_amount = sum(i.paid_amount or 0 for i in installments)
            remaining_amount = total_amount - paid_amount

            # حساب الأقساط حسب الحالة
            pending_count = len([i for i in installments if i.status == 'pending'])
            paid_count = len([i for i in installments if i.status == 'paid'])
            overdue_count = len([i for i in installments if i.status == 'overdue'])

            # نسبة السداد
            payment_percentage = (paid_amount / total_amount * 100) if total_amount > 0 else 0

            # إنشاء عناصر الإحصائيات مطابقة للعملاء
            stats_data = [
                ("📊 إجمالي الأقساط", f"{total_installments}", "#3B82F6"),
                ("💰 إجمالي المبالغ", f"{total_amount:,.0f} جنيه", "#10B981"),
                ("✅ المبالغ المسددة", f"{paid_amount:,.0f} جنيه", "#059669"),
                ("⏳ المبالغ المتبقية", f"{remaining_amount:,.0f} جنيه", "#DC2626"),
                ("📈 نسبة السداد", f"{payment_percentage:.1f}%", "#8B5CF6"),
                ("⏰ أقساط معلقة", f"{pending_count}", "#F59E0B"),
                ("✅ أقساط مسددة", f"{paid_count}", "#10B981"),
                ("🔴 أقساط متأخرة", f"{overdue_count}", "#EF4444")
            ]

            # إنشاء عناصر الإحصائيات
            for title, value, color in stats_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 8px;
                        margin: 2px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 8px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(15)
                item_layout.setContentsMargins(15, 8, 15, 8)

                # العنوان
                title_label = QLabel(title)
                title_label.setStyleSheet(f"""
                    QLabel {{
                        color: #ffffff;
                        font-size: 16px;
                        font-weight: bold;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        background: transparent;
                        min-width: 200px;
                    }}
                """)

                # القيمة
                value_label = QLabel(value)
                value_label.setAlignment(Qt.AlignRight)
                value_label.setStyleSheet(f"""
                    QLabel {{
                        color: {color};
                        font-size: 18px;
                        font-weight: bold;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 rgba(255, 255, 255, 0.1),
                            stop:0.5 rgba(248, 250, 252, 0.08),
                            stop:1 rgba(226, 232, 240, 0.06));
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 8px;
                        padding: 8px 12px;
                        min-width: 120px;
                    }}
                """)

                item_layout.addWidget(title_label)
                item_layout.addWidget(value_label)
                layout.addWidget(item_widget)

            # زر الإغلاق مطابق للبرنامج الرئيسي
            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.accept)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(close_btn, 'danger')

            # توسيط الزر
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            button_layout.addStretch()
            layout.addLayout(button_layout)

            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة مطابقة تماماً للعملاء"""
        # إنشاء قائمة إدارة الأعمدة
        columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها مطابقة للأقساط (مع عمود ID)
        self.column_headers = [
            ("🔢 ID", 0),
            ("🧑‍💼 العميل", 1),
            ("💰 إجمالي المبلغ", 2),
            ("✅ المبلغ المسدد", 3),
            ("⏳ المبلغ المتبقي", 4),
            ("📊 حالة الدفع", 5),
            ("📅 تاريخ الإنشاء", 6),
            ("⏰ موعد الاستحقاق", 7),
            ("📝 الملاحظات", 8)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(not self.installments_table.isColumnHidden(column_index))  # حالة العمود الحالية
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            columns_menu.addAction(action)

        # إضافة فاصل
        columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        columns_menu.addAction(hide_all_action)

        # حفظ مرجع للقائمة
        self.columns_menu = columns_menu

        # تطبيق تصميم المحاذاة اليمنى للعناصر المحددة
        for action in columns_menu.actions():
            if action.property("align_right"):
                current_text = action.text()
                # إضافة مسافات لدفع النص لليمين نسبة بسيطة
                right_aligned_text = f"{current_text:>30}"
                action.setText(right_aligned_text)

        # تخصيص موضع وعرض القائمة مطابق تماماً للعملاء
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون بنفس عرض الزر مع حد أدنى
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل مطابق للعملاء
            columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_columns_visibility(self):
        """تبديل إظهار/إخفاء الأعمدة - استدعاء القائمة المنسدلة"""
        # هذه الدالة موجودة للتوافق مع الأزرار القديمة
        # القائمة المنسدلة يتم استدعاؤها مباشرة من الزر
        pass

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'installments_table') and self.installments_table:
                if visible:
                    self.installments_table.showColumn(column_index)
                else:
                    self.installments_table.hideColumn(column_index)
        except Exception as e:
            self.show_error_message(f"حدث خطأ في تبديل العمود: {str(e)}")

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            for _, column_index in self.column_headers:
                self.installments_table.showColumn(column_index)

            # تحديث حالة القائمة
            for action in self.columns_menu.actions():
                if action.isCheckable():
                    action.setChecked(True)

            self.show_success_message("تم إظهار جميع الأعمدة بنجاح!")
        except Exception as e:
            self.show_error_message(f"حدث خطأ في إظهار الأعمدة: {str(e)}")

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة (عدا العمود الأول)"""
        try:
            for _, column_index in self.column_headers:
                if column_index > 0:  # لا نخفي العمود الأول (رقم القسط)
                    self.installments_table.hideColumn(column_index)

            # تحديث حالة القائمة
            for action in self.columns_menu.actions():
                if action.isCheckable():
                    # إبقاء العمود الأول مرئياً
                    header_text = action.text()
                    if "رقم القسط" in header_text:
                        action.setChecked(True)
                    else:
                        action.setChecked(False)

            self.show_success_message("تم إخفاء جميع الأعمدة عدا رقم القسط!")
        except Exception as e:
            self.show_error_message(f"حدث خطأ في إخفاء الأعمدة: {str(e)}")

    def load_data(self):
        """تحميل بيانات الأقساط مطابق للفواتير"""
        try:
            # استعلام الأقساط من قاعدة البيانات
            installments = self.session.query(Installment).order_by(Installment.id.asc()).all()

            # تحديث الجدول
            self.installments_table.setRowCount(len(installments))

            for row, installment in enumerate(installments):
                # دالة مساعدة لإنشاء العناصر مع الأيقونات مطابقة للعملاء
                def create_item(icon, text, default="لا توجد بيانات"):
                    display_text = text if text and str(text).strip() else default
                    item = QTableWidgetItem(f"{icon} {display_text}")
                    item.setTextAlignment(Qt.AlignCenter)
                    if display_text == default:
                        item.setForeground(QColor("#ef4444"))  # أحمر للبيانات المفقودة
                    return item

                # حساب المبالغ
                total_amount = installment.total_amount or 0
                paid_amount = installment.paid_amount or 0
                remaining_amount = total_amount - paid_amount

                # رقم القسط مع أيقونة حسب حالة الدفع - لون أسود للأرقام
                if remaining_amount > 0:
                    if paid_amount > 0:
                        id_icon = "🟡"  # مدفوع جزئياً
                    else:
                        id_icon = "💰"  # غير مدفوع
                elif remaining_amount < 0:
                    id_icon = "🔴"  # مدفوع أكثر من المطلوب
                else:
                    id_icon = "✅"  # مسدد بالكامل

                # إنشاء عنصر ID مع رقم متسلسل بدلاً من ID الفعلي
                sequential_number = row + 1
                id_item = QTableWidgetItem(f"{id_icon} {sequential_number}")
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setForeground(QColor("#000000"))  # لون أسود للرقم
                # حفظ الـ ID الفعلي كبيانات مخفية للاستخدام في العمليات
                id_item.setData(Qt.UserRole, installment.id)
                self.installments_table.setItem(row, 0, id_item)

                # العميل مع أيقونة - عرض اسم الكيان الأصلي
                client_display_name = None

                # البحث في الملاحظات عن اسم الكيان الأصلي
                if installment.notes and 'قسط تلقائي' in installment.notes:
                    lines = installment.notes.split('\n')
                    for line in lines:
                        if line.startswith('الكيان:'):
                            client_display_name = line.replace('الكيان: ', '').strip()
                            break

                # إذا لم نجد في الملاحظات، نستخدم اسم العميل المرتبط
                if not client_display_name and installment.client:
                    if installment.client.name == "عميل افتراضي - الموردين":
                        client_display_name = "مورد غير محدد"
                    else:
                        client_display_name = installment.client.name

                # إذا لم نجد أي شيء
                if not client_display_name:
                    client_display_name = "غير محدد"

                # تحديد الأيقونة المناسبة
                if client_display_name.startswith('مورد:'):
                    icon = "🏪"
                else:
                    icon = "🧑‍💼"

                self.installments_table.setItem(row, 1, create_item(icon, client_display_name))

                # إجمالي المبلغ مع أيقونة وتنسيق
                if total_amount > 0:
                    total_text = f"{total_amount:,.0f} جنيه"
                    total_item = QTableWidgetItem(f"💰 {total_text}")
                    total_item.setForeground(QColor("#000000"))
                else:
                    total_item = create_item("💰", None)
                total_item.setTextAlignment(Qt.AlignCenter)
                self.installments_table.setItem(row, 2, total_item)

                # المبلغ المسدد مع أيقونة وألوان
                if paid_amount > 0:
                    paid_text = f"{paid_amount:,.0f} جنيه"
                    paid_item = QTableWidgetItem(f"✅ {paid_text}")
                    paid_item.setForeground(QColor("#059669"))  # أخضر للمبلغ المسدد
                else:
                    paid_item = create_item("✅", None)
                paid_item.setTextAlignment(Qt.AlignCenter)
                self.installments_table.setItem(row, 3, paid_item)

                # المبلغ المتبقي مع أيقونة وألوان
                if remaining_amount > 0:
                    remaining_text = f"{remaining_amount:,.0f} جنيه"
                    remaining_item = QTableWidgetItem(f"⏳ {remaining_text}")
                    remaining_item.setForeground(QColor("#dc2626"))  # أحمر للمبلغ المتبقي
                elif remaining_amount < 0:
                    remaining_text = f"{abs(remaining_amount):,.0f} جنيه زائد"
                    remaining_item = QTableWidgetItem(f"💸 {remaining_text}")
                    remaining_item.setForeground(QColor("#8b5cf6"))  # بنفسجي للمبلغ الزائد
                else:
                    remaining_item = QTableWidgetItem("⚫ 0 جنيه")
                    remaining_item.setForeground(QColor("#000000"))  # أسود للصفر
                remaining_item.setTextAlignment(Qt.AlignCenter)
                self.installments_table.setItem(row, 4, remaining_item)

                # حالة الدفع مع أيقونة وألوان
                status_text = self.get_status_text(installment.status)
                status_icons = {
                    "معلق": "⏰",
                    "مسدد": "✅",
                    "متأخر": "🔴",
                    "ملغي": "❌"
                }
                status_colors = {
                    "معلق": QColor("#f59e0b"),
                    "مسدد": QColor("#10b981"),
                    "متأخر": QColor("#ef4444"),
                    "ملغي": QColor("#6b7280")
                }

                status_icon = status_icons.get(status_text, "📊")
                status_item = QTableWidgetItem(f"{status_icon} {status_text}")
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setForeground(status_colors.get(status_text, QColor("#000000")))
                self.installments_table.setItem(row, 5, status_item)

                # تاريخ الإنشاء مع أيقونة
                date_str = installment.date.strftime('%Y-%m-%d') if installment.date else None
                self.installments_table.setItem(row, 6, create_item("📅", date_str))

                # موعد الاستحقاق مع أيقونة
                due_date_str = installment.due_date.strftime('%Y-%m-%d') if installment.due_date else None
                self.installments_table.setItem(row, 7, create_item("⏰", due_date_str))

                # الملاحظات مع أيقونة
                notes_text = installment.notes[:30] + "..." if installment.notes and len(installment.notes) > 30 else installment.notes
                self.installments_table.setItem(row, 8, create_item("📝", notes_text))



            # تحديث حالة الأزرار
            self.on_selection_changed()

            print(f"✅ تم تحميل {len(installments)} قسط بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الأقساط: {str(e)}")
            self.show_error_message(f"حدث خطأ في تحميل البيانات: {str(e)}")

    def get_status_text(self, status):
        """تحويل حالة القسط إلى نص عربي"""
        status_map = {
            'pending': 'معلق',
            'paid': 'مسدد',  # تغيير من "مدفوع" إلى "مسدد" للتوافق مع الأيقونات
            'overdue': 'متأخر',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, 'غير محدد')

    def filter_installments(self):
        """تصفية الأقساط مطابق للفواتير مع النظام المطور"""
        try:
            search_text = self.search_edit.text().lower().strip()

            # استخدام النظام الجديد للتصفية
            status_value = getattr(self, 'current_filter_value', None)

            for row in range(self.installments_table.rowCount()):
                show_row = True

                # تصفية النص
                if search_text:
                    row_text = ""
                    for col in range(self.installments_table.columnCount()):
                        item = self.installments_table.item(row, col)
                        if item:
                            row_text += item.text().lower() + " "

                    if search_text not in row_text:
                        show_row = False

                # تصفية حسب نوع الكيان (عملاء أو موردين)
                if show_row and status_value in ['clients', 'suppliers']:
                    client_item = self.installments_table.item(row, 1)  # عمود العميل
                    if client_item:
                        client_text = client_item.text()
                        if status_value == 'clients':
                            # إظهار العملاء فقط (ليس الموردين)
                            if client_text.startswith('مورد:') or '🏪' in client_text:
                                show_row = False
                        elif status_value == 'suppliers':
                            # إظهار الموردين فقط
                            if not (client_text.startswith('مورد:') or '🏪' in client_text):
                                show_row = False

                # تصفية الحالة باستخدام النظام الجديد
                elif show_row and status_value is not None and status_value not in ['clients', 'suppliers']:
                    status_item = self.installments_table.item(row, 5)  # عمود حالة الدفع هو الفهرس 5
                    if status_item:
                        status_text = status_item.text()
                        # تحويل النص العربي إلى القيمة الإنجليزية للمقارنة
                        status_map = {
                            'معلق': 'pending',
                            'مسدد': 'paid',  # النص الصحيح المستخدم في الجدول
                            'متأخر': 'overdue',
                            'ملغي': 'cancelled'
                        }

                        # البحث عن القيمة المقابلة للنص العربي
                        current_status = None
                        for arabic_text, english_value in status_map.items():
                            if arabic_text in status_text:
                                current_status = english_value
                                break

                        # إخفاء الصف إذا لم يطابق الفلتر
                        if current_status != status_value:
                            show_row = False

                self.installments_table.setRowHidden(row, not show_row)

        except Exception as e:
            print(f"❌ خطأ في تصفية الأقساط: {str(e)}")

    def on_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير التحديد"""
        try:
            print("🚨 تم استدعاء معالج تحديد الأقساط!")

            # تسجيل أن المستخدم تفاعل مع الجدول
            self.user_interacted_with_table = True
            print("👆 المستخدم تفاعل مع الجدول - سيتم تطبيق خاصية الإغلاق")

            if not self.installments_table:
                return

            self.update_button_states()

        except Exception as e:
            print(f"❌ خطأ في معالجة تحديد الأقساط: {e}")
            import traceback
            print(traceback.format_exc())

    def update_button_states(self):
        """تحديث حالة الأزرار حسب التحديد مطابق للمشاريع"""
        try:
            # إذا لم يتفاعل المستخدم مع الجدول بعد، لا نغير حالة الأزرار
            if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                print("🔥 المستخدم لم يتفاعل مع الجدول بعد - الأزرار تبقى مفعلة")
                return

            has_selection = len(self.installments_table.selectedItems()) > 0

            print(f"🔧 تحديث حالة الأزرار: {'يوجد تحديد' if has_selection else 'لا يوجد تحديد'}")

            # الأزرار التي تحتاج تحديد عنصر
            self.set_button_visibility(self.edit_button, has_selection)
            self.set_button_visibility(self.delete_button, has_selection)
            self.set_button_visibility(self.view_button, has_selection)
            self.set_button_visibility(self.documents_button, has_selection)
            self.set_button_visibility(self.whatsapp_button, has_selection)

            # الأزرار المتاحة دائماً
            self.set_button_visibility(self.add_button, True)  # زر الإضافة متاح دائماً

        except Exception as e:
            print(f"❌ خطأ في تحديث حالة الأزرار: {e}")

    def set_button_visibility(self, button, visible):
        """تعيين رؤية الزر مع الحفاظ على الألوان الأصلية - مطابق للمشاريع"""
        try:
            if button:
                # التحقق من أن المستخدم تفاعل مع الجدول قبل تطبيق التأثيرات
                if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                    # إذا لم يتفاعل المستخدم بعد، نبقي الزر مفعلاً ومنيراً
                    button.setEnabled(True)
                    button.setGraphicsEffect(None)
                    print(f"🔥 زر {button.text()}: مفعل (لم يتفاعل المستخدم مع الجدول بعد)")
                    return

                button.setEnabled(visible)

                # تطبيق تأثير الشفافية مع الحفاظ على الألوان - طريقة محسنة
                current_style = button.styleSheet()
                import re
                # إزالة أي opacity موجودة سابقاً
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)

                if visible:
                    # إظهار الزر بشفافية كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                else:
                    # تقليل شفافية الزر (لا نخفيه تماماً)
                    new_style = clean_style + "\nQPushButton { opacity: 0.3; }"

                button.setStyleSheet(new_style)

                print(f"🔧 زر {button.text()}: {'مفعل' if visible else 'معطل'}")
        except Exception as e:
            print(f"❌ خطأ في تعيين رؤية الزر: {e}")
            # في حالة فشل التأثير، استخدم الطريقة البسيطة
            if button:
                button.setEnabled(True)  # نبقيه مفعلاً في حالة الخطأ

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث تغيير التحديد لتفعيل/تعطيل الأزرار
            if hasattr(self, 'installments_table'):
                self.installments_table.itemSelectionChanged.connect(self.on_selection_changed)
                self.installments_table.itemDoubleClicked.connect(self.view_installment)

            print("✅ تم ربط أحداث الأقساط بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث الأقساط: {str(e)}")

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة أزرار الأقساط...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة قسط"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.documents_button, "📁 إدارة الوثائق"),
                (self.whatsapp_button, "📞 واتساب"),
                (self.export_button, "📤 تصدير"),
                (self.statistics_button, "📊 الإحصائيات"),
                (self.columns_visibility_button, "👁️ إدارة الأعمدة")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    import re
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
                    print(f"🟢 تم تفعيل الزر: {name}")

            print("✅ تم تهيئة حالة أزرار الأقساط بنجاح")

            # لا نعطل الأزرار تلقائياً - تبقى مفعلة حتى يتفاعل المستخدم مع الجدول
            print("🔥 الأزرار ستبقى مفعلة حتى التفاعل مع الجدول")

        except Exception as e:
            print(f"❌ خطأ في تهيئة حالة أزرار الأقساط: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات مطابق للعملاء"""
        try:
            self.load_data()
            self.show_success_message("تم تحديث بيانات الأقساط بنجاح")
        except Exception as e:
            self.show_error_message(f"حدث خطأ في تحديث البيانات: {str(e)}")

    def add_installment(self):
        """إضافة قسط جديد"""
        try:
            dialog = AddInstallmentDialog(self.session, self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()  # تحديث الجدول بعد الإضافة
                self.show_success_message("تم إضافة القسط بنجاح")
        except Exception as e:
            self.show_error_message(f"حدث خطأ في إضافة القسط: {str(e)}")

    def edit_installment(self):
        """تعديل قسط"""
        try:
            current_row = self.installments_table.currentRow()
            if current_row >= 0:
                # الحصول على ID القسط من العمود الأول مع إزالة الأيقونات
                id_item = self.installments_table.item(current_row, 0)
                if id_item:
                    # استخراج الأرقام فقط من النص (إزالة الأيقونات والرموز)
                    id_text = id_item.text()
                    import re
                    numbers = re.findall(r'\d+', id_text)
                    if numbers:
                        installment_id = int(numbers[0])
                    else:
                        self.show_error_message("لا يمكن استخراج معرف القسط")
                        return
                    # الحصول على بيانات القسط من قاعدة البيانات
                    installment = self.session.query(Installment).get(installment_id)
                    if installment:
                        # فتح نافذة التعديل
                        dialog = AddInstallmentDialog(self.session, self, installment)
                        if dialog.exec_() == QDialog.Accepted:
                            self.load_data()  # تحديث الجدول بعد التعديل
                        self.show_success_message("تم تعديل القسط بنجاح")
                    else:
                        self.show_error_message("لم يتم العثور على القسط المحدد")
                else:
                    self.show_error_message("لم يتم تحديد قسط صحيح")
            else:
                self.show_warning_message("يرجى اختيار قسط للتعديل")
        except Exception as e:
            self.show_error_message(f"حدث خطأ في تعديل القسط: {str(e)}")

    def pay_installment(self):
        """دفع قسط إضافي - تحديث المبلغ المدفوع فقط"""
        try:
            selected_row = self.installments_table.currentRow()
            if selected_row < 0:
                self.show_warning_message("الرجاء اختيار قسط من القائمة")
                return

            # استخراج الـ ID الفعلي من البيانات المخفية
            id_item = self.installments_table.item(selected_row, 0)
            installment_id = id_item.data(Qt.UserRole)

            installment = self.session.query(Installment).get(installment_id)
            if not installment:
                self.show_error_message("لم يتم العثور على القسط المحدد")
                return

            # التحقق من حالة القسط
            total_amount = installment.total_amount or 0
            paid_amount = installment.paid_amount or 0
            remaining_amount = total_amount - paid_amount

            if remaining_amount <= 0:
                self.show_info_message("هذا القسط مسدد بالكامل بالفعل")
                return

            # فتح نافذة دفع القسط
            dialog = PayInstallmentDialog(self.session, self, installment)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()  # تحديث الجدول بعد الدفع
                self.show_success_message("تم تحديث دفع القسط بنجاح")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في دفع القسط: {str(e)}")

    def delete_installment(self):
        """حذف قسط مع نافذة تأكيد متطورة مطابقة للإشعارات"""
        try:
            selected_row = self.installments_table.currentRow()
            if selected_row < 0:
                self.show_warning_message("الرجاء اختيار قسط من القائمة")
                return

            # استخراج الـ ID الفعلي من البيانات المخفية
            id_item = self.installments_table.item(selected_row, 0)
            installment_id = id_item.data(Qt.UserRole)
            installment = self.session.query(Installment).get(installment_id)

            if not installment:
                self.show_error_message("لم يتم العثور على القسط")
                return

            # إنشاء نافذة حذف متطورة مطابقة للإشعارات
            dialog = DeleteInstallmentDialog(self, installment)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # إعادة الرصيد إلى العميل قبل الحذف
                    balance_message = ""
                    if installment.client_id:
                        client = self.session.query(Client).get(installment.client_id)
                        if client:
                            old_balance = client.balance or 0
                            total_amount = installment.total_amount or 0
                            paid_amount = installment.paid_amount or 0

                            # إعادة المبلغ الإجمالي وخصم المبلغ المدفوع
                            client.balance = old_balance + total_amount - paid_amount

                            balance_message = (
                                f"\n\nتحديث رصيد العميل:\n"
                                f"الرصيد السابق: {old_balance:,.2f} ج.م\n"
                                f"إعادة المبلغ الإجمالي: +{total_amount:,.2f} ج.م\n"
                                f"خصم المبلغ المدفوع: -{paid_amount:,.2f} ج.م\n"
                                f"الرصيد الجديد: {client.balance:,.2f} ج.م"
                            )

                    # حذف القسط من قاعدة البيانات
                    self.session.delete(installment)
                    self.session.commit()

                    # إظهار رسالة نجاح متطورة مع تفاصيل الرصيد
                    client_name = "غير محدد"
                    if hasattr(installment, 'client') and installment.client:
                        client_name = installment.client.name
                    elif hasattr(installment, 'project') and installment.project:
                        client_name = installment.project.name

                    self.show_success_message(f"تم حذف القسط للعميل '{client_name}' بنجاح{balance_message}")

                    # تحديث الجدول
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    self.show_error_message(f"فشل في حذف القسط: {str(e)}")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف القسط: {str(e)}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مطابقة لنافذة الحذف"""
        show_installment_advanced_warning(self, "تحذير", message)

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة مطابقة لنافذة الحذف"""
        show_installment_advanced_error(self, "خطأ", message)

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة مطابقة لنافذة الحذف"""
        show_installment_advanced_success(self, "تم", message)

    def show_info_message(self, message):
        """إظهار رسالة معلومات متطورة مطابقة لنافذة الحذف"""
        show_installment_advanced_info(self, "معلومات", message)

    def show_confirmation_message(self, title, message):
        """إظهار رسالة تأكيد متطورة مطابقة لنافذة الحذف"""
        return show_installment_advanced_confirmation(self, title, message)

    def view_installment(self):
        """عرض تفاصيل القسط مطابق للعملاء"""
        try:
            current_row = self.installments_table.currentRow()
            if current_row >= 0:
                # الحصول على ID القسط من العمود الأول مع إزالة الأيقونات
                id_item = self.installments_table.item(current_row, 0)
                if id_item:
                    # استخراج الأرقام فقط من النص (إزالة الأيقونات والرموز)
                    id_text = id_item.text()
                    import re
                    numbers = re.findall(r'\d+', id_text)
                    if numbers:
                        installment_id = int(numbers[0])
                        # الحصول على بيانات القسط من قاعدة البيانات
                        installment = self.session.query(Installment).get(installment_id)
                        if installment:
                            # فتح نافذة عرض التفاصيل
                            dialog = InstallmentInfoDialog(self, installment)
                            dialog.exec_()
                        else:
                            self.show_error_message("لم يتم العثور على القسط المحدد")
                    else:
                        self.show_error_message("لا يمكن استخراج معرف القسط")
                else:
                    self.show_error_message("لم يتم تحديد قسط صحيح")
            else:
                self.show_warning_message("يرجى اختيار قسط لعرض التفاصيل")
        except Exception as e:
            self.show_error_message(f"حدث خطأ في عرض تفاصيل القسط: {str(e)}")

    def manage_documents(self):
        """إدارة وثائق العميل المرتبط بالقسط"""
        try:
            # التحقق من وجود تحديد
            selected_items = self.installments_table.selectedItems()
            if not selected_items:
                self.show_info_message("الرجاء اختيار قسط من الجدول أولاً")
                return

            # الحصول على بيانات القسط المحدد
            row = selected_items[0].row()
            id_item = self.installments_table.item(row, 0)
            installment_id = id_item.data(Qt.UserRole)

            if not installment_id:
                self.show_error_message("لا يمكن الحصول على معرف القسط")
                return

            installment = self.session.query(Installment).get(installment_id)

            if not installment or not installment.client:
                self.show_error_message("لم يتم العثور على العميل المرتبط بهذا القسط")
                return

            print(f"✅ تم العثور على العميل: {installment.client.name}")

            # فتح نافذة إدارة الوثائق (سيتم استيرادها من قسم العملاء)
            from ui.clients import ClientDocumentsDialog
            dialog = ClientDocumentsDialog(self.session, self, installment.client)
            dialog.exec_()

        except Exception as e:
            print(f"❌ خطأ في إدارة وثائق العميل: {str(e)}")
            self.show_error_message(f"حدث خطأ في إدارة وثائق العميل: {str(e)}")

    def show_whatsapp_options(self):
        """عرض خيارات الواتساب للعميل المرتبط بالقسط"""
        try:
            # التحقق من وجود تحديد
            selected_items = self.installments_table.selectedItems()
            if not selected_items:
                self.show_info_message("الرجاء اختيار قسط من الجدول أولاً")
                return

            # الحصول على بيانات القسط المحدد
            row = selected_items[0].row()
            id_item = self.installments_table.item(row, 0)
            installment_id = id_item.data(Qt.UserRole)

            if not installment_id:
                self.show_error_message("لا يمكن الحصول على معرف القسط")
                return

            installment = self.session.query(Installment).get(installment_id)

            if not installment or not installment.client:
                self.show_error_message("لم يتم العثور على العميل المرتبط بهذا القسط")
                return

            # عرض نافذة خيارات الواتساب (سيتم استيرادها من قسم العملاء)
            from ui.clients import WhatsAppDialog
            dialog = WhatsAppDialog(installment.client, self)
            dialog.exec_()

        except Exception as e:
            print(f"❌ خطأ في عرض خيارات الواتساب: {str(e)}")
            self.show_error_message(f"حدث خطأ في عرض خيارات الواتساب: {str(e)}")

    def export_pdf(self):
        """تصدير إلى PDF"""
        self.show_info_message("ميزة التصدير إلى PDF ستكون متاحة قريباً")

    def export_excel(self):
        """تصدير إلى Excel"""
        self.show_info_message("ميزة التصدير إلى Excel ستكون متاحة قريباً")

    def export_csv(self):
        """تصدير إلى CSV"""
        self.show_info_message("ميزة التصدير إلى CSV ستكون متاحة قريباً")

    def print_installments(self):
        """طباعة الأقساط"""
        self.show_info_message("ميزة الطباعة ستكون متاحة قريباً")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة - مطابق تماماً للعملاء"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق تماماً للعملاء
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                },
                'purple': {
                    'bg_start': '#581c87', 'bg_mid': '#7c3aed', 'bg_end': '#8b5cf6', 'bg_bottom': '#a855f7',
                    'hover_start': '#a855f7', 'hover_mid': '#c084fc', 'hover_end': '#d8b4fe', 'hover_bottom': '#e9d5ff',
                    'hover_border': '#c084fc', 'pressed_start': '#3b0764', 'pressed_mid': '#581c87',
                    'pressed_end': '#6b21a8', 'pressed_bottom': '#7c3aed', 'pressed_border': '#6b21a8',
                    'border': '#a855f7', 'text': '#ffffff', 'shadow': 'rgba(168, 85, 247, 0.6)'
                },
                'gray': {
                    'bg_start': '#2d3748', 'bg_mid': '#4a5568', 'bg_end': '#718096', 'bg_bottom': '#a0aec0',
                    'hover_start': '#a0aec0', 'hover_mid': '#cbd5e0', 'hover_end': '#e2e8f0', 'hover_bottom': '#f7fafc',
                    'hover_border': '#e2e8f0', 'pressed_start': '#1a202c', 'pressed_mid': '#2d3748',
                    'pressed_end': '#4a5568', 'pressed_bottom': '#718096', 'pressed_border': '#4a5568',
                    'border': '#a0aec0', 'text': '#ffffff', 'shadow': 'rgba(160, 174, 192, 0.6)'
                },
                'gold': {
                    'bg_start': '#b7791f', 'bg_mid': '#d69e2e', 'bg_end': '#ecc94b', 'bg_bottom': '#ffd700',
                    'hover_start': '#ecc94b', 'hover_mid': '#f6e05e', 'hover_end': '#faf089', 'hover_bottom': '#fefcbf',
                    'hover_border': '#ffd700', 'pressed_start': '#744210', 'pressed_mid': '#b7791f',
                    'pressed_end': '#d69e2e', 'pressed_bottom': '#ecc94b', 'pressed_border': '#d69e2e',
                    'border': '#ffd700', 'text': '#ffffff', 'shadow': 'rgba(255, 215, 0, 0.9)'
                },
                'black': {
                    'bg_start': '#000000', 'bg_mid': '#1a1a1a', 'bg_end': '#2d2d2d', 'bg_bottom': '#404040',
                    'hover_start': '#2d2d2d', 'hover_mid': '#404040', 'hover_end': '#525252', 'hover_bottom': '#666666',
                    'hover_border': '#808080', 'pressed_start': '#000000', 'pressed_mid': '#000000',
                    'pressed_end': '#1a1a1a', 'pressed_bottom': '#2d2d2d', 'pressed_border': '#1a1a1a',
                    'border': '#404040', 'text': '#ffffff', 'shadow': 'rgba(102, 102, 102, 0.8)'
                },
                'warning': {
                    'bg_start': '#451a03', 'bg_mid': '#78350f', 'bg_end': '#a16207', 'bg_bottom': '#eab308',
                    'hover_start': '#78350f', 'hover_mid': '#ca8a04', 'hover_end': '#eab308', 'hover_bottom': '#facc15',
                    'hover_border': '#eab308', 'pressed_start': '#451a03', 'pressed_mid': '#78350f',
                    'pressed_end': '#a16207', 'pressed_bottom': '#ca8a04', 'pressed_border': '#a16207',
                    'border': '#eab308', 'text': '#ffffff', 'shadow': 'rgba(234, 179, 8, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق تماماً للعملاء
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def export_to_excel_advanced(self):
        """تصدير بيانات الأقساط إلى Excel متقدم مع تنسيق وإحصائيات"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel متقدم", f"الأقساط_متقدم_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                installments = self.session.query(Installment).order_by(Installment.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير الأقساط المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي عدد الأقساط: {len(installments)}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    headers = [
                        'رقم القسط', 'العميل', 'إجمالي المبلغ', 'المبلغ المسدد',
                        'المبلغ المتبقي', 'حالة الدفع', 'تاريخ الإنشاء', 'موعد الاستحقاق',
                        'الملاحظات'
                    ]
                    writer.writerow(headers)

                    # كتابة بيانات الأقساط
                    for installment in installments:
                        remaining_amount = (installment.total_amount or 0) - (installment.paid_amount or 0)
                        status_map = {
                            'pending': 'معلق',
                            'paid': 'مسدد',
                            'overdue': 'متأخر',
                            'cancelled': 'ملغي'
                        }
                        status_text = status_map.get(installment.status, 'غير محدد')

                        row = [
                            installment.installment_number or '',
                            installment.client.name if installment.client else 'غير محدد',
                            f'{installment.total_amount:.2f}' if installment.total_amount else '0.00',
                            f'{installment.paid_amount:.2f}' if installment.paid_amount else '0.00',
                            f'{remaining_amount:.2f}',
                            status_text,
                            installment.date.strftime('%Y-%m-%d') if installment.date else 'غير محدد',
                            installment.due_date.strftime('%Y-%m-%d') if installment.due_date else 'غير محدد',
                            installment.notes or ''
                        ]
                        writer.writerow(row)

                self.show_success_message(f"تم تصدير الأقساط بتنسيق متقدم بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المتقدم: {str(e)}")

    def export_to_csv_advanced(self):
        """تصدير بيانات الأقساط إلى CSV شامل"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV شامل", f"الأقساط_شامل_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                installments = self.session.query(Installment).order_by(Installment.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير الأقساط الشامل'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    headers = [
                        'رقم القسط', 'العميل', 'هاتف العميل', 'عنوان العميل',
                        'إجمالي المبلغ', 'المبلغ المسدد', 'المبلغ المتبقي', 'نسبة السداد',
                        'حالة الدفع', 'نوع القسط', 'الملاحظات', 'تاريخ الإنشاء', 'موعد الاستحقاق'
                    ]
                    writer.writerow(headers)

                    for installment in installments:
                        remaining_amount = (installment.total_amount or 0) - (installment.paid_amount or 0)
                        payment_percentage = ((installment.paid_amount or 0) / (installment.total_amount or 1) * 100) if installment.total_amount else 0
                        installment_type = 'VIP' if (installment.total_amount or 0) > 10000 else 'عادي'

                        row = [
                            installment.installment_number or 'غير محدد',
                            installment.client.name if installment.client else 'غير محدد',
                            installment.client.phone if installment.client and installment.client.phone else 'غير محدد',
                            installment.client.address if installment.client and installment.client.address else 'غير محدد',
                            f'{installment.total_amount:,.2f} جنيه' if installment.total_amount else '0.00 جنيه',
                            f'{installment.paid_amount:,.2f} جنيه' if installment.paid_amount else '0.00 جنيه',
                            f'{remaining_amount:,.2f} جنيه',
                            f'{payment_percentage:.1f}%',
                            installment.status or 'غير محدد',
                            installment_type,
                            installment.notes or 'لا توجد ملاحظات',
                            installment.date.strftime('%Y-%m-%d') if installment.date else 'غير محدد',
                            installment.due_date.strftime('%Y-%m-%d') if installment.due_date else 'غير محدد'
                        ]
                        writer.writerow(row)

                self.show_success_message(f"تم تصدير التقرير الشامل بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير الشامل: {str(e)}")

    def export_detailed_report(self):
        """تصدير تقرير تفصيلي للأقساط"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير التفصيلي", f"تقرير_أقساط_تفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                installments = self.session.query(Installment).order_by(Installment.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['التقرير التفصيلي للأقساط'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي عدد الأقساط: {len(installments)}'])
                    writer.writerow([])

                    # إحصائيات عامة
                    total_amount = sum(i.total_amount or 0 for i in installments)
                    paid_amount = sum(i.paid_amount or 0 for i in installments)
                    remaining_amount = total_amount - paid_amount

                    writer.writerow(['الإحصائيات العامة'])
                    writer.writerow(['إجمالي المبالغ', f'{total_amount:,.2f} جنيه'])
                    writer.writerow(['المبالغ المسددة', f'{paid_amount:,.2f} جنيه'])
                    writer.writerow(['المبالغ المتبقية', f'{remaining_amount:,.2f} جنيه'])
                    writer.writerow([])

                    # تفاصيل الأقساط
                    writer.writerow(['تفاصيل الأقساط'])
                    headers = ['رقم القسط', 'العميل', 'هاتف العميل', 'إجمالي المبلغ', 'المسدد', 'المتبقي', 'النسبة', 'الحالة', 'تاريخ الإنشاء', 'تاريخ الاستحقاق', 'الملاحظات']
                    writer.writerow(headers)

                    for installment in installments:
                        remaining = (installment.total_amount or 0) - (installment.paid_amount or 0)
                        percentage = ((installment.paid_amount or 0) / (installment.total_amount or 1) * 100) if installment.total_amount else 0

                        row = [
                            installment.installment_number or 'غير محدد',
                            installment.client.name if installment.client else 'غير محدد',
                            installment.client.phone if installment.client and installment.client.phone else 'غير محدد',
                            f'{installment.total_amount:,.2f}' if installment.total_amount else '0.00',
                            f'{installment.paid_amount:,.2f}' if installment.paid_amount else '0.00',
                            f'{remaining:,.2f}',
                            f'{percentage:.1f}%',
                            installment.status or 'غير محدد',
                            installment.date.strftime('%Y-%m-%d') if installment.date else 'غير محدد',
                            installment.due_date.strftime('%Y-%m-%d') if installment.due_date else 'غير محدد',
                            installment.notes or 'لا توجد ملاحظات'
                        ]
                        writer.writerow(row)

                self.show_success_message(f"تم تصدير التقرير التفصيلي بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التقرير التفصيلي: {str(e)}")

    def export_balance_report(self):
        """تصدير تقرير الأرصدة"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الأرصدة", f"تقرير_أرصدة_أقساط_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                installments = self.session.query(Installment).order_by(Installment.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير أرصدة الأقساط'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    headers = ['العميل', 'هاتف العميل', 'عدد الأقساط', 'إجمالي المبالغ', 'المسدد', 'المتبقي', 'نسبة السداد', 'حالة الرصيد']
                    writer.writerow(headers)

                    # تجميع البيانات حسب العميل
                    client_data = {}
                    for installment in installments:
                        client_name = installment.client.name if installment.client else 'غير محدد'
                        client_phone = installment.client.phone if installment.client and installment.client.phone else 'غير محدد'

                        if client_name not in client_data:
                            client_data[client_name] = {
                                'phone': client_phone,
                                'count': 0,
                                'total': 0,
                                'paid': 0
                            }

                        client_data[client_name]['count'] += 1
                        client_data[client_name]['total'] += installment.total_amount or 0
                        client_data[client_name]['paid'] += installment.paid_amount or 0

                    # كتابة البيانات
                    for client_name, data in client_data.items():
                        remaining = data['total'] - data['paid']
                        percentage = (data['paid'] / data['total'] * 100) if data['total'] else 0
                        status = 'مسدد' if remaining <= 0 else 'جزئي' if data['paid'] > 0 else 'غير مسدد'

                        row = [
                            client_name,
                            data['phone'],
                            data['count'],
                            f'{data["total"]:,.2f} جنيه',
                            f'{data["paid"]:,.2f} جنيه',
                            f'{remaining:,.2f} جنيه',
                            f'{percentage:.1f}%',
                            status
                        ]
                        writer.writerow(row)

                self.show_success_message(f"تم تصدير تقرير الأرصدة بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تقرير الأرصدة: {str(e)}")

    def export_custom(self):
        """تصدير مخصص مع خيارات متقدمة مطابق تماماً للعملاء"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QGroupBox, QPushButton, QLabel

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(400, 480)  # زيادة الارتفاع لاستيعاب البيانات الإضافية

            # إزالة علامة الاستفهام وتحسين شريط العنوان مطابق للبرنامج
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

            # تخصيص شريط العنوان مطابق للبرنامج
            try:
                from ui.title_bar_utils import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(dialog)
            except Exception as e:
                print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للنافذة الحوارية: {e}")

            # تطبيق نمط النافذة مطابق تماماً لنافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # التخطيط الرئيسي مضغوط
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(15, 10, 15, 10)  # تقليل الهوامش
            layout.setSpacing(8)  # تقليل المسافات

            # العنوان الرئيسي مضغوط
            title_container = QWidget()
            title_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                }
            """)

            title_inner_layout = QVBoxLayout(title_container)
            title_inner_layout.setContentsMargins(0, 0, 0, 0)
            title_inner_layout.setSpacing(3)  # تقليل المسافة

            # الأيقونة والعنوان الرئيسي مضغوط
            main_title = QLabel("🔧 تصدير مخصص للأقساط")
            main_title.setAlignment(Qt.AlignCenter)
            main_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 22px;
                    font-weight: bold;
                    background: transparent;
                    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                    padding: 5px;
                }
            """)

            # العنوان الفرعي التوضيحي مضغوط
            subtitle = QLabel("اختر البيانات المراد تصديرها بدقة")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: normal;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            title_inner_layout.addWidget(main_title)
            title_inner_layout.addWidget(subtitle)
            layout.addWidget(title_container)

            # مجموعة البيانات الأساسية مطابقة لنافذة الإحصائيات
            basic_group = QWidget()
            basic_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            basic_main_layout = QVBoxLayout(basic_group)
            basic_main_layout.setSpacing(5)  # تقليل المسافة
            basic_main_layout.setContentsMargins(15, 5, 15, 5)  # تقليل الهوامش

            # عنوان المجموعة مضغوط ومتوسط
            basic_title = QLabel("📋 البيانات الأساسية")
            basic_title.setAlignment(Qt.AlignCenter)  # توسيط العنوان
            basic_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            basic_main_layout.addWidget(basic_title)

            basic_layout = QVBoxLayout()
            basic_layout.setSpacing(3)  # تقليل المسافة أكثر

            self.export_installment_number = QCheckBox("🔢 رقم القسط")
            self.export_client_name = QCheckBox("👤 اسم العميل")
            self.export_client_phone = QCheckBox("📞 هاتف العميل")
            self.export_total_amount = QCheckBox("💰 إجمالي المبلغ")
            self.export_status = QCheckBox("📊 حالة القسط")

            # تحديد افتراضي
            self.export_client_name.setChecked(True)
            self.export_total_amount.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات
            checkboxes_data = [
                (self.export_installment_number, "#3B82F6"),
                (self.export_client_name, "#10B981"),
                (self.export_client_phone, "#F59E0B"),
                (self.export_total_amount, "#EF4444"),
                (self.export_status, "#8B5CF6")
            ]

            for checkbox, color in checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 2px;
                        margin: 0px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(8)  # تقليل المسافة
                item_layout.setContentsMargins(8, 3, 8, 3)  # تقليل الهوامش

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                basic_layout.addWidget(item_widget)

            basic_main_layout.addLayout(basic_layout)
            layout.addWidget(basic_group)

            # مجموعة البيانات المالية مطابقة لنافذة الإحصائيات
            financial_group = QWidget()
            financial_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            financial_main_layout = QVBoxLayout(financial_group)
            financial_main_layout.setSpacing(5)  # تقليل المسافة
            financial_main_layout.setContentsMargins(15, 5, 15, 5)  # تقليل الهوامش

            # عنوان المجموعة مضغوط ومتوسط
            financial_title = QLabel("💰 البيانات المالية")
            financial_title.setAlignment(Qt.AlignCenter)  # توسيط العنوان
            financial_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            financial_main_layout.addWidget(financial_title)

            financial_layout = QVBoxLayout()
            financial_layout.setSpacing(3)  # تقليل المسافة أكثر

            self.export_paid_amount = QCheckBox("✅ المبلغ المسدد")
            self.export_remaining_amount = QCheckBox("⏳ المبلغ المتبقي")
            self.export_payment_percentage = QCheckBox("📊 نسبة السداد")

            self.export_paid_amount.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات للبيانات المالية
            financial_checkboxes_data = [
                (self.export_paid_amount, "#10B981"),
                (self.export_remaining_amount, "#F59E0B"),
                (self.export_payment_percentage, "#8B5CF6")
            ]

            for checkbox, color in financial_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                financial_layout.addWidget(item_widget)

            financial_main_layout.addLayout(financial_layout)
            layout.addWidget(financial_group)

            # مجموعة البيانات الإضافية
            additional_group = QWidget()
            additional_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            additional_main_layout = QVBoxLayout(additional_group)
            additional_main_layout.setSpacing(5)  # تقليل المسافة
            additional_main_layout.setContentsMargins(15, 5, 15, 5)  # تقليل الهوامش

            # عنوان المجموعة مضغوط ومتوسط
            additional_title = QLabel("📅 البيانات الإضافية")
            additional_title.setAlignment(Qt.AlignCenter)  # توسيط العنوان
            additional_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            additional_main_layout.addWidget(additional_title)

            additional_layout = QVBoxLayout()
            additional_layout.setSpacing(3)  # تقليل المسافة أكثر

            self.export_date = QCheckBox("📅 تاريخ الإنشاء")
            self.export_due_date = QCheckBox("⏰ تاريخ الاستحقاق")
            self.export_notes = QCheckBox("📝 الملاحظات")

            # تطبيق تصميم مطابق لعناصر الإحصائيات للبيانات الإضافية
            additional_checkboxes_data = [
                (self.export_date, "#84CC16"),
                (self.export_due_date, "#EC4899"),
                (self.export_notes, "#EF4444")
            ]

            for checkbox, color in additional_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                additional_layout.addWidget(item_widget)

            additional_main_layout.addLayout(additional_layout)
            layout.addWidget(additional_group)

            # أزرار التحكم مطابقة للبرنامج الرئيسي
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)

            # زر الإلغاء مطابق للبرنامج الرئيسي مع ارتفاع أكبر
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            cancel_btn.setMinimumHeight(55)  # ارتفاع أكبر

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(cancel_btn, 'danger')

            # زر التصدير مطابق للبرنامج الرئيسي مع ارتفاع أكبر
            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_custom_export(dialog))
            export_btn.setMinimumHeight(55)  # ارتفاع أكبر

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(export_btn, 'emerald')

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(export_btn)
            layout.addLayout(buttons_layout)

            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    def perform_custom_export(self, dialog):
        """تنفيذ التصدير المخصص"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التصدير المخصص", f"تصدير_مخصص_أقساط_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                installments = self.session.query(Installment).order_by(Installment.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['التصدير المخصص للأقساط'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # إنشاء رؤوس الأعمدة حسب الاختيار
                    headers = []
                    if self.export_installment_number.isChecked():
                        headers.append('رقم القسط')
                    if self.export_client_name.isChecked():
                        headers.append('اسم العميل')
                    if self.export_client_phone.isChecked():
                        headers.append('هاتف العميل')
                    if self.export_total_amount.isChecked():
                        headers.append('إجمالي المبلغ')
                    if self.export_status.isChecked():
                        headers.append('حالة القسط')
                    if self.export_paid_amount.isChecked():
                        headers.append('المبلغ المسدد')
                    if self.export_remaining_amount.isChecked():
                        headers.append('المبلغ المتبقي')
                    if self.export_payment_percentage.isChecked():
                        headers.append('نسبة السداد')
                    if self.export_date.isChecked():
                        headers.append('تاريخ الإنشاء')
                    if self.export_due_date.isChecked():
                        headers.append('تاريخ الاستحقاق')
                    if self.export_notes.isChecked():
                        headers.append('الملاحظات')

                    writer.writerow(headers)

                    # كتابة البيانات
                    for installment in installments:
                        row = []
                        if self.export_installment_number.isChecked():
                            row.append(installment.installment_number or 'غير محدد')
                        if self.export_client_name.isChecked():
                            row.append(installment.client.name if installment.client else 'غير محدد')
                        if self.export_client_phone.isChecked():
                            row.append(installment.client.phone if installment.client and installment.client.phone else 'غير محدد')
                        if self.export_total_amount.isChecked():
                            row.append(f'{installment.total_amount:,.2f} جنيه' if installment.total_amount else '0.00 جنيه')
                        if self.export_status.isChecked():
                            status_map = {'pending': 'معلق', 'paid': 'مسدد', 'overdue': 'متأخر', 'cancelled': 'ملغي'}
                            row.append(status_map.get(installment.status, 'غير محدد'))
                        if self.export_paid_amount.isChecked():
                            row.append(f'{installment.paid_amount:,.2f} جنيه' if installment.paid_amount else '0.00 جنيه')
                        if self.export_remaining_amount.isChecked():
                            remaining = (installment.total_amount or 0) - (installment.paid_amount or 0)
                            row.append(f'{remaining:,.2f} جنيه')
                        if self.export_payment_percentage.isChecked():
                            percentage = ((installment.paid_amount or 0) / (installment.total_amount or 1) * 100) if installment.total_amount else 0
                            row.append(f'{percentage:.1f}%')
                        if self.export_date.isChecked():
                            row.append(installment.date.strftime('%Y-%m-%d') if installment.date else 'غير محدد')
                        if self.export_due_date.isChecked():
                            row.append(installment.due_date.strftime('%Y-%m-%d') if installment.due_date else 'غير محدد')
                        if self.export_notes.isChecked():
                            row.append(installment.notes or 'لا توجد ملاحظات')

                        writer.writerow(row)

                dialog.accept()
                self.show_success_message(f"تم التصدير المخصص بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_أقساط_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                installments = self.session.query(Installment).order_by(Installment.id.asc()).all()
                backup_data = {
                    'backup_date': datetime.now().isoformat(),
                    'total_installments': len(installments),
                    'installments': []
                }

                for installment in installments:
                    installment_data = {
                        'installment_number': installment.installment_number,
                        'client_name': installment.client.name if installment.client else None,
                        'client_phone': installment.client.phone if installment.client and installment.client.phone else None,
                        'total_amount': float(installment.total_amount) if installment.total_amount else 0.0,
                        'paid_amount': float(installment.paid_amount) if installment.paid_amount else 0.0,
                        'status': installment.status,
                        'date': installment.date.isoformat() if installment.date else None,
                        'due_date': installment.due_date.isoformat() if installment.due_date else None,
                        'notes': installment.notes
                    }
                    backup_data['installments'].append(installment_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختيار النسخة الاحتياطية", "",
                "JSON Files (*.json)"
            )

            if file_path:
                self.show_info_message(f"تم اختيار الملف للاستعادة:\n{file_path}\n\nسيتم تطوير ميزة الاستعادة قريباً...")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")


class AddInstallmentDialog(QDialog):
    """نافذة إضافة أو تعديل قسط مطابقة تماماً لنافذة العملاء"""

    def __init__(self, session, parent=None, installment=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد
        self.installment = installment  # القسط للتعديل (None للإضافة)
        self.is_edit_mode = installment is not None  # تحديد وضع التعديل

        self.setModal(True)
        self.resize(650, 850)  # نفس حجم نافذة العملاء تماماً
        self.setup_ui()

        # تحميل البيانات في وضع التعديل
        if self.is_edit_mode:
            self.load_installment_data()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة العملاء"""
        # عنوان النافذة
        if self.is_edit_mode:
            self.setWindowTitle("✏️ تعديل قسط - نظام إدارة الأقساط المتطور والشامل")
        else:
            self.setWindowTitle("➕ إضافة قسط جديد - نظام إدارة الأقساط المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان مطابق لنافذة العملاء
        self.customize_title_bar()

        # تطبيق نمط النافذة مطابق للعملاء
        self.setStyleSheet(self.get_dialog_styling())

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إنشاء النموذج
        self.create_form()

        # إنشاء الأزرار
        self.create_buttons()

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للعملاء"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def get_dialog_styling(self):
        """الحصول على تصميم النافذة مطابق تماماً للعملاء"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }

            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
                margin: 5px 0;
            }

            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox, QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(139, 92, 246, 0.7);
                border-radius: 10px;
                padding: 12px 15px;
                font-size: 13px;
                font-weight: bold;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                selection-color: #1f2937;
            }

            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus, QDateEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """

    def create_form(self):
        """إنشاء النموذج مطابق تماماً لنافذة العملاء"""
        # إنشاء دالة لتصميم النصوص مع عرض مقلل مطابقة للعملاء
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # إنشاء النموذج
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # رقم القسط (تلقائي في حالة الإضافة)
        self.installment_number_edit = QLineEdit()
        self.installment_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً...")
        if not self.is_edit_mode:
            self.installment_number_edit.setReadOnly(True)
        form_layout.addRow(create_styled_label("رقم القسط", "🏦"), self.installment_number_edit)

        # العميل
        self.client_combo = QComboBox()
        self.client_combo.setEditable(True)
        self.client_combo.setInsertPolicy(QComboBox.NoInsert)
        self.load_clients()
        form_layout.addRow(create_styled_label("العميل", "🧑‍💼", True), self.client_combo)

        # تاريخ الإنشاء
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow(create_styled_label("تاريخ الإنشاء", "📆"), self.date_edit)

        # موعد الاستحقاق
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))  # افتراضي 30 يوم
        self.due_date_edit.setCalendarPopup(True)
        form_layout.addRow(create_styled_label("موعد الاستحقاق", "⏳", True), self.due_date_edit)

        # إجمالي المبلغ
        self.total_amount_spinbox = QDoubleSpinBox()
        self.total_amount_spinbox.setRange(0, 999999999)
        self.total_amount_spinbox.setDecimals(2)
        self.total_amount_spinbox.setSuffix(" ج.م")
        form_layout.addRow(create_styled_label("إجمالي المبلغ", "💎", True), self.total_amount_spinbox)

        # المبلغ المسدد
        self.paid_amount_spinbox = QDoubleSpinBox()
        self.paid_amount_spinbox.setRange(0, 999999999)
        self.paid_amount_spinbox.setDecimals(2)
        self.paid_amount_spinbox.setSuffix(" ج.م")
        form_layout.addRow(create_styled_label("المبلغ المسدد", "💵"), self.paid_amount_spinbox)

        # حالة الدفع
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "pending - معلق",
            "paid - مسدد",
            "overdue - متأخر",
            "cancelled - ملغي"
        ])
        form_layout.addRow(create_styled_label("حالة الدفع", "🎯"), self.status_combo)

        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        # إضافة النموذج للتخطيط الرئيسي
        self.layout().addLayout(form_layout)

    def load_clients(self):
        """تحميل قائمة العملاء"""
        try:
            clients = self.session.query(Client).order_by(Client.name).all()
            self.client_combo.clear()
            self.client_combo.addItem("اختر العميل...", None)

            for client in clients:
                display_text = f"{client.name}"
                if client.phone:
                    display_text += f" - {client.phone}"
                self.client_combo.addItem(display_text, client.id)

        except Exception as e:
            show_installment_advanced_error(self, "خطأ في تحميل العملاء", f"حدث خطأ في تحميل العملاء: {str(e)}")

    def create_buttons(self):
        """إنشاء الأزرار مطابقة تماماً لنافذة العملاء"""
        # إطار الأزرار مطابق للعملاء
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(226, 232, 240, 0.1),
                    stop:1 rgba(203, 213, 224, 0.08));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                margin: 15px 5px 5px 5px;
                padding: 20px;
                min-height: 80px;
                max-height: 90px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.3 rgba(248, 250, 252, 0.17),
                    stop:0.7 rgba(226, 232, 240, 0.15),
                    stop:1 rgba(203, 213, 224, 0.12));
                border: 3px solid rgba(255, 255, 255, 0.4);
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(20)
        buttons_layout.setContentsMargins(10, 10, 10, 10)

        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumHeight(50)
        save_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(save_btn, 'emerald')
        save_btn.clicked.connect(self.save_installment)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumHeight(50)
        cancel_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        self.layout().addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار مطابق تماماً للعملاء"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل مطابق للعملاء
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6',
                    'primary': '#4f46e5'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def load_installment_data(self):
        """تحميل بيانات القسط في وضع التعديل"""
        if not self.installment:
            return

        try:
            # رقم القسط
            self.installment_number_edit.setText(self.installment.installment_number or "")

            # العميل
            if self.installment.client_id:
                for i in range(self.client_combo.count()):
                    if self.client_combo.itemData(i) == self.installment.client_id:
                        self.client_combo.setCurrentIndex(i)
                        break

            # التواريخ
            if self.installment.date:
                self.date_edit.setDate(QDate.fromString(self.installment.date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

            if self.installment.due_date:
                self.due_date_edit.setDate(QDate.fromString(self.installment.due_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

            # المبالغ
            self.total_amount_spinbox.setValue(self.installment.total_amount or 0)
            self.paid_amount_spinbox.setValue(self.installment.paid_amount or 0)

            # الحالة
            status_map = {
                'pending': 0,
                'paid': 1,
                'overdue': 2,
                'cancelled': 3
            }
            status_index = status_map.get(self.installment.status, 0)
            self.status_combo.setCurrentIndex(status_index)

            # الملاحظات
            self.notes_edit.setPlainText(self.installment.notes or "")

        except Exception as e:
            show_installment_advanced_error(self, "خطأ في تحميل البيانات", f"حدث خطأ في تحميل بيانات القسط: {str(e)}")

    def save_installment(self):
        """حفظ القسط الجديد أو تحديث القسط الموجود"""
        try:
            # التحقق من صحة البيانات مطابق للعملاء
            client_id = self.client_combo.currentData()
            if not client_id:
                show_installment_advanced_warning(self, "تحذير", "يرجى اختيار العميل")
                self.client_combo.setFocus()
                return

            total_amount = self.total_amount_spinbox.value()
            if total_amount <= 0:
                show_installment_advanced_warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                self.total_amount_spinbox.setFocus()
                return

            # استخراج الحالة
            status_text = self.status_combo.currentText()
            status = status_text.split(' - ')[0]

            if self.is_edit_mode:
                # حفظ القيم القديمة لتحديث الرصيد
                old_client_id = self.installment.client_id
                old_total_amount = self.installment.total_amount or 0
                old_paid_amount = self.installment.paid_amount or 0

                # القيم الجديدة
                new_paid_amount = self.paid_amount_spinbox.value()

                # إعادة الرصيد القديم للعميل القديم
                if old_client_id:
                    old_client = self.session.query(Client).get(old_client_id)
                    if old_client:
                        # إعادة المبلغ الإجمالي القديم وخصم المبلغ المدفوع القديم
                        old_client.balance = (old_client.balance or 0) + old_total_amount - old_paid_amount

                # تحديث القسط الموجود
                self.installment.client_id = client_id
                self.installment.date = self.date_edit.date().toPyDate()
                self.installment.due_date = self.due_date_edit.date().toPyDate()
                self.installment.total_amount = total_amount
                self.installment.paid_amount = new_paid_amount
                self.installment.status = status
                self.installment.notes = self.notes_edit.toPlainText().strip() or None

                # تطبيق الرصيد الجديد للعميل الجديد
                new_client = self.session.query(Client).get(client_id)
                balance_message = ""
                if new_client:
                    old_balance = new_client.balance or 0
                    # خصم المبلغ الإجمالي الجديد وإضافة المبلغ المدفوع الجديد
                    new_client.balance = old_balance - total_amount + new_paid_amount
                    balance_message = (
                        f"\n\nتحديث رصيد العميل:\n"
                        f"الرصيد السابق: {old_balance:,.2f} ج.م\n"
                        f"خصم المبلغ الإجمالي: -{total_amount:,.2f} ج.م\n"
                        f"إضافة المبلغ المدفوع: +{new_paid_amount:,.2f} ج.م\n"
                        f"الرصيد الجديد: {new_client.balance:,.2f} ج.م"
                    )

                self.session.commit()

                # رسالة نجاح التحديث مع تفاصيل الرصيد
                show_installment_advanced_success(
                    self,
                    "تم التحديث بنجاح",
                    f"تم تحديث القسط '{self.installment.installment_number}' بنجاح!{balance_message}"
                )

            else:
                # إنشاء قسط جديد
                installment_number = generate_installment_number(self.session)
                paid_amount = self.paid_amount_spinbox.value()

                new_installment = Installment(
                    installment_number=installment_number,
                    client_id=client_id,
                    date=self.date_edit.date().toPyDate(),
                    due_date=self.due_date_edit.date().toPyDate(),
                    total_amount=total_amount,
                    paid_amount=paid_amount,
                    status=status,
                    notes=self.notes_edit.toPlainText().strip() or None
                )

                # تحديث رصيد العميل - خصم المبلغ الإجمالي وإضافة المبلغ المدفوع
                client = self.session.query(Client).get(client_id)
                if client:
                    old_balance = client.balance or 0
                    # خصم المبلغ الإجمالي للقسط من رصيد العميل
                    client.balance = old_balance - total_amount
                    # إضافة المبلغ المدفوع إلى رصيد العميل
                    client.balance = client.balance + paid_amount

                self.session.add(new_installment)
                self.session.commit()

                # رسالة نجاح الإضافة مع تفاصيل الرصيد
                client_name = client.name if client else "غير محدد"
                balance_message = ""
                if client:
                    balance_message = (
                        f"\n\nتحديث رصيد العميل:\n"
                        f"الرصيد السابق: {old_balance:,.2f} ج.م\n"
                        f"خصم المبلغ الإجمالي: -{total_amount:,.2f} ج.م\n"
                        f"إضافة المبلغ المدفوع: +{paid_amount:,.2f} ج.م\n"
                        f"الرصيد الجديد: {client.balance:,.2f} ج.م"
                    )

                show_installment_advanced_success(
                    self,
                    "تم الإضافة بنجاح",
                    f"تم إضافة القسط '{installment_number}' للعميل '{client_name}' بنجاح!\nالمبلغ الإجمالي: {total_amount:.2f} ج.م{balance_message}"
                )

            self.accept()

        except Exception as e:
            self.session.rollback()
            show_installment_advanced_error(self, "خطأ في الحفظ", f"حدث خطأ في حفظ القسط: {str(e)}")


class InstallmentInfoDialog(QDialog):
    """
    نافذة عرض معلومات القسط التفصيلية - مطابقة تماماً لنافذة العملاء

    هذه النافذة تعتبر مطابقة للنموذج المرجعي لجميع نوافذ المعلومات في النظام
    المميزات:
    - تصميم موحد ومتسق مع حواف مربعة
    - ألوان واضحة ومتباينة للبيانات
    - تخطيط منظم ومرن
    - أزرار وظيفية متطورة
    - أيقونات محسنة ومتطورة
    """

    def __init__(self, parent=None, installment=None):
        super().__init__(parent)
        self.installment = installment
        self.parent_widget = parent
        self.original_entity_info = self.extract_original_entity_info()
        self.setup_ui()

    def extract_original_entity_info(self):
        """استخراج معلومات الكيان الأصلي من ملاحظات القسط"""
        if not self.installment or not self.installment.notes:
            return None

        notes = self.installment.notes
        entity_info = {}

        try:
            # البحث عن معرف التتبع لتحديد نوع الكيان
            import re
            tracking_match = re.search(r'رصيد سالب تلقائي - (client|supplier):(\d+)', notes)
            if tracking_match:
                entity_type = tracking_match.group(1)
                entity_id = int(tracking_match.group(2))

                # الحصول على البيانات من قاعدة البيانات
                if hasattr(self.parent_widget, 'session'):
                    session = self.parent_widget.session

                    if entity_type == 'client':
                        from database import Client
                        entity = session.query(Client).get(entity_id)
                        entity_info['type'] = 'عميل'
                        entity_info['type_en'] = 'client'
                    else:
                        from database import Supplier
                        entity = session.query(Supplier).get(entity_id)
                        entity_info['type'] = 'مورد'
                        entity_info['type_en'] = 'supplier'

                    if entity:
                        entity_info['id'] = entity.id
                        entity_info['name'] = entity.name
                        entity_info['phone'] = entity.phone
                        entity_info['email'] = entity.email
                        entity_info['address'] = entity.address
                        entity_info['balance'] = entity.balance
                        entity_info['notes'] = entity.notes
                        if hasattr(entity, 'status'):
                            entity_info['status'] = entity.status

                        return entity_info

            # إذا لم نجد معرف التتبع، نحاول استخراج المعلومات من النص
            lines = notes.split('\n')
            for line in lines:
                if 'نوع الكيان:' in line:
                    entity_info['type'] = line.split('نوع الكيان:')[1].strip()
                elif 'اسم الكيان:' in line:
                    entity_info['name'] = line.split('اسم الكيان:')[1].strip()
                elif '- الاسم:' in line:
                    entity_info['name'] = line.split('- الاسم:')[1].strip()
                elif '- الهاتف:' in line:
                    entity_info['phone'] = line.split('- الهاتف:')[1].strip()
                elif '- البريد الإلكتروني:' in line:
                    entity_info['email'] = line.split('- البريد الإلكتروني:')[1].strip()
                elif '- العنوان:' in line:
                    entity_info['address'] = line.split('- العنوان:')[1].strip()
                elif '- الرصيد الحالي:' in line:
                    balance_text = line.split('- الرصيد الحالي:')[1].strip()
                    try:
                        entity_info['balance'] = float(balance_text.replace('ج.م', '').replace(',', '').strip())
                    except:
                        entity_info['balance'] = 0.0
                elif '- الحالة:' in line:
                    entity_info['status'] = line.split('- الحالة:')[1].strip()

            return entity_info if entity_info else None

        except Exception as e:
            print(f"خطأ في استخراج معلومات الكيان الأصلي: {str(e)}")
            return None

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية
        # ═══════════════════════════════════════════════════════════════
        self.setWindowTitle("💳📋 معلومات القسط - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # عرض النافذة الأصلي مستعاد

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling())

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # إنشاء منطقة التمرير مطابقة للعملاء
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollArea > QWidget > QWidget {
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        """)

        # إنشاء widget المحتوى مع خلفية شفافة
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(15)

        # إضافة أقسام المعلومات
        self.create_info_sections(info_layout)

        scroll_area.setWidget(info_widget)
        layout.addWidget(scroll_area)

        # إضافة أزرار التحكم
        self.create_control_buttons(layout)

        # تطبيق تصميم شريط العنوان المتطور
        self.apply_advanced_title_bar_styling()

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان المتطور مطابق للعملاء"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان المتطور: {e}")

    def create_window_icon(self):
        """إنشاء أيقونة النافذة"""
        try:
            from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor
            pixmap = QPixmap(32, 32)
            pixmap.fill(QColor(79, 70, 229))
            return QIcon(pixmap)
        except:
            return QIcon()

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة الحذف"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي للنوافذ"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """

    def create_info_sections(self, layout):
        """إنشاء أقسام المعلومات مطابق تماماً للعملاء"""
        # قسم المعلومات الأساسية
        basic_info = [
            ("🏦 رقم القسط", self.installment.installment_number or "غير محدد"),
            ("🧑‍💼 العميل", self.installment.client.name if self.installment.client else "غير محدد"),
            ("📆 تاريخ الإنشاء", self.installment.date.strftime("%Y-%m-%d") if self.installment.date else "غير محدد"),
            ("⏳ موعد الاستحقاق", self.installment.due_date.strftime("%Y-%m-%d") if self.installment.due_date else "غير محدد")
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية", basic_info)

        # قسم معلومات الكيان الأصلي (العميل أو المورد)
        if self.original_entity_info:
            entity_info_list = [
                ("🏷️ نوع الكيان", self.original_entity_info.get('type', 'غير محدد')),
                ("👤 اسم الكيان", self.original_entity_info.get('name', 'غير محدد')),
                ("📞 رقم الهاتف", self.original_entity_info.get('phone', 'غير محدد') or 'غير محدد'),
                ("📧 البريد الإلكتروني", self.original_entity_info.get('email', 'غير محدد') or 'غير محدد'),
                ("🏠 العنوان", self.original_entity_info.get('address', 'غير محدد') or 'غير محدد'),
                ("💰 الرصيد الحالي", f"{self.original_entity_info.get('balance', 0):.2f} ج.م")
            ]

            # إضافة الحالة إذا كانت متوفرة (للموردين)
            if 'status' in self.original_entity_info:
                entity_info_list.append(("📊 الحالة", self.original_entity_info.get('status', 'غير محدد')))

            self.add_info_section(layout, f"🔗 بيانات {self.original_entity_info.get('type', 'الكيان')} الأصلي", entity_info_list)

        # قسم المعلومات المالية
        total_amount = f"{self.installment.total_amount:.2f} ج.م" if self.installment.total_amount else "0.00 ج.م"
        paid_amount = f"{self.installment.paid_amount:.2f} ج.م" if self.installment.paid_amount else "0.00 ج.م"
        remaining_amount = (self.installment.total_amount or 0) - (self.installment.paid_amount or 0)
        remaining_text = f"{remaining_amount:.2f} ج.م"

        financial_info = [
            ("💎 إجمالي المبلغ", total_amount),
            ("💵 المبلغ المسدد", paid_amount),
            ("💰 المبلغ المتبقي", remaining_text),
            ("📊 نسبة السداد", f"{((self.installment.paid_amount or 0) / (self.installment.total_amount or 1) * 100):.1f}%")
        ]
        self.add_info_section(layout, "💰 المعلومات المالية", financial_info)

        # قسم الحالة والملاحظات
        status_map = {
            'pending': '⏰ معلق',
            'paid': '✅ مسدد',
            'overdue': '🚨 متأخر',
            'cancelled': '🚫 ملغي'
        }
        status_text = status_map.get(self.installment.status, '❓ غير محدد')

        status_info = [
            ("🎯 حالة الدفع", status_text),
            ("📝 الملاحظات", self.installment.notes or "لا توجد ملاحظات")
        ]
        self.add_info_section(layout, "📝 الحالة والملاحظات", status_info)

        # قسم الملاحظات والتفاصيل الإضافية (مطابق للعملاء)
        self.add_info_section(layout, "📝 ملاحظات وتفاصيل إضافية", [
            ("📝 الملاحظات", self.installment.notes or "لا توجد ملاحظات"),
            ("🔍 معلومات إضافية", self.get_additional_info()),
            ("📋 ملخص الحساب", self.get_account_summary())
        ])

    def add_info_section(self, layout, title, info_list):
        """إضافة قسم معلومات مطابق تماماً للعملاء"""
        # إطار القسم مطابق للعملاء
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(241, 245, 249, 0.10),
                    stop:1 rgba(226, 232, 240, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 15px;
                margin: 8px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setSpacing(12)
        section_layout.setContentsMargins(15, 15, 15, 15)

        # عنوان القسم
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 22px;  /* خط أكبر للعناوين */
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 15px 18px;  /* حشو أكبر */
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(99, 102, 241, 0.7),
                    stop:1 rgba(139, 92, 246, 0.8));
                border: 2px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        section_layout.addWidget(title_label)

        # إضافة المعلومات
        for label_text, value_text in info_list:
            self.add_info_row_to_section(section_layout, label_text, value_text)

        layout.addWidget(section_frame)

    def get_value_color(self, label, value):
        """تحديد لون القيمة حسب المحتوى - مطابق تماماً للعملاء"""
        try:
            # ألوان للرصيد والمبالغ المالية
            if "مبلغ" in label or "ج.م" in value:
                if "0" in value or "0.0" in value:
                    return "#E2E8F0"  # رمادي فاتح للصفر
                elif any(pos in value for pos in ["دائن", "💚", "🟢"]):
                    return "#00FF7F"  # أخضر نيون للموجب
                elif any(neg in value for neg in ["مدين", "🔴"]):
                    return "#FF6B6B"  # أحمر نيون للسالب
                else:
                    return "#FFFFFF"  # أبيض نقي افتراضي

            # ألوان للحالات
            elif "حالة" in label:
                if any(good in value for good in ["مسدد", "✅", "ممتاز", "🌟"]):
                    return "#00FF7F"  # أخضر نيون للحالات الجيدة
                elif any(warn in value for warn in ["معلق", "⚠️", "متوسط", "⏰"]):
                    return "#FFD700"  # ذهبي للتحذيرات
                elif any(bad in value for bad in ["متأخر", "🚨", "ملغي", "🚫"]):
                    return "#FF6B6B"  # أحمر نيون للحالات السيئة
                else:
                    return "#00BFFF"  # أزرق سماوي للحالات العادية

            # ألوان للنسب المئوية
            elif "نسبة" in label or "%" in value:
                try:
                    percentage = float(value.replace('%', '').replace('نسبة السداد:', '').strip())
                    if percentage >= 100:
                        return "#00FF7F"  # أخضر للمكتمل
                    elif percentage >= 75:
                        return "#FFD700"  # ذهبي للعالي
                    elif percentage >= 50:
                        return "#FFA500"  # برتقالي للمتوسط
                    else:
                        return "#FF6B6B"  # أحمر للمنخفض
                except:
                    return "#FFFFFF"

            # ألوان للتواريخ
            elif "تاريخ" in label:
                return "#F0F8FF"  # أبيض مزرق للتواريخ

            # ألوان للملاحظات
            elif "ملاحظات" in label:
                if "لا توجد" in value:
                    return "#C0C0C0"  # فضي للفارغ
                else:
                    return "#F5F5DC"  # بيج للنصوص

            # لون افتراضي
            else:
                return "#FFFFFF"  # أبيض نقي افتراضي

        except Exception as e:
            print(f"خطأ في تحديد لون القيمة: {e}")
            return "#FFFFFF"

    def add_info_row_to_section(self, layout, label_text, value_text):
        """إضافة صف معلومات مطابق تماماً للعملاء"""
        row_frame = QFrame()
        row_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
                margin: 2px 0;
            }
        """)
        row_layout = QHBoxLayout(row_frame)
        row_layout.setContentsMargins(0, 5, 0, 5)
        row_layout.setSpacing(15)

        # العنوان مطابق تماماً للعملاء مع خط أكبر
        label = QLabel(label_text)
        label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;  /* خط أكبر لعناوين البيانات */
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 14px 16px;  /* حشو أكبر */
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(99, 102, 241, 0.7),
                    stop:1 rgba(139, 92, 246, 0.8));
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                min-width: 190px;  /* عرض مستعاد للنافذة الأصلية */
                max-width: 240px;
            }
        """)

        # القيمة مطابقة تماماً للعملاء مع خط أكبر
        value = QLabel(str(value_text))
        value.setStyleSheet(f"""
            QLabel {{
                color: {self.get_value_color(label_text, str(value_text))};
                font-size: 15px;  /* خط أكبر لقيم البيانات */
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 14px 16px;  /* حشو أكبر */
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.12),
                    stop:0.3 rgba(248, 250, 252, 0.1),
                    stop:0.7 rgba(241, 245, 249, 0.08),
                    stop:1 rgba(226, 232, 240, 0.06));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
            }}
        """)
        value.setWordWrap(True)

        row_layout.addWidget(label)
        row_layout.addWidget(value, 1)  # تمدد القيمة لتأخذ المساحة المتبقية

        layout.addWidget(row_frame)



    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم مطابقة للعملاء"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(226, 232, 240, 0.1),
                    stop:1 rgba(203, 213, 224, 0.08));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                margin: 15px 5px 5px 5px;
                padding: 20px;
                min-height: 80px;
                max-height: 90px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(10, 10, 10, 10)

        # زر الإغلاق - في المقدمة مطابق تماماً للعملاء
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(180)  # عرض مستعاد للنافذة الأصلية
        close_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(close_btn, 'danger', False)
        close_btn.clicked.connect(self.close)

        # زر الطباعة - مطابق تماماً للعملاء
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)  # عرض أكبر لزر الطباعة
        print_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(print_btn, 'emerald', False)  # لون مطابق للعملاء
        print_btn.clicked.connect(self.print_info)

        # زر التصدير إلى PDF - مطابق تماماً للعملاء
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(180)  # عرض مستعاد للنافذة الأصلية
        export_pdf_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(export_pdf_btn, 'info', False)
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة - مطابق تماماً للعملاء
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)  # عرض أكبر لزر الملاحظات
        note_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(note_btn, 'emerald', False)
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار مطابق تماماً للعملاء: إغلاق، طباعة، تصدير، ملاحظة
        buttons_layout.addWidget(close_btn)  # زر الإغلاق في المقدمة
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة مطابقة للعملاء"""
        try:
            dialog = AddInstallmentNoteDialog(self, self.installment, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                # تحديث العرض في النافذة الحالية
                self.refresh_installment_info()

        except Exception as e:
            show_installment_advanced_error(self, "خطأ في الملاحظات", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_installment_info(self):
        """تحديث معلومات القسط في النافذة الحالية"""
        try:
            # إعادة تحميل بيانات القسط من قاعدة البيانات
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                self.parent_widget.session.refresh(self.installment)

            # إعادة إنشاء المحتوى
            self.setup_ui()

        except Exception as e:
            print(f"خطأ في تحديث معلومات القسط: {e}")

    def get_additional_info(self):
        """الحصول على معلومات إضافية للقسط"""
        try:
            info_parts = []

            # معلومات الحالة
            if self.installment.status:
                status_names = {
                    'pending': 'معلق',
                    'paid': 'مسدد',
                    'overdue': 'متأخر',
                    'cancelled': 'ملغي'
                }
                info_parts.append(f"الحالة: {status_names.get(self.installment.status, 'غير محدد')}")

            # معلومات التواريخ
            if self.installment.date and self.installment.due_date:
                from datetime import datetime
                days_diff = (self.installment.due_date - self.installment.date).days
                info_parts.append(f"مدة الاستحقاق: {days_diff} يوم")

            # معلومات المبالغ
            if self.installment.total_amount and self.installment.paid_amount:
                remaining = self.installment.total_amount - self.installment.paid_amount
                if remaining > 0:
                    info_parts.append(f"متبقي: {remaining:.2f} ج.م")
                elif remaining == 0:
                    info_parts.append("مسدد بالكامل")

            return " | ".join(info_parts) if info_parts else "لا توجد معلومات إضافية"

        except Exception as e:
            return f"خطأ في جلب المعلومات: {str(e)}"

    def get_account_summary(self):
        """الحصول على ملخص حساب القسط"""
        try:
            summary_parts = []

            # إجمالي المبلغ
            if self.installment.total_amount:
                summary_parts.append(f"الإجمالي: {self.installment.total_amount:.2f} ج.م")

            # المبلغ المسدد
            if self.installment.paid_amount:
                summary_parts.append(f"المسدد: {self.installment.paid_amount:.2f} ج.م")

            # النسبة المئوية للسداد
            if self.installment.total_amount and self.installment.total_amount > 0:
                percentage = (self.installment.paid_amount or 0) / self.installment.total_amount * 100
                summary_parts.append(f"نسبة السداد: {percentage:.1f}%")

            return " | ".join(summary_parts) if summary_parts else "لا يوجد ملخص متاح"

        except Exception as e:
            return f"خطأ في ملخص الحساب: {str(e)}"

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار مطابق تماماً للعملاء"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للنموذج المرجعي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'primary': {
                        'base': '#4F46E5',
                        'hover': '#4338CA',
                        'pressed': '#3730A3',
                        'shadow': 'rgba(79, 70, 229, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    },
                    'emerald': {
                        'base': '#10B981',
                        'hover': '#059669',
                        'pressed': '#047857',
                        'shadow': 'rgba(16, 185, 129, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: white;
                        border: 2px solid {colors['base']};
                        border-radius: 12px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: bold;
                        font-family: 'Segoe UI', 'Roboto', sans-serif;
                        box-shadow: 0 4px 8px {colors['shadow']};
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['pressed']});
                        border: 2px solid {colors['hover']};
                        transform: translateY(-2px);
                        box-shadow: 0 6px 12px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: {colors['pressed']};
                        border: 2px solid {colors['pressed']};
                        transform: translateY(0px);
                        box-shadow: 0 2px 4px {colors['shadow']};
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def print_info(self):
        """طباعة معلومات القسط"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from PyQt5.QtCore import QRect

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخط
                font = QFont("Arial", 12)
                painter.setFont(font)

                # طباعة المعلومات
                y = 100
                painter.drawText(100, y, f"معلومات القسط: {self.installment.installment_number}")
                y += 50
                painter.drawText(100, y, f"العميل: {self.installment.client.name if self.installment.client else 'غير محدد'}")
                y += 50
                painter.drawText(100, y, f"إجمالي المبلغ: {self.installment.total_amount:.2f} ج.م")
                y += 50
                painter.drawText(100, y, f"المبلغ المسدد: {self.installment.paid_amount:.2f} ج.م")
                y += 50
                remaining = (self.installment.total_amount or 0) - (self.installment.paid_amount or 0)
                painter.drawText(100, y, f"المبلغ المتبقي: {remaining:.2f} ج.م")

                painter.end()

                show_installment_advanced_success(self, "تم الطباعة", "تم طباعة معلومات القسط بنجاح!")

        except Exception as e:
            show_installment_advanced_error(self, "خطأ في الطباعة", f"حدث خطأ في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات القسط إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف PDF",
                f"installment_{self.installment.installment_number}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)

                painter = QPainter(printer)

                # إعداد الخط
                font = QFont("Arial", 12)
                painter.setFont(font)

                # كتابة المعلومات
                y = 100
                painter.drawText(100, y, f"معلومات القسط: {self.installment.installment_number}")
                y += 50
                painter.drawText(100, y, f"العميل: {self.installment.client.name if self.installment.client else 'غير محدد'}")
                y += 50
                painter.drawText(100, y, f"تاريخ الإنشاء: {self.installment.date.strftime('%Y-%m-%d') if self.installment.date else 'غير محدد'}")
                y += 50
                painter.drawText(100, y, f"موعد الاستحقاق: {self.installment.due_date.strftime('%Y-%m-%d') if self.installment.due_date else 'غير محدد'}")
                y += 50
                painter.drawText(100, y, f"إجمالي المبلغ: {self.installment.total_amount:.2f} ج.م")
                y += 50
                painter.drawText(100, y, f"المبلغ المسدد: {self.installment.paid_amount:.2f} ج.م")
                y += 50
                remaining = (self.installment.total_amount or 0) - (self.installment.paid_amount or 0)
                painter.drawText(100, y, f"المبلغ المتبقي: {remaining:.2f} ج.م")
                y += 50
                status_map = {
                    'pending': 'معلق',
                    'paid': 'مسدد',
                    'overdue': 'متأخر',
                    'cancelled': 'ملغي'
                }
                status_text = status_map.get(self.installment.status, 'غير محدد')
                painter.drawText(100, y, f"حالة الدفع: {status_text}")

                if self.installment.notes:
                    y += 50
                    painter.drawText(100, y, f"الملاحظات: {self.installment.notes}")

                painter.end()

                show_installment_advanced_success(self, "تم التصدير", f"تم تصدير معلومات القسط إلى:\n{file_path}")

        except Exception as e:
            show_installment_advanced_error(self, "خطأ في التصدير", f"حدث خطأ في التصدير: {str(e)}")


class AddInstallmentNoteDialog(QDialog):
    """نافذة ملاحظات بسيطة جداً مطابقة للعملاء"""

    def __init__(self, parent=None, installment=None, parent_widget=None):
        super().__init__(parent)
        self.installment = installment
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً مطابقة للعملاء"""
        installment_name = f"القسط {self.installment.installment_number}" if self.installment and self.installment.installment_number else "قسط"
        self.setWindowTitle(f"📝 {installment_name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(InstallmentInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # عنوان النافذة
        title_label = QLabel("📝 إضافة ملاحظة للقسط")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(79, 70, 229, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # حقل النص
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل الملاحظة هنا...")
        if self.installment and self.installment.notes:
            self.notes_edit.setPlainText(self.installment.notes)

        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(139, 92, 246, 0.7);
                border-radius: 10px;
                padding: 12px;
                font-size: 13px;
                font-weight: bold;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                selection-color: #1f2937;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
            }
        """)
        layout.addWidget(self.notes_edit)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر الحفظ مطابق تماماً للعملاء
        save_btn = QPushButton("💾 حفظ الملاحظة")
        save_btn.setMinimumWidth(180)  # عرض مطابق للعملاء
        save_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        save_btn.clicked.connect(self.save_note)
        self.style_advanced_button(save_btn, 'emerald', False)

        # زر الإلغاء مطابق تماماً للعملاء
        cancel_btn = QPushButton("❌ إلغاء العملية")
        cancel_btn.setMinimumWidth(180)  # عرض مطابق للعملاء
        cancel_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        cancel_btn.clicked.connect(self.reject)
        self.style_advanced_button(cancel_btn, 'danger', False)

        # ترتيب مطابق للعملاء: إلغاء ثم حفظ
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليكون أسود"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            pass

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار مطابق للعملاء"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للنموذج المرجعي
                color_schemes = {
                    'emerald': {
                        'base': '#10B981',
                        'hover': '#059669',
                        'pressed': '#047857',
                        'shadow': 'rgba(16, 185, 129, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['emerald'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: white;
                        border: 2px solid {colors['base']};
                        border-radius: 12px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: bold;
                        font-family: 'Segoe UI', 'Roboto', sans-serif;
                        box-shadow: 0 4px 8px {colors['shadow']};
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['pressed']});
                        border: 2px solid {colors['hover']};
                        transform: translateY(-2px);
                        box-shadow: 0 6px 12px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: {colors['pressed']};
                        border: 2px solid {colors['pressed']};
                        transform: translateY(0px);
                        box-shadow: 0 2px 4px {colors['shadow']};
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def save_note(self):
        """حفظ الملاحظة"""
        try:
            if self.installment:
                # تحديث الملاحظة
                self.installment.notes = self.notes_edit.toPlainText().strip() or None

                # حفظ في قاعدة البيانات
                if self.parent_widget and hasattr(self.parent_widget, 'session'):
                    self.parent_widget.session.commit()

                show_installment_advanced_success(self, "تم الحفظ", "تم حفظ الملاحظة بنجاح!")
                self.accept()
            else:
                show_installment_advanced_warning(self, "تحذير", "لا يمكن حفظ الملاحظة - القسط غير محدد")

        except Exception as e:
            show_installment_advanced_error(self, "خطأ في الحفظ", f"حدث خطأ في حفظ الملاحظة: {str(e)}")


class PayInstallmentDialog(QDialog):
    """نافذة دفع قسط إضافي مطابقة لنافذة الحذف"""

    def __init__(self, session, parent=None, installment=None):
        super().__init__(parent)
        self.session = session
        self.installment = installment
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle("💰 دفع قسط - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(450, 350)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # العنوان الداخلي
        title_label = QLabel("💰 دفع قسط إضافي")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #10b981;
                font-size: 18px;
                font-weight: bold;
                margin: 10px 0;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """)
        layout.addWidget(title_label)

        # معلومات القسط
        self.create_installment_info_section(layout)

        # قسم إدخال مبلغ الدفع
        self.create_payment_input_section(layout)

        # الأزرار
        self.create_buttons_section(layout)

        self.setLayout(layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة الحذف"""
        try:
            # إنشاء أيقونة مخصصة
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # رسم دائرة خضراء للخلفية
            painter.setBrush(QBrush(QColor("#10b981")))
            painter.setPen(QPen(QColor("#065f46"), 2))
            painter.drawEllipse(2, 2, 28, 28)

            # رسم رمز الدولار
            painter.setPen(QPen(QColor("white"), 3))
            painter.setFont(QFont("Arial", 14, QFont.Bold))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "$")

            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")

    def create_installment_info_section(self, layout):
        """إنشاء قسم معلومات القسط"""
        # إطار معلومات القسط
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
        """)

        info_layout = QVBoxLayout(info_frame)
        info_layout.setSpacing(8)

        # معلومات القسط
        client_name = self.installment.client.name if self.installment.client else "غير محدد"
        total_amount = self.installment.total_amount or 0
        paid_amount = self.installment.paid_amount or 0
        remaining_amount = total_amount - paid_amount

        info_text = f"""
        <div style='color: white; font-size: 13px; line-height: 1.6;'>
            <b>🏷️ رقم القسط:</b> {self.installment.installment_number}<br>
            <b>👤 العميل:</b> {client_name}<br>
            <b>💰 المبلغ الإجمالي:</b> {total_amount:,.2f} ج.م<br>
            <b>✅ المبلغ المدفوع:</b> {paid_amount:,.2f} ج.م<br>
            <b>⏳ المبلغ المتبقي:</b> {remaining_amount:,.2f} ج.م
        </div>
        """

        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background: transparent;
                border: none;
                padding: 5px;
            }
        """)
        info_layout.addWidget(info_label)
        layout.addWidget(info_frame)

    def create_payment_input_section(self, layout):
        """إنشاء قسم إدخال مبلغ الدفع"""
        # إطار إدخال الدفع
        payment_frame = QFrame()
        payment_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)

        payment_layout = QVBoxLayout(payment_frame)
        payment_layout.setSpacing(10)

        # تسمية مبلغ الدفع
        payment_label = QLabel("💳 مبلغ الدفع الإضافي:")
        payment_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                margin-bottom: 5px;
            }
        """)
        payment_layout.addWidget(payment_label)

        # حقل إدخال مبلغ الدفع
        remaining_amount = (self.installment.total_amount or 0) - (self.installment.paid_amount or 0)
        self.payment_amount_spinbox = QDoubleSpinBox()
        self.payment_amount_spinbox.setRange(0.01, remaining_amount)
        self.payment_amount_spinbox.setDecimals(2)
        self.payment_amount_spinbox.setSuffix(" ج.م")
        self.payment_amount_spinbox.setValue(remaining_amount)
        self.payment_amount_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #10b981;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
            }
            QDoubleSpinBox:focus {
                border-color: #059669;
                background: white;
            }
        """)
        payment_layout.addWidget(self.payment_amount_spinbox)

        # ملاحظة الحد الأقصى
        max_note = QLabel(f"💡 الحد الأقصى للدفع: {remaining_amount:,.2f} ج.م")
        max_note.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 12px;
                font-style: italic;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        payment_layout.addWidget(max_note)

        layout.addWidget(payment_frame)

    def create_buttons_section(self, layout):
        """إنشاء قسم الأزرار مطابق تماماً للعملاء"""
        # إطار الأزرار مطابق تماماً للعملاء
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(226, 232, 240, 0.1),
                    stop:1 rgba(203, 213, 224, 0.08));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                margin: 15px 5px 5px 5px;
                padding: 20px;
                min-height: 80px;
                max-height: 90px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.3 rgba(248, 250, 252, 0.17),
                    stop:0.7 rgba(226, 232, 240, 0.15),
                    stop:1 rgba(203, 213, 224, 0.12));
                border: 3px solid rgba(255, 255, 255, 0.4);
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(10, 10, 10, 10)

        # زر الإلغاء - مطابق تماماً للعملاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(150)
        cancel_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(cancel_btn, 'danger', False)
        cancel_btn.clicked.connect(self.reject)

        # زر الدفع - مطابق تماماً للعملاء
        pay_btn = QPushButton("💰 دفع القسط")
        pay_btn.setMinimumWidth(180)
        pay_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(pay_btn, 'emerald', False)
        pay_btn.clicked.connect(self.save_payment)

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(pay_btn)
        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مطابق تماماً للعملاء"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق تماماً للعملاء
                colors = {
                    'emerald': {
                        'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                        'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                        'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                        'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                        'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                    },
                    'danger': {
                        'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                        'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                        'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                        'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                        'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                    }
                }

                color_set = colors.get(button_type, colors['emerald'])

                # مؤشر القائمة المنسدلة
                menu_indicator = ""
                if has_menu:
                    menu_indicator = """
                        QPushButton::menu-indicator {
                            image: none;
                            width: 0px;
                        }
                    """

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_set['bg_start']},
                            stop:0.25 {color_set['bg_mid']},
                            stop:0.75 {color_set['bg_end']},
                            stop:1 {color_set['bg_bottom']});
                        color: {color_set['text']};
                        border: 3px solid {color_set['border']};
                        border-radius: 12px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        box-shadow: 0 6px 12px {color_set['shadow']};
                        min-height: 45px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_set['hover_start']},
                            stop:0.25 {color_set['hover_mid']},
                            stop:0.75 {color_set['hover_end']},
                            stop:1 {color_set['hover_bottom']});
                        border: 3px solid {color_set['hover_border']};
                        transform: translateY(-3px);
                        box-shadow: 0 8px 16px {color_set['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_set['pressed_start']},
                            stop:0.25 {color_set['pressed_mid']},
                            stop:0.75 {color_set['pressed_end']},
                            stop:1 {color_set['pressed_bottom']});
                        border: 3px solid {color_set['pressed_border']};
                        transform: translateY(1px);
                        box-shadow: 0 3px 6px {color_set['shadow']};
                    }}
                    {menu_indicator}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def save_payment(self):
        """حفظ الدفع الإضافي"""
        try:
            payment_amount = self.payment_amount_spinbox.value()

            if payment_amount <= 0:
                from utils import show_error_message
                show_error_message(self, "يجب أن يكون مبلغ الدفع أكبر من صفر")
                return

            # تحديث المبلغ المدفوع
            old_paid_amount = self.installment.paid_amount or 0
            new_paid_amount = old_paid_amount + payment_amount

            # التأكد من عدم تجاوز المبلغ الإجمالي
            if new_paid_amount > (self.installment.total_amount or 0):
                from utils import show_error_message
                show_error_message(self, "لا يمكن أن يتجاوز المبلغ المدفوع المبلغ الإجمالي للقسط")
                return

            # تحديث رصيد العميل
            old_balance = 0
            if self.installment.client_id:
                client = self.session.query(Client).get(self.installment.client_id)
                if client:
                    old_balance = client.balance or 0
                    # إضافة مبلغ الدفع الإضافي إلى رصيد العميل
                    client.balance = old_balance + payment_amount

            # تحديث القسط
            self.installment.paid_amount = new_paid_amount

            # تحديث حالة القسط بناءً على المبلغ المدفوع
            total_amount = self.installment.total_amount or 0
            if new_paid_amount >= total_amount:
                self.installment.status = 'paid'  # مسدد بالكامل
            elif new_paid_amount > 0:
                self.installment.status = 'pending'  # معلق (مدفوع جزئياً)
            else:
                self.installment.status = 'pending'  # معلق (غير مدفوع)

            self.session.commit()

            # رسالة نجاح مع تفاصيل شاملة
            client_name = self.installment.client.name if self.installment.client else "غير محدد"

            # تفاصيل القسط
            remaining_amount = total_amount - new_paid_amount
            status_text = "مسدد بالكامل ✅" if new_paid_amount >= total_amount else f"متبقي {remaining_amount:,.2f} ج.م ⏳"

            installment_details = (
                f"\n\nتفاصيل القسط:\n"
                f"المبلغ الإجمالي: {total_amount:,.2f} ج.م\n"
                f"المبلغ المدفوع الجديد: {new_paid_amount:,.2f} ج.م\n"
                f"الحالة: {status_text}"
            )

            # تفاصيل رصيد العميل
            balance_message = ""
            if self.installment.client_id and client:
                balance_message = (
                    f"\n\nتحديث رصيد العميل:\n"
                    f"الرصيد السابق: {old_balance:,.2f} ج.م\n"
                    f"إضافة مبلغ الدفع: +{payment_amount:,.2f} ج.م\n"
                    f"الرصيد الجديد: {client.balance:,.2f} ج.م"
                )

            show_installment_advanced_success(
                self,
                "تم الدفع بنجاح",
                f"تم دفع {payment_amount:,.2f} ج.م للقسط '{self.installment.installment_number}'\n"
                f"للعميل '{client_name}' بنجاح!{installment_details}{balance_message}"
            )

            self.accept()

        except Exception as e:
            self.session.rollback()
            show_installment_advanced_error(self, "خطأ في الدفع", f"حدث خطأ في حفظ الدفع: {str(e)}")


# دوال الرسائل المتطورة





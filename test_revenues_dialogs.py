#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع نوافذ التحذير المتطورة للإيرادات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer

# استيراد النوافذ المتطورة الجديدة
from ui.revenues import (
    RevenueWarningDialog, 
    RevenueErrorDialog, 
    RevenueInfoDialog, 
    DeleteRevenueDialog,
    show_revenue_advanced_warning,
    show_revenue_advanced_error,
    show_revenue_advanced_info,
    ensure_revenue_dialog_stays_visible
)

class TestRevenuesWindow(QMainWindow):
    """نافذة اختبار النوافذ المتطورة للإيرادات"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 اختبار نوافذ التحذير المتطورة للإيرادات")
        self.setGeometry(100, 100, 800, 600)
        
        # إعداد الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان رئيسي
        title_label = QPushButton("💰 اختبار نوافذ التحذير المتطورة للإيرادات")
        title_label.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        title_label.setEnabled(False)
        layout.addWidget(title_label)
        
        # الصف الأول - النوافذ الأساسية
        row1 = QHBoxLayout()
        
        warning_btn = QPushButton("⚠️ نافذة تحذير")
        warning_btn.clicked.connect(self.test_warning_dialog)
        self.style_button(warning_btn, '#F59E0B')
        row1.addWidget(warning_btn)
        
        error_btn = QPushButton("❌ نافذة خطأ")
        error_btn.clicked.connect(self.test_error_dialog)
        self.style_button(error_btn, '#EF4444')
        row1.addWidget(error_btn)
        
        info_btn = QPushButton("ℹ️ نافذة معلومات")
        info_btn.clicked.connect(self.test_info_dialog)
        self.style_button(info_btn, '#3B82F6')
        row1.addWidget(info_btn)
        
        layout.addLayout(row1)
        
        # الصف الثاني - النوافذ المتقدمة
        row2 = QHBoxLayout()
        
        delete_btn = QPushButton("🗑️ نافذة حذف")
        delete_btn.clicked.connect(self.test_delete_dialog)
        self.style_button(delete_btn, '#DC2626')
        row2.addWidget(delete_btn)
        
        helper_warning_btn = QPushButton("⚠️ دالة التحذير")
        helper_warning_btn.clicked.connect(self.test_helper_warning)
        self.style_button(helper_warning_btn, '#F59E0B')
        row2.addWidget(helper_warning_btn)
        
        helper_error_btn = QPushButton("❌ دالة الخطأ")
        helper_error_btn.clicked.connect(self.test_helper_error)
        self.style_button(helper_error_btn, '#EF4444')
        row2.addWidget(helper_error_btn)
        
        layout.addLayout(row2)
        
        # الصف الثالث - المزيد من الاختبارات
        row3 = QHBoxLayout()
        
        helper_info_btn = QPushButton("ℹ️ دالة المعلومات")
        helper_info_btn.clicked.connect(self.test_helper_info)
        self.style_button(helper_info_btn, '#3B82F6')
        row3.addWidget(helper_info_btn)
        
        test_all_btn = QPushButton("🧪 اختبار جميع النوافذ")
        test_all_btn.clicked.connect(self.test_all_dialogs)
        self.style_button(test_all_btn, '#6366F1')
        row3.addWidget(test_all_btn)
        
        close_btn = QPushButton("❌ إغلاق الاختبار")
        close_btn.clicked.connect(self.close)
        self.style_button(close_btn, '#6B7280')
        row3.addWidget(close_btn)
        
        layout.addLayout(row3)
        
        # إضافة مساحة فارغة
        layout.addStretch()
    
    def style_button(self, button, color):
        """تطبيق تصميم على الأزرار"""
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 12px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
                transform: translateY(0px);
            }}
        """)
    
    def test_warning_dialog(self):
        """اختبار نافذة التحذير"""
        dialog = RevenueWarningDialog(
            self, 
            "تحذير مهم", 
            "هذا مثال على نافذة تحذير متطورة للإيرادات.\nتم تصميمها لتتطابق مع نافذة الحذف المرجعية.",
            "⚠️"
        )
        dialog.exec_()
    
    def test_error_dialog(self):
        """اختبار نافذة الخطأ"""
        dialog = RevenueErrorDialog(
            self, 
            "خطأ في النظام", 
            "حدث خطأ غير متوقع في النظام.\nيرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.",
            "❌"
        )
        dialog.exec_()
    
    def test_info_dialog(self):
        """اختبار نافذة المعلومات"""
        dialog = RevenueInfoDialog(
            self, 
            "معلومات مفيدة", 
            "هذه نافذة معلومات متطورة للإيرادات.\nيمكن استخدامها لعرض معلومات مهمة للمستخدم.",
            "ℹ️"
        )
        dialog.exec_()
    
    def test_delete_dialog(self):
        """اختبار نافذة الحذف"""
        # إنشاء كائن وهمي للإيراد
        class MockRevenue:
            def __init__(self):
                self.title = "إيراد تجريبي"
                self.amount = 2500.0
                self.category = "إيرادات عامة"
        
        mock_revenue = MockRevenue()
        dialog = DeleteRevenueDialog(self, mock_revenue)
        result = dialog.exec_()
        if result:
            show_revenue_advanced_info(self, "تم الحذف", "تم حذف الإيراد بنجاح!")
    
    def test_helper_warning(self):
        """اختبار دالة التحذير المساعدة"""
        show_revenue_advanced_warning(
            self, 
            "تحذير من الدالة المساعدة", 
            "هذا تحذير تم عرضه باستخدام الدالة المساعدة."
        )
    
    def test_helper_error(self):
        """اختبار دالة الخطأ المساعدة"""
        show_revenue_advanced_error(
            self, 
            "خطأ من الدالة المساعدة", 
            "هذا خطأ تم عرضه باستخدام الدالة المساعدة."
        )
    
    def test_helper_info(self):
        """اختبار دالة المعلومات المساعدة"""
        show_revenue_advanced_info(
            self, 
            "معلومات من الدالة المساعدة", 
            "هذه معلومات تم عرضها باستخدام الدالة المساعدة."
        )
    
    def test_all_dialogs(self):
        """اختبار جميع النوافذ بالتتابع"""
        QTimer.singleShot(500, self.test_warning_dialog)
        QTimer.singleShot(1500, self.test_error_dialog)
        QTimer.singleShot(2500, self.test_info_dialog)
        QTimer.singleShot(3500, lambda: show_revenue_advanced_info(
            self, "انتهى الاختبار", "تم اختبار جميع النوافذ بنجاح!"
        ))

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق تصميم عام
    app.setStyleSheet("""
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.5 #e2e8f0, stop:1 #cbd5e0);
        }
    """)
    
    window = TestRevenuesWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_tables():
    """التحقق من الجداول الموجودة في قاعدة البيانات"""
    
    db_path = 'accounting.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📋 الجداول الموجودة في قاعدة البيانات:")
        print("=" * 40)

        if not tables:
            print("❌ لا توجد جداول في قاعدة البيانات!")
            return

        for table in tables:
            table_name = table[0]
            print(f"📊 {table_name}")

            # الحصول على معلومات الأعمدة
            try:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()

                for column in columns:
                    col_name = column[1]
                    col_type = column[2]
                    print(f"   - {col_name} ({col_type})")
                print()
            except Exception as e:
                print(f"   ❌ خطأ في قراءة أعمدة الجدول: {e}")
                print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    check_tables()
